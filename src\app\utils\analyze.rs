use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 包装规则
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PackageRule {
    /// 层级编号
    pub level: u32,
    /// 层级名称
    pub name: String,
    /// 数量
    pub amount: Option<u32>,
    /// 类型
    pub r#type: Option<String>,
}

/// 层级信息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LevelInfo {
    /// 层级名称
    pub name: String,
    /// 数量
    pub amount: u32,
    /// 类型
    pub r#type: Option<String>,
    /// 层级编号
    pub level: u32,
}

/// 层级层次结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HierarchyInfo {
    /// 层级编号
    pub level: u32,
    /// 层级名称
    pub name: String,
}

/// 包装规则分析结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisResult {
    /// 各层级信息
    pub levels: HashMap<u32, LevelInfo>,
    /// 层级关系
    pub hierarchy: Vec<HierarchyInfo>,
    /// 各层级总数量关系
    pub total_quantities: HashMap<u32, u32>,
}

/// 包装规则分析工具
///
/// 用于分析产品包装规则，计算不同包装层级的数量关系
pub struct Analyzer;

impl Analyzer {
    /// 分析包装规则
    pub fn analyze(package_rules: &[PackageRule]) -> Option<AnalysisResult> {
        if package_rules.is_empty() {
            tracing::error!("[包装规则分析] 无效的包装规则数据");
            return None;
        }

        // 按level排序
        let mut sorted_rules = package_rules.to_vec();
        sorted_rules.sort_by(|a, b| a.level.cmp(&b.level));

        // 创建结果对象
        let mut result = AnalysisResult {
            levels: HashMap::new(),
            hierarchy: Vec::new(),
            total_quantities: HashMap::new(),
        };

        // 解析每个层级
        for rule in &sorted_rules {
            let level = rule.level;
            let level_name = rule.name.clone();

            result.levels.insert(
                level,
                LevelInfo {
                    name: level_name.clone(),
                    amount: rule.amount.unwrap_or(0),
                    r#type: rule.r#type.clone(),
                    level,
                },
            );

            result.hierarchy.push(HierarchyInfo {
                level,
                name: level_name,
            });
        }

        // 计算层级间的数量关系
        let mut total_quantities = HashMap::new();

        // 找到基础单位(通常是level=1的Primary)
        let base_level = sorted_rules.iter().find(|rule| rule.level == 1);

        if let Some(base_level) = base_level {
            // 以基础单位为1，计算各层级的数量关系
            total_quantities.insert(base_level.level, 1);

            // 从基础单位向上计算
            for i in 0..sorted_rules.len() {
                let current_rule = &sorted_rules[i];
                let current_level = current_rule.level;

                // 找到下一个更高层级
                for j in 0..sorted_rules.len() {
                    let higher_rule = &sorted_rules[j];
                    let higher_level = higher_rule.level;

                    if higher_level > current_level {
                        // 如果当前层级有amount，计算高层级包含的基础单位数量
                        if let Some(amount) = current_rule.amount {
                            if let Some(&total_quantity) = total_quantities.get(&current_level) {
                                total_quantities.insert(higher_level, total_quantity * amount);
                            }
                        }
                        break;
                    }
                }
            }
        } else {
            tracing::warn!("[包装规则分析] 未找到基础单位层级");
        }

        result.total_quantities = total_quantities;

        Some(result)
    }

    /// 计算指定层级包含的数量
    pub fn calc_qty(package_rules: &[PackageRule], from_level: u32, to_level: u32) -> u32 {
        if package_rules.is_empty() {
            tracing::error!("[包装规则分析] 无效的包装规则数据");
            return 0;
        }

        // 按level排序
        let mut sorted_rules = package_rules.to_vec();
        sorted_rules.sort_by(|a, b| a.level.cmp(&b.level));

        // 如果是计算同级，返回1
        if from_level == to_level {
            return 1;
        }

        // 如果是计算低级包含高级的数量，不合理，返回0
        if from_level < to_level {
            return 0;
        }

        // 找到对应层级的规则
        let from_rule = sorted_rules.iter().find(|rule| rule.level == from_level);
        let to_rule = sorted_rules.iter().find(|rule| rule.level == to_level);

        if from_rule.is_none() || to_rule.is_none() {
            tracing::error!(
                "[包装规则分析] 未找到层级 {} 或 {} 的规则",
                from_level,
                to_level
            );
            return 0;
        }

        // 如果是直接相邻的层级，直接返回amount
        if Self::is_adjacent(&sorted_rules, from_level, to_level) {
            if let Some(lower_rule) = sorted_rules.iter().find(|rule| rule.level == to_level) {
                return lower_rule.amount.unwrap_or(0);
            }
        }

        // 计算中间层级的数量关系
        let mut quantity = 1;
        let mut current_level = to_level;

        while current_level < from_level {
            // 找到当前层级
            let current_rule = sorted_rules.iter().find(|rule| rule.level == current_level);
            if current_rule.is_none() {
                break;
            }

            // 找到下一个更高层级
            let mut next_level = None;
            for rule in &sorted_rules {
                if rule.level > current_level
                    && (next_level.is_none() || rule.level < next_level.unwrap())
                {
                    next_level = Some(rule.level);
                }
            }

            if next_level.is_none() || next_level.unwrap() > from_level {
                break;
            }

            // 累乘数量
            if let Some(rule) = current_rule {
                quantity *= rule.amount.unwrap_or(1);
            }
            current_level = next_level.unwrap();
        }

        quantity
    }

    /// 检查两个层级是否相邻
    pub fn is_adjacent(sorted_rules: &[PackageRule], higher_level: u32, lower_level: u32) -> bool {
        let mut levels: Vec<u32> = sorted_rules.iter().map(|rule| rule.level).collect();
        levels.sort();

        let higher_index = levels.iter().position(|&level| level == higher_level);
        let lower_index = levels.iter().position(|&level| level == lower_level);

        if let (Some(higher_index), Some(lower_index)) = (higher_index, lower_index) {
            return higher_index as i32 - lower_index as i32 == 1;
        }

        false
    }

    /// 获取层级名称
    pub fn level_name(package_rules: &[PackageRule], level: u32) -> String {
        if package_rules.is_empty() {
            return "未知层级".to_string();
        }

        if let Some(rule) = package_rules.iter().find(|r| r.level == level) {
            rule.name.clone()
        } else {
            "未知层级".to_string()
        }
    }

    /// 获取指定层级的包装规则
    pub fn level_rule(package_rules: &[PackageRule], level: u32) -> Option<PackageRule> {
        if package_rules.is_empty() {
            return None;
        }

        package_rules.iter().find(|r| r.level == level).cloned()
    }

    /// 从响应数据中解析包装规则
    pub fn parse<T>(response_data: &T) -> Option<AnalysisResult>
    where
        T: serde::Serialize + for<'de> serde::Deserialize<'de>,
    {
        // 将响应数据转换为Value以便于访问
        let value = serde_json::to_value(response_data).ok()?;

        // 尝试从不同位置获取包装规则
        let package_rules = if let Some(data) = value.get("data").and_then(|d| d.get("data")) {
            // 从完整响应中获取
            if let Some(product) = data.get("product") {
                product.get("packageRules")
            } else if let Some(order) = data.get("order") {
                order.get("packageRules")
            } else {
                None
            }
        } else if let Some(product) = value.get("product") {
            // 直接从数据对象获取
            product.get("packageRules")
        } else if let Some(order) = value.get("order") {
            order.get("packageRules")
        } else {
            None
        };

        if let Some(rules) = package_rules {
            if let Ok(rules) = serde_json::from_value::<Vec<PackageRule>>(rules.clone()) {
                return Self::analyze(&rules);
            }
        }

        tracing::error!("[包装规则分析] 未在响应数据中找到包装规则");
        None
    }

    /// 获取包装比例
    pub fn ratio(package_rules: &[PackageRule], from_level: u32, to_level: u32) -> String {
        if package_rules.is_empty() {
            return "未知".to_string();
        }

        // 按level排序
        let mut sorted_rules = package_rules.to_vec();
        sorted_rules.sort_by(|a, b| a.level.cmp(&b.level));

        // 找到所有相关层级
        let relevant_levels: Vec<&PackageRule> = sorted_rules
            .iter()
            .filter(|rule| rule.level >= to_level && rule.level <= from_level)
            .collect();

        if relevant_levels.is_empty() {
            return "未知".to_string();
        }

        // 按level从大到小排序
        let mut relevant_levels = relevant_levels.to_vec();
        relevant_levels.sort_by(|a, b| b.level.cmp(&a.level));

        // 构建比例字符串
        let mut ratio_values = vec![1];

        // 计算后续值
        for i in 0..relevant_levels.len() - 1 {
            let current_rule = relevant_levels[i];
            let next_rule = relevant_levels[i + 1];

            // 如果有amount属性，使用它
            if let Some(amount) = next_rule.amount {
                ratio_values.push(amount);
            } else {
                // 否则尝试计算
                let ratio = Self::calc_qty(package_rules, current_rule.level, next_rule.level);
                if ratio > 0 {
                    ratio_values.push(ratio);
                } else {
                    ratio_values.push(0); // 使用0代替"?"
                }
            }
        }

        // 如果只有一个层级，返回1
        if ratio_values.len() == 1 {
            return "1".to_string();
        }

        // 返回比例字符串
        ratio_values
            .iter()
            .map(|&val| val.to_string())
            .collect::<Vec<String>>()
            .join(":")
    }

    /// 获取产品的完整包装比例
    pub fn full_ratio(package_rules: &[PackageRule]) -> String {
        if package_rules.is_empty() {
            return "未知".to_string();
        }

        // 按level排序
        let mut sorted_rules = package_rules.to_vec();
        sorted_rules.sort_by(|a, b| a.level.cmp(&b.level));

        // 找到最高和最低层级
        let lowest_level = sorted_rules[0].level;
        let highest_level = sorted_rules[sorted_rules.len() - 1].level;

        // 获取完整比例
        Self::ratio(package_rules, highest_level, lowest_level)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_analyze() {
        let package_rules = vec![
            PackageRule {
                level: 1,
                name: "Primary".to_string(),
                amount: Some(12),
                r#type: None,
            },
            PackageRule {
                level: 3,
                name: "Case".to_string(),
                amount: Some(2),
                r#type: None,
            },
            PackageRule {
                level: 4,
                name: "Pallet".to_string(),
                amount: Some(1),
                r#type: None,
            },
        ];

        let result = Analyzer::analyze(&package_rules).unwrap();

        assert_eq!(result.levels.len(), 3);
        assert_eq!(result.hierarchy.len(), 3);
        assert!(result.total_quantities.contains_key(&1));
        assert_eq!(result.total_quantities.get(&1), Some(&1));
    }

    #[test]
    fn test_calc_qty() {
        let package_rules = vec![
            PackageRule {
                level: 1,
                name: "Primary".to_string(),
                amount: Some(12),
                r#type: None,
            },
            PackageRule {
                level: 3,
                name: "Case".to_string(),
                amount: Some(2),
                r#type: None,
            },
            PackageRule {
                level: 4,
                name: "Pallet".to_string(),
                amount: Some(1),
                r#type: None,
            },
        ];

        // 测试同级
        assert_eq!(Analyzer::calc_qty(&package_rules, 3, 3), 1);

        // 测试低级包含高级（不合理）
        assert_eq!(Analyzer::calc_qty(&package_rules, 1, 3), 0);

        // 测试相邻层级
        assert_eq!(Analyzer::calc_qty(&package_rules, 3, 1), 12);
    }

    #[test]
    fn test_ratio() {
        let package_rules = vec![
            PackageRule {
                level: 1,
                name: "Primary".to_string(),
                amount: Some(12),
                r#type: None,
            },
            PackageRule {
                level: 3,
                name: "Case".to_string(),
                amount: Some(2),
                r#type: None,
            },
            PackageRule {
                level: 4,
                name: "Pallet".to_string(),
                amount: Some(1),
                r#type: None,
            },
        ];

        assert_eq!(Analyzer::ratio(&package_rules, 4, 1), "1:2:12");
    }
}
