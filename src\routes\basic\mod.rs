use crate::app::providers::DatabaseServiceProvider;
use axum::Router;
use std::sync::Arc;

mod company;
mod level;
mod line;
mod plant;
mod product;

/// 基础路由模块
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().nest(
        "/basic",
        Router::new()
            .merge(level::routes())
            .merge(company::routes())
            .merge(plant::routes())
            .merge(line::routes())
            .merge(product::routes()),
    )
}
