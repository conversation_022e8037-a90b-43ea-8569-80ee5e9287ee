use crate::app::traits::DateTime;
use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::collections::{HashMap, HashSet};
use std::sync::OnceLock;
use tiberius::Row;

/// SN码记录模型
///
/// # 主要功能
/// - 智能字段检测：根据查询自动识别和处理字段
/// - 部分字段查询：只返回实际查询的字段，实现"查询多少，返回多少"
/// - 动态字段存储：处理非标准字段，适应不同业务需求
/// - 高性能数据处理：优化大批量数据加载和处理
/// - 类型安全：提供类型安全的动态字段访问
///
/// # 用法示例
///
/// ```rust
/// // 查询和加载数据
/// let row = db.query("SELECT id, fileName FROM sn_record WHERE id = 1").await?.into_row()?;
/// let record = Record::renovation(&row);
///
/// // 访问标准属性
/// println!("文件名: {}", record.file_name);
///
/// // 使用动态字段
/// if let Some(value) = record.get_dynamic::<String>("customField") {
///     println!("自定义字段: {}", value);
/// }
///
/// // 序列化为JSON响应（只包含查询的字段）
/// let json = record.to_response();
/// ```
#[derive(Debug, Serialize, Deserialize, Default)]
pub struct Record {
    /// 主键ID
    pub id: i32,
    /// 数据ID
    #[serde(rename = "dataID")]
    pub data_id: i32,
    /// 工厂ID
    #[serde(rename = "plantId")]
    pub plant_id: i32,
    /// 标识码
    #[serde(rename = "resCode")]
    pub res_code: String,
    /// 标志位
    #[serde(rename = "filterValue")]
    pub filter_value: i8,
    /// 类型标志
    #[serde(rename = "typeFlag")]
    pub type_flag: i32,
    /// 数量
    pub amount: i32,
    /// 剩余数量
    #[serde(rename = "freeAmount")]
    pub free_amount: i32,
    /// SN文件
    #[serde(rename = "SNFile")]
    pub sn_file: String,
    /// 账户类型
    #[serde(rename = "accountType")]
    pub account_type: String,
    /// 用户ID
    pub uid: i32,
    /// 状态
    pub estate: i8,
    /// 创建时间
    #[serde(rename = "createTime")]
    pub create_time: DateTime,
    /// 更新时间
    #[serde(rename = "updateTime")]
    pub update_time: DateTime,
    /// 产码方式
    #[serde(rename = "codeStyle")]
    pub code_style: String,
    /// 动态字段存储，用于存储非标准字段
    #[serde(flatten, skip_serializing_if = "Option::is_none")]
    pub dynamic_fields: Option<HashMap<String, Value>>,
    /// 记录实际查询的字段，用于序列化时过滤
    #[serde(skip)]
    pub queried_fields: Option<HashSet<String>>,
}

impl Record {
    /// 获取标准字段集合
    ///
    /// 返回系统预定义的所有标准字段名称集合，用于区分标准字段和动态字段。
    /// 该方法使用OnceLock实现懒加载单例模式，确保字段集合只初始化一次。
    ///
    /// # 返回值
    /// 返回包含所有标准字段名称的静态HashSet引用
    #[inline]
    pub fn standard_fields() -> &'static HashSet<&'static str> {
        static FIELDS: OnceLock<HashSet<&'static str>> = OnceLock::new();

        FIELDS.get_or_init(|| {
            [
                "id",
                "dataID",
                "plantId",
                "resCode",
                "filterValue",
                "typeFlag",
                "amount",
                "freeAmount",
                "SNFile",
                "accountType",
                "uid",
                "estate",
                "createTime",
                "updateTime",
                "codeStyle",
                "RowNum",
            ]
            .into_iter()
            .collect()
        })
    }

    /// 从数据库行创建SN码记录实例
    ///
    /// 支持指定要加载的字段和动态字段，只加载需要的数据以提高性能。
    /// 记录查询的字段，用于实现"查询多少，返回多少"的原则。
    ///
    /// # 参数
    /// * `row` - 数据库查询结果行
    /// * `fields` - 可选参数，指定要转换的字段集合
    /// * `dynamic_fields` - 可选参数，指定要提取的动态字段
    ///
    /// # 返回值
    /// 返回SN码记录实例
    pub fn from_row(
        row: &Row,
        fields: Option<&HashSet<String>>,
        dynamic_fields: Option<&HashSet<String>>,
    ) -> Self {
        // 创建字段读取函数
        let should_read = |field: &str| -> bool { fields.map_or(true, |f| f.contains(field)) };

        // 构建SN码记录实例
        let mut record = Self::build_from_row(row, &should_read);

        // 处理动态字段
        if let Some(dynamic_field_set) = dynamic_fields {
            if !dynamic_field_set.is_empty() {
                record.dynamic_fields = Self::extract_dynamic_fields(row, dynamic_field_set);
            }
        }

        // 记录查询的字段
        if let Some(field_set) = fields {
            record.queried_fields = Some(field_set.clone());
        }

        record
    }

    /// 判断SN码记录是否为部分加载，用于确定是否需要补充数据
    #[inline]
    pub fn is_partial(&self) -> bool {
        self.id <= 0 || self.res_code.is_empty() || self.sn_file.is_empty()
    }

    /// 合并SN码记录数据，用于补充部分加载的SN码记录
    ///
    /// # 参数
    /// * `other` - 包含完整数据的SN码记录实例
    ///
    /// # 返回值
    /// 返回是否进行了合并操作
    pub fn merge(&mut self, other: &Self) -> bool {
        // 如果当前不是部分加载，则无需合并
        if !self.is_partial() {
            return false;
        }

        let mut merged = false;

        // 合并所有必要字段
        if self.id <= 0 && other.id > 0 {
            self.id = other.id;
            merged = true;
        }

        if self.data_id <= 0 && other.data_id > 0 {
            self.data_id = other.data_id;
            merged = true;
        }

        if self.plant_id <= 0 && other.plant_id > 0 {
            self.plant_id = other.plant_id;
            merged = true;
        }

        if self.type_flag <= 0 && other.type_flag > 0 {
            self.type_flag = other.type_flag;
            merged = true;
        }

        if self.amount <= 0 && other.amount > 0 {
            self.amount = other.amount;
            merged = true;
        }

        if self.free_amount <= 0 && other.free_amount > 0 {
            self.free_amount = other.free_amount;
            merged = true;
        }

        if self.uid <= 0 && other.uid > 0 {
            self.uid = other.uid;
            merged = true;
        }

        Self::merge_string_field(&mut self.res_code, &other.res_code, &mut merged);
        Self::merge_string_field(&mut self.sn_file, &other.sn_file, &mut merged);
        Self::merge_string_field(&mut self.account_type, &other.account_type, &mut merged);
        Self::merge_string_field(&mut self.code_style, &other.code_style, &mut merged);

        // 合并状态字段
        if self.filter_value == 0 && other.filter_value != 0 {
            self.filter_value = other.filter_value;
            merged = true;
        }

        if self.estate == 0 && other.estate != 0 {
            self.estate = other.estate;
            merged = true;
        }

        // 合并动态字段
        if let Some(other_fields) = &other.dynamic_fields {
            if self.dynamic_fields.is_none() {
                self.dynamic_fields = Some(other_fields.clone());
                merged = true;
            } else if let Some(self_fields) = &mut self.dynamic_fields {
                for (key, value) in other_fields {
                    if !self_fields.contains_key(key) {
                        self_fields.insert(key.clone(), value.clone());
                        merged = true;
                    }
                }
            }
        }

        merged
    }

    /// 获取动态字段值
    ///
    /// 将动态字段值转换为指定类型并返回。
    #[inline]
    pub fn get_dynamic<T>(&self, field: &str) -> Option<T>
    where
        T: serde::de::DeserializeOwned,
    {
        self.dynamic_fields
            .as_ref()
            .and_then(|fields| fields.get(field))
            .and_then(|v| serde_json::from_value(v.clone()).ok())
    }

    /// 设置动态字段值
    #[inline]
    pub fn set_dynamic<T>(&mut self, field: &str, value: T) -> Result<(), serde_json::Error>
    where
        T: Serialize,
    {
        let json_value = serde_json::to_value(value)?;

        if self.dynamic_fields.is_none() {
            self.dynamic_fields = Some(HashMap::new());
        }

        if let Some(ref mut fields) = self.dynamic_fields {
            fields.insert(field.to_string(), json_value);
        }

        Ok(())
    }

    /// 智能转换数据库结果为SN码记录实例
    ///
    /// 自动分析查询结果包含的字段，只返回查询的字段。
    /// 是数据加载的主要入口点，实现"查询多少，返回多少"的需求。
    ///
    /// # 类型参数
    /// * `T` - 实现了DataConversion特征的数据类型
    ///
    /// # 参数
    /// * `data` - 数据库查询结果行或行的集合
    ///
    /// # 返回值
    /// * SN码记录结构体或结构体集合
    #[inline]
    pub fn renovation<T>(data: &T) -> T::Output
    where
        T: DataConversion,
    {
        data.convert()
    }

    /// 将SN码记录对象序列化为JSON响应
    ///
    /// 根据查询的字段自动过滤，只返回实际查询的字段。
    /// 实现"查询多少，返回多少"的原则，避免返回未查询的字段。
    ///
    /// # 返回值
    /// 返回只包含实际查询字段的JSON对象
    pub fn to_response(&self) -> serde_json::Value {
        // 获取原始序列化结果
        let json = serde_json::to_value(self).unwrap_or(serde_json::Value::Null);

        if let serde_json::Value::Object(obj) = json {
            let filtered: serde_json::Map<String, serde_json::Value> = match &self.queried_fields {
                // 如果有记录查询字段，只返回这些字段
                Some(queried) => obj
                    .into_iter()
                    .filter(|(key, _value)| {
                        // 转换字段名以匹配查询字段名
                        let field_name = if key.contains('_') {
                            // 转换蛇形命名为驼峰命名，匹配SQL查询字段名
                            let parts: Vec<&str> = key.split('_').collect();
                            let mut camel_case = parts[0].to_string();
                            for part in &parts[1..] {
                                camel_case.push_str(&part[0..1].to_uppercase());
                                camel_case.push_str(&part[1..]);
                            }
                            camel_case
                        } else {
                            key.clone()
                        };

                        // 只保留查询的字段，不论值是什么
                        queried.contains(&field_name)
                    })
                    .collect(),
                // 如果没有记录查询字段，过滤掉null和默认值
                None => obj
                    .into_iter()
                    .filter(|(key, value)| {
                        // 排除null值
                        if value.is_null() {
                            return false;
                        }

                        // 排除空字符串
                        if let serde_json::Value::String(s) = value {
                            if s.is_empty() {
                                return false;
                            }
                        }

                        // 排除0值（对于可选字段）
                        if let serde_json::Value::Number(n) = value {
                            if let Some(i) = n.as_i64() {
                                if i == 0 && !["id", "estate", "amount"].contains(&key.as_str()) {
                                    return false;
                                }
                            }
                        }

                        true
                    })
                    .collect(),
            };

            serde_json::Value::Object(filtered)
        } else {
            json
        }
    }

    // 私有辅助方法

    /// 从行数据构建SN码记录实例
    ///
    /// 根据指定的字段筛选条件，从数据库行中提取数据并构建SN码记录实例。
    /// 此方法会处理各种类型转换和数据格式问题，确保安全地读取数据。
    ///
    /// # 参数
    /// * `row` - 数据库查询结果行
    /// * `should_read` - 函数指针，判断是否应该读取特定字段
    ///
    /// # 返回值
    /// 返回构建的SN码记录实例
    #[inline]
    fn build_from_row(row: &Row, should_read: &impl Fn(&str) -> bool) -> Self {
        // 定义读取函数
        let get_i32 = |field: &str, default: i32| -> i32 {
            if !should_read(field) {
                return default;
            }
            match row.get::<i32, _>(field) {
                Some(value) => value,
                None => {
                    if let Some(str_val) = row.get::<&str, _>(field) {
                        str_val.parse::<i32>().unwrap_or(default)
                    } else {
                        default
                    }
                }
            }
        };

        let get_i8 = |field: &str, default: i8| -> i8 {
            if !should_read(field) {
                return default;
            }
            if let Some(u8_val) = row.get::<u8, _>(field) {
                u8_val as i8
            } else if let Some(str_val) = row.get::<&str, _>(field) {
                str_val.parse::<i8>().unwrap_or(default)
            } else {
                default
            }
        };

        let get_string = |field: &str| -> String {
            if !should_read(field) {
                return String::new();
            }
            row.get::<&str, _>(field).unwrap_or_default().to_string()
        };

        let get_datetime = |field: &str| -> DateTime {
            if !should_read(field) {
                return DateTime(NaiveDateTime::default());
            }
            match row.get::<&str, _>(field) {
                Some(s) => {
                    // 尝试解析日期时间，如果失败则返回默认值
                    match DateTime::parse(s, "%Y-%m-%d %H:%M:%S") {
                        Some(dt) => dt,
                        None => DateTime(NaiveDateTime::default()),
                    }
                }
                None => DateTime(NaiveDateTime::default()),
            }
        };

        // 构建SN码记录实例
        Self {
            id: get_i32("id", 0),
            data_id: get_i32("dataID", 0),
            plant_id: get_i32("plantId", 0),
            res_code: get_string("resCode"),
            filter_value: get_i8("filterValue", 0),
            type_flag: get_i32("typeFlag", 0),
            amount: get_i32("amount", 0),
            free_amount: get_i32("freeAmount", 0),
            sn_file: get_string("SNFile"),
            account_type: get_string("accountType"),
            uid: get_i32("uid", 0),
            estate: get_i8("estate", 0),
            create_time: get_datetime("createTime"),
            update_time: get_datetime("updateTime"),
            code_style: get_string("codeStyle"),
            dynamic_fields: None,
            queried_fields: None,
        }
    }

    /// 提取动态字段
    #[inline]
    fn extract_dynamic_fields(
        row: &Row,
        fields: &HashSet<String>,
    ) -> Option<HashMap<String, Value>> {
        let mut result = HashMap::new();
        let standard_fields = Self::standard_fields();

        for field in fields {
            // 只处理非标准字段
            if !standard_fields.contains(field.as_str()) {
                if let Some(value) = Self::extract_field_value(row, field) {
                    result.insert(field.clone(), value);
                }
            }
        }

        if result.is_empty() {
            None
        } else {
            Some(result)
        }
    }

    /// 提取字段值
    #[inline]
    fn extract_field_value(row: &Row, field: &str) -> Option<Value> {
        // 尝试不同类型的提取
        if let Some(val) = row.get::<i32, _>(field) {
            return Some(Value::from(val));
        }

        if let Some(val) = row.get::<u8, _>(field) {
            return Some(Value::from(val));
        }

        if let Some(val) = row.get::<&str, _>(field) {
            // 尝试将字符串解析为数字
            if let Ok(num) = val.parse::<i32>() {
                return Some(Value::from(num));
            }

            if let Ok(num) = val.parse::<u8>() {
                return Some(Value::from(num));
            }

            // 尝试解析为JSON
            if let Ok(json) = serde_json::from_str::<Value>(val) {
                return Some(json);
            }

            // 尝试解析为日期时间
            if let Some(dt) = DateTime::parse(val, "%Y-%m-%d %H:%M:%S") {
                return Some(Value::String(dt.0.to_string()));
            }

            // 作为普通字符串处理
            return Some(Value::String(val.to_string()));
        }

        None
    }

    /// 合并字符串字段，避免重复代码
    #[inline]
    fn merge_string_field(target: &mut String, source: &str, merged: &mut bool) {
        if target.is_empty() && !source.is_empty() {
            *target = source.to_string();
            *merged = true;
        }
    }
}

/// 数据转换特征
///
/// 为SN码记录模型提供统一的数据转换接口，使单行查询和批量查询
/// 可以使用相同的处理逻辑，简化代码并提高一致性。
pub trait DataConversion {
    /// 转换后的输出类型
    type Output;

    /// 执行数据转换
    fn convert(&self) -> Self::Output;

    /// 获取当前行对象的引用
    fn row(&self) -> Option<&Row>;
}

/// 单行数据转换实现
///
/// 将单个数据库行转换为SN码记录实例，只加载查询到的字段。
/// 记录查询的字段，用于实现"查询多少，返回多少"的原则。
impl DataConversion for Row {
    type Output = Record;

    fn convert(&self) -> Self::Output {
        // 获取行中所有可用的列名
        let available_columns: HashSet<_> = self
            .columns()
            .iter()
            .map(|c| c.name().to_string())
            .collect();

        // 只读取存在于查询结果中的字段
        let fields = Some(&available_columns);

        // 获取所有可能的动态字段
        let mut dynamic_fields = HashSet::new();
        let standard_fields = Record::standard_fields();

        for field in &available_columns {
            if !standard_fields.contains(field.as_str()) {
                dynamic_fields.insert(field.clone());
            }
        }

        // 使用from_row方法创建SN码记录实例，只填充查询到的字段
        let mut record = Record::from_row(
            self,
            fields,
            if dynamic_fields.is_empty() {
                None
            } else {
                Some(&dynamic_fields)
            },
        );

        // 确保queried_fields被设置为可用列名
        record.queried_fields = Some(available_columns);

        record
    }

    fn row(&self) -> Option<&Row> {
        Some(self)
    }
}

/// 批量数据转换实现
///
/// 将多个数据库行转换为SN码记录实例集合，用于批量处理查询结果。
impl DataConversion for [Row] {
    type Output = Vec<Record>;

    fn convert(&self) -> Self::Output {
        self.iter().map(|row| row.convert()).collect()
    }

    fn row(&self) -> Option<&Row> {
        None
    }
}
