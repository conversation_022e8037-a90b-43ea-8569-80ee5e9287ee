# Socket配置示例文件
# 复制此文件为 .env 并根据需要修改配置

# Socket服务器配置
SOCKET_HOST=127.0.0.1
SOCKET_PORT=8080

# 超时配置（毫秒）
# 连接超时 - 建立TCP连接的最大时间
SOCKET_CONNECT_TIMEOUT_MS=5000

# 读取超时 - 从服务器读取响应的最大时间
SOCKET_READ_TIMEOUT_MS=10000

# 写入超时 - 向服务器发送数据的最大时间
SOCKET_WRITE_TIMEOUT_MS=5000

# 总操作超时 - 整个发送+接收操作的最大时间
SOCKET_OPERATION_TIMEOUT_MS=30000

# 重连配置
# 重连间隔（毫秒）
SOCKET_RECONNECT_INTERVAL_MS=3000

# 最大重连次数
SOCKET_MAX_RECONNECT_ATTEMPTS=10

# 心跳间隔（毫秒）
SOCKET_HEARTBEAT_INTERVAL_MS=30000

# 缓冲区配置
# 读取缓冲区大小（字节）
SOCKET_READ_BUFFER_SIZE=8192

# 写入缓冲区大小（字节）
SOCKET_WRITE_BUFFER_SIZE=8192

# 不同网络环境的推荐配置：

# 本地网络（低延迟）:
# SOCKET_CONNECT_TIMEOUT_MS=1000
# SOCKET_READ_TIMEOUT_MS=3000
# SOCKET_WRITE_TIMEOUT_MS=1000
# SOCKET_OPERATION_TIMEOUT_MS=8000

# 内网服务（中等延迟）:
# SOCKET_CONNECT_TIMEOUT_MS=3000
# SOCKET_READ_TIMEOUT_MS=10000
# SOCKET_WRITE_TIMEOUT_MS=3000
# SOCKET_OPERATION_TIMEOUT_MS=20000

# 外网服务（高延迟）:
# SOCKET_CONNECT_TIMEOUT_MS=10000
# SOCKET_READ_TIMEOUT_MS=30000
# SOCKET_WRITE_TIMEOUT_MS=10000
# SOCKET_OPERATION_TIMEOUT_MS=60000

# 批量操作（大量数据）:
# SOCKET_CONNECT_TIMEOUT_MS=5000
# SOCKET_READ_TIMEOUT_MS=60000
# SOCKET_WRITE_TIMEOUT_MS=10000
# SOCKET_OPERATION_TIMEOUT_MS=120000
