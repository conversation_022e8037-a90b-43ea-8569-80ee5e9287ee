use serde::Deserialize;
use std::env;

/// Socket配置结构体
#[derive(Debug, <PERSON><PERSON>, Deserialize)]
pub struct SocketConfig {
    /// Socket服务器地址
    pub host: String,
    /// Socket服务器端口
    pub port: u16,
    /// 连接超时时间（秒）
    pub timeout: u64,
    /// 重连间隔（秒）
    pub reconnect_interval: u64,
    /// 最大重连次数
    pub max_reconnect_attempts: u32,
    /// 心跳间隔（秒）
    pub heartbeat_interval: u64,
    /// 读取缓冲区大小
    pub read_buffer_size: usize,
    /// 写入缓冲区大小
    pub write_buffer_size: usize,
}

impl SocketConfig {
    /// 从环境变量加载Socket配置
    pub fn from_env() -> Self {
        Self {
            host: env::var("SOCKET_HOST").unwrap_or_else(|_| "127.0.0.1".to_string()),
            port: env::var("SOCKET_PORT")
                .unwrap_or_else(|_| "8080".to_string())
                .parse()
                .unwrap_or(8080),
            timeout: env::var("SOCKET_TIMEOUT")
                .unwrap_or_else(|_| "5".to_string())
                .parse()
                .unwrap_or(5),
            reconnect_interval: env::var("SOCKET_RECONNECT_INTERVAL")
                .unwrap_or_else(|_| "3".to_string())
                .parse()
                .unwrap_or(3),
            max_reconnect_attempts: env::var("SOCKET_MAX_RECONNECT_ATTEMPTS")
                .unwrap_or_else(|_| "10".to_string())
                .parse()
                .unwrap_or(10),
            heartbeat_interval: env::var("SOCKET_HEARTBEAT_INTERVAL")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .unwrap_or(30),
            read_buffer_size: env::var("SOCKET_READ_BUFFER_SIZE")
                .unwrap_or_else(|_| "8192".to_string())
                .parse()
                .unwrap_or(8192),
            write_buffer_size: env::var("SOCKET_WRITE_BUFFER_SIZE")
                .unwrap_or_else(|_| "8192".to_string())
                .parse()
                .unwrap_or(8192),
        }
    }

    /// 获取Socket连接字符串
    pub fn connection_string(&self) -> String {
        format!("{}:{}", self.host, self.port)
    }
}
