use crate::app::http::controllers::nucleus::IndustryController;
use crate::app::providers::DatabaseServiceProvider;
use axum::{routing::post, Router};
use std::sync::Arc;

/// PDA业务核心管理路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().merge(
        Router::new()
            .route("/industry/sampling", post(IndustryController::sampling))
            .route("/industry/unpack", post(IndustryController::unpack))
            .route("/industry/merge", post(IndustryController::merge))
            .route("/industry/unbind", post(IndustryController::unbind))
            .route("/industry/aggregate", post(IndustryController::aggregate))
            .route("/industry/extract", post(IndustryController::extract))
            .route("/industry/replace", post(IndustryController::replace))
            .route("/industry/reprint", post(IndustryController::reprint)),
    )
}
