use crate::framework::validation::traits::ValidationRule;

/// 比较操作符
pub enum CompareOperator {
    /// 等于
    Equal,
    /// 不等于
    NotEqual,
    /// 大于
    GreaterThan,
    /// 大于等于
    GreaterThanOrEqual,
    /// 小于
    LessThan,
    /// 小于等于
    LessThanOrEqual,
}

impl CompareOperator {
    fn compare<T: PartialOrd>(&self, left: &T, right: &T) -> bool {
        match self {
            Self::Equal => left == right,
            Self::NotEqual => left != right,
            Self::GreaterThan => left > right,
            Self::GreaterThanOrEqual => left >= right,
            Self::LessThan => left < right,
            Self::LessThanOrEqual => left <= right,
        }
    }

    fn as_str(&self) -> &'static str {
        match self {
            Self::Equal => "等于",
            Self::NotEqual => "不等于",
            Self::GreaterThan => "大于",
            Self::GreaterThanOrEqual => "大于等于",
            Self::LessThan => "小于",
            Self::LessThanOrEqual => "小于等于",
        }
    }
}

/// 数值比较验证规则
pub struct NumberCompare {
    operator: CompareOperator,
    target: f64,
    message_template: String,
}

impl NumberCompare {
    pub fn new(operator: CompareOperator, target: f64) -> Self {
        let message = format!("必须{}值 {}", operator.as_str(), target);
        Self {
            operator,
            target,
            message_template: message,
        }
    }

    pub fn with_message(mut self, message: &str) -> Self {
        self.message_template = message.to_string();
        self
    }
}

impl ValidationRule for NumberCompare {
    fn name(&self) -> &'static str {
        "number_compare"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        if let Ok(num) = value.parse::<f64>() {
            self.operator.compare(&num, &self.target)
        } else {
            false
        }
    }

    fn message(&self) -> &str {
        &self.message_template
    }
}

/// 日期比较验证规则
pub struct DateCompare {
    operator: CompareOperator,
    target: String,
    format: String,
    message_template: String,
}

impl DateCompare {
    pub fn new(operator: CompareOperator, target: &str, format: &str) -> Self {
        let message = format!("日期必须{}值 {}", operator.as_str(), target);
        Self {
            operator,
            target: target.to_string(),
            format: format.to_string(),
            message_template: message,
        }
    }

    pub fn with_message(mut self, message: &str) -> Self {
        self.message_template = message.to_string();
        self
    }
}

impl ValidationRule for DateCompare {
    fn name(&self) -> &'static str {
        "date_compare"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        use chrono::NaiveDate;

        if let (Ok(date), Ok(target)) = (
            NaiveDate::parse_from_str(value, &self.format),
            NaiveDate::parse_from_str(&self.target, &self.format),
        ) {
            match self.operator {
                CompareOperator::Equal => date == target,
                CompareOperator::NotEqual => date != target,
                CompareOperator::GreaterThan => date > target,
                CompareOperator::GreaterThanOrEqual => date >= target,
                CompareOperator::LessThan => date < target,
                CompareOperator::LessThanOrEqual => date <= target,
            }
        } else {
            false
        }
    }

    fn message(&self) -> &str {
        &self.message_template
    }
}
