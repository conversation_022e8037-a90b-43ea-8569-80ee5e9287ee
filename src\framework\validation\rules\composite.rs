use crate::framework::validation::traits::ValidationRule;

/// 组合操作符
pub enum CompositeOperator {
    /// 与操作：所有规则都必须通过
    And,
    /// 或操作：任一规则通过即可
    Or,
}

/// 组合验证规则
pub struct CompositeRule {
    rules: Vec<Box<dyn ValidationRule>>,
    operator: CompositeOperator,
    message_text: String,
}

impl CompositeRule {
    pub fn new(
        rules: Vec<Box<dyn ValidationRule>>,
        operator: CompositeOperator,
        message: &str,
    ) -> Self {
        Self {
            rules,
            operator,
            message_text: message.to_string(),
        }
    }
}

impl ValidationRule for CompositeRule {
    fn name(&self) -> &'static str {
        "composite"
    }

    fn validate(&self, value: &str, params: &[&str]) -> bool {
        match self.operator {
            CompositeOperator::And => self.rules.iter().all(|rule| rule.validate(value, params)),
            CompositeOperator::Or => self.rules.iter().any(|rule| rule.validate(value, params)),
        }
    }

    fn message(&self) -> &str {
        &self.message_text
    }
}

/// 条件验证规则
pub struct ConditionalRule {
    condition: Box<dyn Fn(&str) -> bool>,
    rule: Box<dyn ValidationRule>,
}

impl ConditionalRule {
    pub fn new<F>(condition: F, rule: Box<dyn ValidationRule>) -> Self
    where
        F: Fn(&str) -> bool + 'static,
    {
        Self {
            condition: Box::new(condition),
            rule,
        }
    }
}

impl ValidationRule for ConditionalRule {
    fn name(&self) -> &'static str {
        "conditional"
    }

    fn validate(&self, value: &str, params: &[&str]) -> bool {
        if (self.condition)(value) {
            self.rule.validate(value, params)
        } else {
            true
        }
    }

    fn message(&self) -> &str {
        self.rule.message()
    }
}
