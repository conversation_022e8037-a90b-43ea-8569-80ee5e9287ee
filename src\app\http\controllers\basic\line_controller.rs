use axum::response::IntoResponse;

/// 产线管理控制器
pub struct LineController;

impl LineController {
    /// 分页列表
    pub async fn page() -> impl IntoResponse {
        "page"
    }

    /// 列表
    pub async fn index() -> impl IntoResponse {
        "index"
    }

    /// 新增
    pub async fn store() -> impl IntoResponse {
        "store"
    }

    /// 修改
    pub async fn update() -> impl IntoResponse {
        "update"
    }

    /// 删除
    pub async fn destroy() -> impl IntoResponse {
        "destroy"
    }
}
