use crate::app::providers::DatabaseServiceProvider;
use crate::app::services::pivotal::print_service::PrintService;
use crate::app::traits::response::track;
use axum::{
    body::Body,
    extract::{J<PERSON>, Query, State},
    http::{header, StatusCode},
    response::{IntoResponse, Response},
};
use serde::Deserialize;
use std::path::Path;
use std::sync::Arc;
use std::time::Instant;
use tokio::fs::File;
use tokio_util::io::ReaderStream;
use tracing::{error, info, warn};

/// 检查更新请求参数
#[derive(Debug, serde::Deserialize)]
pub struct RiseParams {
    /// 操作类型：check 检查更新，download 下载应用
    pub action: Option<String>,
    /// 平台：android, ios, windows
    pub platform: Option<String>,
    /// 应用ID
    pub appid: Option<String>,
}

/// 打印请求参数
#[derive(Debug, Deserialize)]
pub struct PrintParams {
    /// 序列码
    pub code: String,
    /// 打印数量
    pub quantity: Option<u32>,
}

/// 对接管理控制器
pub struct DockingController;

impl DockingController {
    /// 网络检测
    pub async fn network(State(db): State<Arc<DatabaseServiceProvider>>) -> impl IntoResponse {
        let start = Instant::now();

        // 检查数据库连接
        match db.get_connection().await {
            Ok(_) => {
                let latency = start.elapsed().as_millis();
                info!("[网络检测] 网络连接完成，耗时: {}ms", latency);
                track()
                    .victory()
                    .message("网络连接正常")
                    .data(serde_json::json!({
                        "latency": format!("{}ms", latency)
                    }))
                    .build()
            }
            Err(_) => {
                warn!("[网络检测] 网络连接失败");
                track().defeat().message("网络连接异常").build()
            }
        }
    }

    /// 数据同步
    pub async fn sync(State(db): State<Arc<DatabaseServiceProvider>>) -> impl IntoResponse {
        info!("[数据同步] 开始获取各表数据条数");

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[数据同步] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[数据同步] 获取数据库连接失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 查询 basic_product 表数据条数
        let product_count = match conn
            .query(
                "SELECT COUNT(*) AS count FROM basic_product WITH (NOLOCK)",
                &[],
            )
            .await
        {
            Ok(stream) => match stream.into_first_result().await {
                Ok(rows) => {
                    if !rows.is_empty() {
                        rows[0].get::<i32, _>("count").unwrap_or(0)
                    } else {
                        0
                    }
                }
                Err(e) => {
                    error!("[数据同步] 查询产品数据条数失败: {}", e);
                    return track()
                        .defeat()
                        .message(format!("查询产品数据条数失败: {}", e))
                        .build();
                }
            },
            Err(e) => {
                error!("[数据同步] 执行产品查询失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("执行产品查询失败: {}", e))
                    .build();
            }
        };

        // 查询 produce_order 表数据条数
        let batch_count = match conn
            .query(
                "SELECT COUNT(*) AS count FROM produce_order WITH (NOLOCK)",
                &[],
            )
            .await
        {
            Ok(stream) => match stream.into_first_result().await {
                Ok(rows) => {
                    if !rows.is_empty() {
                        rows[0].get::<i32, _>("count").unwrap_or(0)
                    } else {
                        0
                    }
                }
                Err(e) => {
                    error!("[数据同步] 查询订单数据条数失败: {}", e);
                    return track()
                        .defeat()
                        .message(format!("查询订单数据条数失败: {}", e))
                        .build();
                }
            },
            Err(e) => {
                error!("[数据同步] 执行批次查询失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("执行批次查询失败: {}", e))
                    .build();
            }
        };

        // 查询 ware_order 表数据条数
        let order_count = match conn
            .query(
                "SELECT COUNT(*) AS count FROM ware_order WITH (NOLOCK)",
                &[],
            )
            .await
        {
            Ok(stream) => match stream.into_first_result().await {
                Ok(rows) => {
                    if !rows.is_empty() {
                        rows[0].get::<i32, _>("count").unwrap_or(0)
                    } else {
                        0
                    }
                }
                Err(e) => {
                    error!("[数据同步] 查询订单数据条数失败: {}", e);
                    return track()
                        .defeat()
                        .message(format!("查询订单数据条数失败: {}", e))
                        .build();
                }
            },
            Err(e) => {
                error!("[数据同步] 执行订单查询失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("执行订单查询失败: {}", e))
                    .build();
            }
        };

        // 查询 produce_repacking 表数据条数
        let repacking_count = match conn
            .query(
                "SELECT COUNT(*) AS count FROM produce_repacking WITH (NOLOCK)",
                &[],
            )
            .await
        {
            Ok(stream) => match stream.into_first_result().await {
                Ok(rows) => {
                    if !rows.is_empty() {
                        rows[0].get::<i32, _>("count").unwrap_or(0)
                    } else {
                        0
                    }
                }
                Err(e) => {
                    error!("[数据同步] 查询返工数据条数失败: {}", e);
                    return track()
                        .defeat()
                        .message(format!("查询返工数据条数失败: {}", e))
                        .build();
                }
            },
            Err(e) => {
                error!("[数据同步] 执行返工查询失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("执行返工查询失败: {}", e))
                    .build();
            }
        };

        // 返回所有表的数据条数
        info!(
            "[数据同步] 查询成功，产品:{}, 批次:{}, 订单:{}, 返工:{}",
            product_count, batch_count, order_count, repacking_count
        );

        track()
            .victory()
            .message("数据同步成功")
            .data(serde_json::json!({
                "product": product_count,
                "batch": batch_count,
                "order": order_count,
                "repacking": repacking_count
            }))
            .build()
    }

    /// 检查更新
    pub async fn rise(
        State(_db): State<Arc<DatabaseServiceProvider>>,
        Query(params): axum::extract::Query<RiseParams>,
    ) -> Response {
        info!(
            "[检查更新] 接收到更新请求: action={:?}, platform={:?}, appid={:?}",
            params.action, params.platform, params.appid
        );

        // 验证必要参数
        if params.platform.is_none() || params.appid.is_none() {
            warn!("[检查更新] 缺少必要参数");
            return track()
                .defeat()
                .message("缺少必要参数")
                .build()
                .into_response();
        }

        let platform = params.platform.unwrap();
        let appid = params.appid.unwrap();

        match params.action.as_deref() {
            Some("check") => {
                // 检查更新逻辑
                info!("[检查更新] 检查 {} 平台 {} 应用的更新", platform, appid);

                // 暂时仅支持安卓平台
                if platform != "android" {
                    info!("[检查更新] 暂不支持的平台: {}", platform);
                    return track()
                        .defeat()
                        .message("暂时仅支持安卓平台")
                        .build()
                        .into_response();
                }

                // 前端传递的appid是固定值：com.huahai.pda
                let app_info = if appid == "com.huahai.pda" {
                    serde_json::json!({
                        "description": [
                            "修复已知问题",
                            "优化用户体验",
                            "提升性能"
                        ],
                        "downloadUrl": "/api/docking/rise?action=download&platform=android&appid=com.huahai.pda",
                        "upgrade": false // 手动控制是否需要更新
                    })
                } else {
                    info!("[检查更新] 未找到应用信息: {}", appid);
                    return track()
                        .defeat()
                        .message("未找到应用信息")
                        .build()
                        .into_response();
                };

                info!("[检查更新] 返回应用信息: {:?}", app_info);

                track()
                    .victory()
                    .message("获取更新信息成功")
                    .data(app_info)
                    .build()
                    .into_response()
            }
            Some("download") => {
                // 下载应用逻辑
                info!("[检查更新] 请求下载 {} 平台 {} 应用", platform, appid);

                // 暂时仅支持安卓平台
                if platform != "android" {
                    info!("[检查更新] 暂不支持的平台: {}", platform);
                    return track()
                        .defeat()
                        .message("暂时仅支持安卓平台")
                        .build()
                        .into_response();
                }

                // 前端传递的appid是固定值：com.huahai.pda
                if appid != "com.huahai.pda" {
                    info!("[检查更新] 未找到应用包信息: {}", appid);
                    return track()
                        .defeat()
                        .message("未找到应用包信息")
                        .build()
                        .into_response();
                }

                // APK文件路径（实际项目中应根据环境配置）
                // 确保目录存在
                let dir_path = "src/storage/apk";
                if !Path::new(dir_path).exists() {
                    // 创建目录
                    if let Err(e) = std::fs::create_dir_all(dir_path) {
                        error!("[检查更新] 创建目录失败: {}", e);
                        return track()
                            .defeat()
                            .message(format!("创建目录失败: {}", e))
                            .build()
                            .into_response();
                    }
                    info!("[检查更新] 创建目录: {}", dir_path);
                }

                let file_path = "src/storage/apk/huahai-pda-latest.apk";
                let file_name = "huahai-pda-latest.apk";

                // 检查文件是否存在
                if !Path::new(file_path).exists() {
                    error!("[检查更新] APK文件不存在: {}", file_path);

                    // 在实际环境中，这里可能需要创建一个示例APK文件或从其他位置复制
                    // 现在我们返回一个错误响应
                    return track()
                        .defeat()
                        .message("应用安装包暂未上传，请联系管理员")
                        .build()
                        .into_response();
                }

                info!("[检查更新] 开始下载APK文件: {}", file_path);

                // 异步读取文件并创建流
                match File::open(file_path).await {
                    Ok(file) => {
                        // 创建文件流
                        let stream = ReaderStream::new(file);
                        let body = Body::from_stream(stream);

                        let content_disposition = format!("attachment; filename=\"{}\"", file_name);

                        info!("[检查更新] APK文件流创建成功，准备下载");

                        // 返回文件流响应
                        Response::builder()
                            .status(StatusCode::OK)
                            .header(
                                header::CONTENT_TYPE,
                                "application/vnd.android.package-archive",
                            )
                            .header(header::CONTENT_DISPOSITION, &content_disposition)
                            .body(body)
                            .unwrap()
                    }
                    Err(e) => {
                        error!("[检查更新] 打开APK文件失败: {}", e);
                        track()
                            .defeat()
                            .message(format!("打开应用安装包失败: {}", e))
                            .build()
                            .into_response()
                    }
                }
            }
            _ => {
                warn!("[检查更新] 无效的操作类型: {:?}", params.action);
                track()
                    .defeat()
                    .message("无效的操作类型")
                    .build()
                    .into_response()
            }
        }
    }

    /// 序列码打印
    pub async fn print(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Json(params): Json<PrintParams>,
    ) -> impl IntoResponse {
        info!(
            "[序列码打印] 接收到参数: code={}, quantity={:?}",
            params.code, params.quantity
        );

        // 验证参数
        if params.code.is_empty() {
            error!("[序列码打印] 参数错误: 缺少序列码");
            return track().defeat().message("请传入序列码").build();
        }

        // 获取打印数量，默认为1
        let quantity = params.quantity.unwrap_or(1);
        if quantity == 0 {
            error!("[序列码打印] 参数错误: 打印数量不能为0");
            return track().defeat().message("打印数量不能为0").build();
        }

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[序列码打印] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[序列码打印] 数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 使用打印服务获取打印数据
        match PrintService::get_print_data_by_code(&mut conn, &params.code).await {
            Ok(print_data) => {
                info!("[序列码打印] 获取打印数据成功");

                // 执行打印操作
                // 循环打印指定数量
                let mut success_count = 0;
                let mut error_message = String::new();

                for i in 0..quantity {
                    info!("[序列码打印] 开始第{}次打印", i + 1);
                    match PrintService::execute_print(&print_data).await {
                        Ok(response) => {
                            info!("[序列码打印] 第{}次打印成功: {}", i + 1, response);
                            success_count += 1;
                        }
                        Err(e) => {
                            error!("[序列码打印] 第{}次打印失败: {}", i + 1, e);
                            error_message = e;
                            break;
                        }
                    }
                }

                // 返回打印结果
                if success_count == quantity {
                    info!("[序列码打印] 全部{}份打印成功", quantity);
                    track()
                        .victory()
                        .message(format!("{}份标签打印成功", quantity))
                        .build()
                } else if success_count > 0 {
                    warn!("[序列码打印] 部分打印成功: {}/{}", success_count, quantity);
                    track()
                        .victory()
                        .message(format!(
                            "部分打印成功: {}/{}，失败原因: {}",
                            success_count, quantity, error_message
                        ))
                        .build()
                } else {
                    error!("[序列码打印] 打印失败: {}", error_message);
                    track()
                        .defeat()
                        .message(format!("打印失败: {}", error_message))
                        .build()
                }
            }
            Err(e) => {
                error!("[序列码打印] 获取打印数据失败: {}", e);
                track()
                    .defeat()
                    .message(format!("获取打印数据失败: {}", e))
                    .build()
            }
        }
    }
}
