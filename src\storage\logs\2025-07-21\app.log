2025-07-21 06:50:17.255  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 06:50:17.256  INFO 数据库连接管理器创建成功
2025-07-21 06:50:17.289  INFO 数据库连接池创建成功
2025-07-21 06:50:17.294  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 06:50:20.320  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 06:50:20.322  INFO 数据库连接管理器创建成功
2025-07-21 06:50:20.369  INFO 数据库连接池创建成功
2025-07-21 06:50:20.376  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 06:57:56.198  INFO [查询序列码] 开始查询序列码信息
2025-07-21 06:57:56.239  INFO [查询序列码] 数据库连接获取成功
2025-07-21 06:57:56.243  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 06:57:56.246  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 06:57:56.246  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 06:57:56.362  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 06:57:56.364  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 06:57:59.474  INFO [查询序列码] 开始查询序列码信息
2025-07-21 06:57:59.484  INFO [查询序列码] 数据库连接获取成功
2025-07-21 06:57:59.487  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 06:57:59.490  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 06:57:59.490  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 06:57:59.625  INFO [查询序列码] 托盘内箱码数量: 2
2025-07-21 06:58:00.183  INFO [查询序列码] 托盘内瓶码数量: 24
2025-07-21 06:58:00.184  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 06:58:03.035  INFO [查询序列码] 开始查询序列码信息
2025-07-21 06:58:03.044  INFO [查询序列码] 数据库连接获取成功
2025-07-21 06:58:03.046  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 06:58:03.048  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 06:58:03.049  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 06:58:03.135  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 06:58:03.136  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:00:38.977  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:00:38.990  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:00:38.992  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:00:38.996  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:00:38.996  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:00:39.241  INFO [查询序列码] 托盘内箱码数量: 2
2025-07-21 07:00:39.560  INFO [查询序列码] 托盘内瓶码数量: 24
2025-07-21 07:00:39.561  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:00:42.191  INFO [解绑] 开始解绑操作流程
2025-07-21 07:00:42.192  INFO [解绑] 接收到请求参数: code=00703435470002290235
2025-07-21 07:00:42.204  INFO [解绑] 数据库连接获取成功
2025-07-21 07:00:42.208  INFO [解绑] 开始查询序列码: 00703435470002290235
2025-07-21 07:00:42.214  INFO [解绑] 查询到序列码信息: code_flag=2, level_code=4, zero_box=0, batch_no=R202500040
2025-07-21 07:00:42.214  INFO [解绑] 序列码状态正常: code_flag=2
2025-07-21 07:00:42.215  INFO [解绑] 开始判断托盘码是否符合解绑条件
2025-07-21 07:00:42.215  INFO [解绑] 开始解绑托盘下所有大箱及相关瓶码与托盘的关联关系
2025-07-21 07:00:42.215  INFO [解绑] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = 0,
                parentCode = NULL,
                boxCode = NULL,
                palletCode = NULL,
                codeFlag = 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-21 07:00:42.239  INFO [解绑] 更新托盘码信息成功
2025-07-21 07:00:42.240  INFO [解绑] 开始执行解除托盘下所有大箱与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '00703435470002290235'
              AND palletCode = '00703435470002290235'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-21 07:00:42.261  INFO [解绑] 解除托盘下所有大箱与托盘的绑定成功
2025-07-21 07:00:42.262  INFO [解绑] 开始执行解除托盘下所有小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-21 07:00:42.282  INFO [解绑] 解除托盘下所有小瓶与托盘的绑定成功
2025-07-21 07:00:42.283  INFO [解绑] 事务提交成功，解绑操作完成
2025-07-21 07:00:56.749  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:00:56.760  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:00:56.763  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:00:56.765  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:00:56.766  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:00:56.895  INFO [查询序列码] 托盘内箱码数量: 0
2025-07-21 07:00:57.204  INFO [查询序列码] 托盘内瓶码数量: 0
2025-07-21 07:00:57.206  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:00:59.655  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:00:59.692  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:00:59.694  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:00:59.696  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:00:59.699  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:00:59.792  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 07:00:59.793  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:01:03.436  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:01:03.445  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:01:03.449  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:01:03.451  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:01:03.452  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:01:03.571  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:01:03.572  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:01:07.609  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:01:07.621  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:01:07.623  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:01:07.625  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:01:07.626  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:01:07.746  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:01:07.747  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:06:22.683  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:06:22.695  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:06:22.698  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:06:22.700  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:06:22.701  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:06:22.821  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:06:22.822  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:06:25.828  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:06:25.838  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:06:25.840  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:06:25.842  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:06:25.843  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:06:26.032  INFO [查询序列码] 托盘内箱码数量: 0
2025-07-21 07:06:26.558  INFO [查询序列码] 托盘内瓶码数量: 0
2025-07-21 07:06:26.559  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:06:35.739  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:06:35.751  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:06:35.754  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:06:35.758  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:06:35.759  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:06:35.845  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 07:06:35.848  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:06:40.502  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:06:40.514  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:06:40.516  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:06:40.518  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:06:40.520  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:06:40.636  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:06:40.637  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:06:44.006  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:06:44.039  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:06:44.042  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:06:44.044  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:06:44.045  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:06:44.163  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:06:44.164  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:06:48.861  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:06:48.869  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:06:48.872  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:06:48.875  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:06:48.875  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:06:48.989  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:06:48.990  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:06:58.863  INFO [聚合] 开始聚合操作流程
2025-07-21 07:06:58.865  INFO [聚合] 接收到请求参数: aggregate_type=newPackage, box_codes数量=1
2025-07-21 07:06:58.879  INFO [聚合] 数据库连接获取成功
2025-07-21 07:06:58.880  INFO [聚合] |新建包装| 开始执行新建包装模式
2025-07-21 07:06:58.881  INFO [聚合] |新建包装| 处理箱码 1/1: 00503435470000872620
2025-07-21 07:06:58.883  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 07:06:58.883  INFO [聚合] |新建包装| 开始执行新建包装操作
2025-07-21 07:06:58.886  INFO [取码函数] 查询生产订单信息SQL: SELECT TOP 1 id, productId, poNumber, batchNo FROM [produce_order] WHERE id = '4'
2025-07-21 07:06:58.902  INFO [取码函数] 查询产品信息SQL: SELECT TOP 1 id, productCode, packageRules, codeStyle FROM [basic_product] WHERE id = '3'
2025-07-21 07:06:58.907  INFO [取码函数] 查询序列码信息SQL: SELECT TOP 1 tc.id, tc.code, tc.snCode, tc.fileID 
        FROM [transit_code] tc
        JOIN [sn_record] sr ON sr.id = tc.fileID
        WHERE tc.orderId = '4' 
        AND tc.levelCode = '4' 
        AND tc.typeFlag = '1' 
        AND tc.codeFlag IN (0, 1)
        AND sr.filterValue = '7'
        AND sr.resCode = '0343547'
        AND sr.typeFlag = '1'
        AND sr.codeStyle = 'tracelink'
        ORDER BY tc.id
2025-07-21 07:06:58.933  INFO [取码函数] 数据库中符合条件的序列码数量: 1
2025-07-21 07:06:58.934  INFO [聚合] |新建包装| 获取托盘码结果: {"data": Array [String("00703435470002290235")], "errmsg": String("序列码获取成功"), "errcode": Number(0)}
2025-07-21 07:06:58.936  INFO [聚合] |新建包装| 获取托盘码数据: [String("00703435470002290235")]
2025-07-21 07:06:58.937  INFO [聚合] |新建包装| 获取到托盘码: 00703435470002290235
2025-07-21 07:06:58.938  INFO [聚合] |新建包装| 开始执行更新瓶码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290235',
                updateTime = '2025-07-21 07:06:58'
            WHERE levelCode = 1
              AND codeFlag = 2
              AND boxCode IN ('00503435470000872620')
        
2025-07-21 07:06:59.045  INFO [聚合] |新建包装| 更新瓶码信息成功
2025-07-21 07:06:59.045  INFO [聚合] |新建包装| 开始执行更新箱码SQL: 
            UPDATE transit_code
            SET parentCode = '00703435470002290235',
                palletCode = '00703435470002290235',
                updateTime = '2025-07-21 07:07:58'
            WHERE levelCode = 3
              AND codeFlag = 2
              AND code IN ('00503435470000872620')
        
2025-07-21 07:06:59.054  INFO [聚合] |新建包装| 更新箱码信息成功
2025-07-21 07:06:59.054  INFO [聚合] |新建包装| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290235',
                amount = 1,
                codeFlag = 2,
                updateTime = '2025-07-21 07:11:58'
            WHERE levelCode = 4
              AND code = '00703435470002290235'
        
2025-07-21 07:06:59.059  INFO [聚合] |新建包装| 更新托盘码信息成功
2025-07-21 07:06:59.061  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-21 07:07:39.030  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:07:39.041  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:07:39.048  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:07:39.050  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:07:39.051  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:07:39.181  INFO [查询序列码] 托盘内箱码数量: 1
2025-07-21 07:07:39.480  INFO [查询序列码] 托盘内瓶码数量: 6
2025-07-21 07:07:39.481  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:07:44.918  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:07:44.928  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:07:44.931  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:07:44.934  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:07:44.934  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:07:45.054  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:07:45.055  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:08:05.075  INFO [聚合] 开始聚合操作流程
2025-07-21 07:08:05.076  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-21 07:08:05.088  INFO [聚合] 数据库连接获取成功
2025-07-21 07:08:05.092  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-21 07:08:05.096  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-21 07:08:05.096  INFO [聚合] |放入| 处理箱码 1/1: 015034354727603921100000127124
2025-07-21 07:08:05.097  INFO [聚合] |放入| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 07:08:05.097  INFO [聚合] |放入| 开始执行放入操作
2025-07-21 07:08:05.097  INFO [聚合] |放入| 处理箱码: 015034354727603921100000127124
2025-07-21 07:08:05.098  INFO [聚合] |放入| 开始执行更新瓶码SQL: 
                UPDATE transit_code
                SET palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 07:08:05'
                WHERE levelCode = 1
                  AND codeFlag = 2
                  AND boxCode = '015034354727603921100000127124'
            
2025-07-21 07:08:05.239  INFO [聚合] |放入| 更新瓶码信息成功
2025-07-21 07:08:05.239  INFO [聚合] |放入| 开始执行更新箱码SQL: 
                UPDATE transit_code
                SET parentCode = '00703435470002290235',
                    palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 07:09:05'
                WHERE levelCode = 3
                  AND codeFlag = 2
                  AND code = '015034354727603921100000127124'
            
2025-07-21 07:08:05.241  INFO [聚合] |放入| 更新箱码信息成功
2025-07-21 07:08:05.241  INFO [聚合] |放入| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount + 1,
                updateTime = '2025-07-21 07:11:05'
            WHERE levelCode = 4
              AND codeFlag = 2
              AND code = '00703435470002290235'
        
2025-07-21 07:08:05.247  INFO [聚合] |放入| 更新托盘码信息成功
2025-07-21 07:08:05.248  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-21 07:09:17.121  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:09:17.132  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:09:17.135  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:09:17.137  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:09:17.138  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:09:17.256  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:09:17.257  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:09:19.225  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:09:19.234  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:09:19.238  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:09:19.241  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:09:19.241  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:09:19.376  INFO [查询序列码] 托盘内箱码数量: 2
2025-07-21 07:09:19.656  INFO [查询序列码] 托盘内瓶码数量: 18
2025-07-21 07:09:19.656  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:10:02.337  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:10:02.348  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:10:02.351  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:10:02.353  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:10:02.354  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:10:02.471  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:10:02.472  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:10:04.623  INFO [取出] 开始取出操作流程
2025-07-21 07:10:04.624  INFO [取出] 接收到请求参数: code=015034354727603921100000127124
2025-07-21 07:10:04.633  INFO [取出] 数据库连接获取成功
2025-07-21 07:10:04.634  INFO [取出] 开始查询序列码: 015034354727603921100000127124
2025-07-21 07:10:04.636  INFO [取出] 查询到序列码信息: code_flag=2, level_code=3, zero_box=0, batch_no=R202500040
2025-07-21 07:10:04.636  INFO [取出] 序列码状态正常: code_flag=2
2025-07-21 07:10:04.637  INFO [取出] 开始判断箱码是否符合取出条件
2025-07-21 07:10:04.638  INFO [取出] 大箱关联的托盘码: 00703435470002290235
2025-07-21 07:10:04.638  INFO [取出] 开始从托盘中取出箱码
2025-07-21 07:10:04.639  INFO [取出] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount - 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-21 07:10:04.642  INFO [取出] 更新托盘码信息成功
2025-07-21 07:10:04.642  INFO [取出] 开始执行解除箱码与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '015034354727603921100000127124'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-21 07:10:04.643  INFO [取出] 解除箱码与托盘的绑定成功
2025-07-21 07:10:04.644  INFO [取出] 开始执行解除小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '015034354727603921100000127124'
              AND boxCode = '015034354727603921100000127124'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-21 07:10:04.663  INFO [取出] 解除小瓶与托盘的绑定成功
2025-07-21 07:10:04.664  INFO [取出] 事务提交成功，取出操作完成
2025-07-21 07:10:17.346  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:10:17.356  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:10:17.359  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:10:17.362  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:10:17.362  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:10:17.503  INFO [查询序列码] 托盘内箱码数量: 1
2025-07-21 07:10:17.851  INFO [查询序列码] 托盘内瓶码数量: 6
2025-07-21 07:10:17.853  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:10:23.692  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:10:23.703  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:10:23.707  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:10:23.710  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:10:23.710  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:10:23.836  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:10:23.837  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:20:44.128  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:20:44.140  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:20:44.143  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:20:44.145  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:20:44.145  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:20:44.274  INFO [查询序列码] 托盘内箱码数量: 1
2025-07-21 07:20:44.557  INFO [查询序列码] 托盘内瓶码数量: 6
2025-07-21 07:20:44.558  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:20:48.529  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:20:48.540  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:20:48.545  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:20:48.548  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:20:48.548  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:20:48.637  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 07:20:48.638  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:20:51.736  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:20:51.752  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:20:51.765  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:20:51.769  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:20:51.771  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:20:51.912  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:20:51.913  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:21:21.637  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:21:21.648  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:21:21.650  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:21:21.655  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:21:21.659  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:21:21.899  INFO [查询序列码] 托盘内箱码数量: 1
2025-07-21 07:21:22.200  INFO [查询序列码] 托盘内瓶码数量: 6
2025-07-21 07:21:22.201  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:21:26.972  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:21:26.986  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:21:26.988  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:21:26.990  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:21:26.991  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:21:27.115  INFO [查询序列码] 托盘内箱码数量: 1
2025-07-21 07:21:27.421  INFO [查询序列码] 托盘内瓶码数量: 6
2025-07-21 07:21:27.422  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:22:21.269  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:22:21.280  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:22:21.282  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:22:21.284  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:22:21.284  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:22:21.424  INFO [查询序列码] 托盘内箱码数量: 1
2025-07-21 07:22:21.711  INFO [查询序列码] 托盘内瓶码数量: 6
2025-07-21 07:22:21.712  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:22:27.671  INFO [解绑] 开始解绑操作流程
2025-07-21 07:22:27.672  INFO [解绑] 接收到请求参数: code=00703435470002290235
2025-07-21 07:22:27.683  INFO [解绑] 数据库连接获取成功
2025-07-21 07:22:27.684  INFO [解绑] 开始查询序列码: 00703435470002290235
2025-07-21 07:22:27.686  INFO [解绑] 查询到序列码信息: code_flag=2, level_code=4, zero_box=0, batch_no=R202500040
2025-07-21 07:22:27.686  INFO [解绑] 序列码状态正常: code_flag=2
2025-07-21 07:22:27.693  INFO [解绑] 开始判断托盘码是否符合解绑条件
2025-07-21 07:22:27.694  INFO [解绑] 开始解绑托盘下所有大箱及相关瓶码与托盘的关联关系
2025-07-21 07:22:27.695  INFO [解绑] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = 0,
                parentCode = NULL,
                boxCode = NULL,
                palletCode = NULL,
                codeFlag = 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-21 07:22:27.696  INFO [解绑] 更新托盘码信息成功
2025-07-21 07:22:27.697  INFO [解绑] 开始执行解除托盘下所有大箱与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '00703435470002290235'
              AND palletCode = '00703435470002290235'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-21 07:22:27.711  INFO [解绑] 解除托盘下所有大箱与托盘的绑定成功
2025-07-21 07:22:27.712  INFO [解绑] 开始执行解除托盘下所有小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-21 07:22:27.726  INFO [解绑] 解除托盘下所有小瓶与托盘的绑定成功
2025-07-21 07:22:27.728  INFO [解绑] 事务提交成功，解绑操作完成
2025-07-21 07:22:47.209  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:22:47.222  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:22:47.224  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:22:47.226  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:22:47.227  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:22:47.359  INFO [查询序列码] 托盘内箱码数量: 0
2025-07-21 07:22:47.636  INFO [查询序列码] 托盘内瓶码数量: 0
2025-07-21 07:22:47.637  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:22:58.781  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:22:58.791  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:22:58.794  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:22:58.797  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:22:58.797  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:22:58.886  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 07:22:58.887  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:23:05.574  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:23:05.583  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:23:05.587  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:23:05.589  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:23:05.589  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:23:05.705  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:23:05.706  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:23:08.613  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:23:08.623  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:23:08.626  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:23:08.628  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:23:08.629  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:23:08.745  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:23:08.746  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:23:11.595  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:23:11.607  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:23:11.610  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:23:11.612  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:23:11.612  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:23:11.735  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:23:11.736  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:23:17.285  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:23:17.295  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:23:17.297  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:23:17.300  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:23:17.300  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:23:17.418  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:23:17.419  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:23:25.048  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:23:25.061  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:23:25.063  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:23:25.066  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:23:25.066  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:23:25.194  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:23:25.195  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:23:41.673  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:23:41.684  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:23:41.687  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:23:41.690  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:23:41.690  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:23:41.825  INFO [查询序列码] 托盘内箱码数量: 0
2025-07-21 07:23:42.112  INFO [查询序列码] 托盘内瓶码数量: 0
2025-07-21 07:23:42.114  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:23:49.609  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:23:49.620  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:23:49.623  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:23:49.625  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:23:49.625  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:23:49.713  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 07:23:49.714  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:23:51.360  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:23:51.370  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:23:51.373  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:23:51.377  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:23:51.377  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:23:51.496  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:23:51.497  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:23:55.216  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:23:55.226  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:23:55.228  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:23:55.231  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:23:55.231  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:23:55.352  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:23:55.353  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:23:55.487  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:23:55.497  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:23:55.500  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:23:55.502  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:23:55.503  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:23:55.622  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:23:55.623  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:23:58.252  INFO [聚合] 开始聚合操作流程
2025-07-21 07:23:58.252  INFO [聚合] 接收到请求参数: aggregate_type=newPackage, box_codes数量=2
2025-07-21 07:23:58.264  INFO [聚合] 数据库连接获取成功
2025-07-21 07:23:58.265  INFO [聚合] |新建包装| 开始执行新建包装模式
2025-07-21 07:23:58.265  INFO [聚合] |新建包装| 处理箱码 1/2: 00503435470000872620
2025-07-21 07:23:58.267  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 07:23:58.268  INFO [聚合] |新建包装| 处理箱码 2/2: 015034354727603921100000127124
2025-07-21 07:23:58.269  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 07:23:58.270  INFO [聚合] |新建包装| 开始执行新建包装操作
2025-07-21 07:23:58.272  INFO [取码函数] 查询生产订单信息SQL: SELECT TOP 1 id, productId, poNumber, batchNo FROM [produce_order] WHERE id = '4'
2025-07-21 07:23:58.282  INFO [取码函数] 查询产品信息SQL: SELECT TOP 1 id, productCode, packageRules, codeStyle FROM [basic_product] WHERE id = '3'
2025-07-21 07:23:58.283  INFO [取码函数] 查询序列码信息SQL: SELECT TOP 1 tc.id, tc.code, tc.snCode, tc.fileID 
        FROM [transit_code] tc
        JOIN [sn_record] sr ON sr.id = tc.fileID
        WHERE tc.orderId = '4' 
        AND tc.levelCode = '4' 
        AND tc.typeFlag = '1' 
        AND tc.codeFlag IN (0, 1)
        AND sr.filterValue = '7'
        AND sr.resCode = '0343547'
        AND sr.typeFlag = '1'
        AND sr.codeStyle = 'tracelink'
        ORDER BY tc.id
2025-07-21 07:23:58.292  INFO [取码函数] 数据库中符合条件的序列码数量: 1
2025-07-21 07:23:58.293  INFO [聚合] |新建包装| 获取托盘码结果: {"errcode": Number(0), "errmsg": String("序列码获取成功"), "data": Array [String("00703435470002290235")]}
2025-07-21 07:23:58.293  INFO [聚合] |新建包装| 获取托盘码数据: [String("00703435470002290235")]
2025-07-21 07:23:58.294  INFO [聚合] |新建包装| 获取到托盘码: 00703435470002290235
2025-07-21 07:23:58.295  INFO [聚合] |新建包装| 开始执行更新瓶码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290235',
                updateTime = '2025-07-21 07:23:58'
            WHERE levelCode = 1
              AND codeFlag = 2
              AND boxCode IN ('00503435470000872620','015034354727603921100000127124')
        
2025-07-21 07:23:58.484  INFO [聚合] |新建包装| 更新瓶码信息成功
2025-07-21 07:23:58.484  INFO [聚合] |新建包装| 开始执行更新箱码SQL: 
            UPDATE transit_code
            SET parentCode = '00703435470002290235',
                palletCode = '00703435470002290235',
                updateTime = '2025-07-21 07:24:58'
            WHERE levelCode = 3
              AND codeFlag = 2
              AND code IN ('00503435470000872620','015034354727603921100000127124')
        
2025-07-21 07:23:58.491  INFO [聚合] |新建包装| 更新箱码信息成功
2025-07-21 07:23:58.492  INFO [聚合] |新建包装| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290235',
                amount = 2,
                codeFlag = 2,
                updateTime = '2025-07-21 07:28:58'
            WHERE levelCode = 4
              AND code = '00703435470002290235'
        
2025-07-21 07:23:58.494  INFO [聚合] |新建包装| 更新托盘码信息成功
2025-07-21 07:23:58.495  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-21 07:24:09.546  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:24:09.559  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:24:09.561  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:24:09.563  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:24:09.564  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:24:09.692  INFO [查询序列码] 托盘内箱码数量: 2
2025-07-21 07:24:10.307  INFO [查询序列码] 托盘内瓶码数量: 18
2025-07-21 07:24:10.307  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:24:21.281  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:24:21.292  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:24:21.295  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:24:21.297  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:24:21.298  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:24:21.383  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 07:24:21.385  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:24:23.215  INFO [取出] 开始取出操作流程
2025-07-21 07:24:23.217  INFO [取出] 接收到请求参数: code=00503435470000872620
2025-07-21 07:24:23.228  INFO [取出] 数据库连接获取成功
2025-07-21 07:24:23.228  INFO [取出] 开始查询序列码: 00503435470000872620
2025-07-21 07:24:23.229  INFO [取出] 查询到序列码信息: code_flag=2, level_code=3, zero_box=1, batch_no=R202500040
2025-07-21 07:24:23.230  INFO [取出] 序列码状态正常: code_flag=2
2025-07-21 07:24:23.230  INFO [取出] 开始判断箱码是否符合取出条件
2025-07-21 07:24:23.231  INFO [取出] 大箱关联的托盘码: 00703435470002290235
2025-07-21 07:24:23.231  INFO [取出] 开始从托盘中取出箱码
2025-07-21 07:24:23.231  INFO [取出] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount - 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-21 07:24:23.233  INFO [取出] 更新托盘码信息成功
2025-07-21 07:24:23.233  INFO [取出] 开始执行解除箱码与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00503435470000872620'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-21 07:24:23.234  INFO [取出] 解除箱码与托盘的绑定成功
2025-07-21 07:24:23.234  INFO [取出] 开始执行解除小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '00503435470000872620'
              AND boxCode = '00503435470000872620'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-21 07:24:23.255  INFO [取出] 解除小瓶与托盘的绑定成功
2025-07-21 07:24:23.256  INFO [取出] 事务提交成功，取出操作完成
2025-07-21 07:24:40.373  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:24:40.382  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:24:40.385  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:24:40.388  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:24:40.389  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:24:40.475  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 07:24:40.476  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:24:43.975  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:24:43.987  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:24:43.990  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:24:43.992  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:24:43.993  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:24:44.116  INFO [查询序列码] 托盘内箱码数量: 1
2025-07-21 07:24:44.649  INFO [查询序列码] 托盘内瓶码数量: 12
2025-07-21 07:24:44.651  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:24:48.293  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:24:48.304  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:24:48.307  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:24:48.309  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:24:48.309  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:24:48.408  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 07:24:48.408  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:25:00.556  INFO [聚合] 开始聚合操作流程
2025-07-21 07:25:00.556  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-21 07:25:00.567  INFO [聚合] 数据库连接获取成功
2025-07-21 07:25:00.568  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-21 07:25:00.569  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-21 07:25:00.570  INFO [聚合] |放入| 处理箱码 1/1: 00503435470000872620
2025-07-21 07:25:00.571  INFO [聚合] |放入| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 07:25:00.572  INFO [聚合] |放入| 开始执行放入操作
2025-07-21 07:25:00.573  INFO [聚合] |放入| 处理箱码: 00503435470000872620
2025-07-21 07:25:00.573  INFO [聚合] |放入| 开始执行更新瓶码SQL: 
                UPDATE transit_code
                SET palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 07:25:00'
                WHERE levelCode = 1
                  AND codeFlag = 2
                  AND boxCode = '00503435470000872620'
            
2025-07-21 07:25:00.686  INFO [聚合] |放入| 更新瓶码信息成功
2025-07-21 07:25:00.687  INFO [聚合] |放入| 开始执行更新箱码SQL: 
                UPDATE transit_code
                SET parentCode = '00703435470002290235',
                    palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 07:26:00'
                WHERE levelCode = 3
                  AND codeFlag = 2
                  AND code = '00503435470000872620'
            
2025-07-21 07:25:00.688  INFO [聚合] |放入| 更新箱码信息成功
2025-07-21 07:25:00.689  INFO [聚合] |放入| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount + 1,
                updateTime = '2025-07-21 07:28:00'
            WHERE levelCode = 4
              AND codeFlag = 2
              AND code = '00703435470002290235'
        
2025-07-21 07:25:00.690  INFO [聚合] |放入| 更新托盘码信息成功
2025-07-21 07:25:00.691  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-21 07:30:58.455  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:30:58.466  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:30:58.469  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:30:58.471  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:30:58.473  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:30:58.597  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:30:58.598  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:31:02.032  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:31:02.042  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:31:02.045  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:31:02.051  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:31:02.051  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:31:02.228  INFO [查询序列码] 托盘内箱码数量: 2
2025-07-21 07:31:02.515  INFO [查询序列码] 托盘内瓶码数量: 18
2025-07-21 07:31:02.517  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:31:04.456  INFO [解绑] 开始解绑操作流程
2025-07-21 07:31:04.457  INFO [解绑] 接收到请求参数: code=00703435470002290235
2025-07-21 07:31:04.490  INFO [解绑] 数据库连接获取成功
2025-07-21 07:31:04.490  INFO [解绑] 开始查询序列码: 00703435470002290235
2025-07-21 07:31:04.492  INFO [解绑] 查询到序列码信息: code_flag=2, level_code=4, zero_box=0, batch_no=R202500040
2025-07-21 07:31:04.493  INFO [解绑] 序列码状态正常: code_flag=2
2025-07-21 07:31:04.493  INFO [解绑] 开始判断托盘码是否符合解绑条件
2025-07-21 07:31:04.494  INFO [解绑] 开始解绑托盘下所有大箱及相关瓶码与托盘的关联关系
2025-07-21 07:31:04.495  INFO [解绑] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = 0,
                parentCode = NULL,
                boxCode = NULL,
                palletCode = NULL,
                codeFlag = 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-21 07:31:04.499  INFO [解绑] 更新托盘码信息成功
2025-07-21 07:31:04.500  INFO [解绑] 开始执行解除托盘下所有大箱与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '00703435470002290235'
              AND palletCode = '00703435470002290235'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-21 07:31:04.517  INFO [解绑] 解除托盘下所有大箱与托盘的绑定成功
2025-07-21 07:31:04.518  INFO [解绑] 开始执行解除托盘下所有小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-21 07:31:04.533  INFO [解绑] 解除托盘下所有小瓶与托盘的绑定成功
2025-07-21 07:31:04.535  INFO [解绑] 事务提交成功，解绑操作完成
2025-07-21 07:31:13.758  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:31:13.769  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:31:13.772  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:31:13.774  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:31:13.775  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:31:13.910  INFO [查询序列码] 托盘内箱码数量: 0
2025-07-21 07:31:14.197  INFO [查询序列码] 托盘内瓶码数量: 0
2025-07-21 07:31:14.198  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:31:16.778  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:31:16.791  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:31:16.794  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:31:16.796  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:31:16.797  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:31:16.885  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 07:31:16.886  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:31:24.279  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:31:24.289  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:31:24.292  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:31:24.294  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:31:24.295  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:31:24.440  INFO [查询序列码] 托盘内箱码数量: 0
2025-07-21 07:31:24.757  INFO [查询序列码] 托盘内瓶码数量: 0
2025-07-21 07:31:24.758  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:31:29.107  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:31:29.120  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:31:29.122  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:31:29.124  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:31:29.128  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:31:29.233  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 07:31:29.235  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:31:40.023  INFO [聚合] 开始聚合操作流程
2025-07-21 07:31:40.024  INFO [聚合] 接收到请求参数: aggregate_type=newPackage, box_codes数量=1
2025-07-21 07:31:40.040  INFO [聚合] 数据库连接获取成功
2025-07-21 07:31:40.040  INFO [聚合] |新建包装| 开始执行新建包装模式
2025-07-21 07:31:40.041  INFO [聚合] |新建包装| 处理箱码 1/1: 00503435470000872620
2025-07-21 07:31:40.042  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 07:31:40.042  INFO [聚合] |新建包装| 开始执行新建包装操作
2025-07-21 07:31:40.043  INFO [取码函数] 查询生产订单信息SQL: SELECT TOP 1 id, productId, poNumber, batchNo FROM [produce_order] WHERE id = '4'
2025-07-21 07:31:40.051  INFO [取码函数] 查询产品信息SQL: SELECT TOP 1 id, productCode, packageRules, codeStyle FROM [basic_product] WHERE id = '3'
2025-07-21 07:31:40.052  INFO [取码函数] 查询序列码信息SQL: SELECT TOP 1 tc.id, tc.code, tc.snCode, tc.fileID 
        FROM [transit_code] tc
        JOIN [sn_record] sr ON sr.id = tc.fileID
        WHERE tc.orderId = '4' 
        AND tc.levelCode = '4' 
        AND tc.typeFlag = '1' 
        AND tc.codeFlag IN (0, 1)
        AND sr.filterValue = '7'
        AND sr.resCode = '0343547'
        AND sr.typeFlag = '1'
        AND sr.codeStyle = 'tracelink'
        ORDER BY tc.id
2025-07-21 07:31:40.061  INFO [取码函数] 数据库中符合条件的序列码数量: 1
2025-07-21 07:31:40.062  INFO [聚合] |新建包装| 获取托盘码结果: {"errmsg": String("序列码获取成功"), "errcode": Number(0), "data": Array [String("00703435470002290235")]}
2025-07-21 07:31:40.063  INFO [聚合] |新建包装| 获取托盘码数据: [String("00703435470002290235")]
2025-07-21 07:31:40.063  INFO [聚合] |新建包装| 获取到托盘码: 00703435470002290235
2025-07-21 07:31:40.064  INFO [聚合] |新建包装| 开始执行更新瓶码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290235',
                updateTime = '2025-07-21 07:31:40'
            WHERE levelCode = 1
              AND codeFlag = 2
              AND boxCode IN ('00503435470000872620')
        
2025-07-21 07:31:40.165  INFO [聚合] |新建包装| 更新瓶码信息成功
2025-07-21 07:31:40.166  INFO [聚合] |新建包装| 开始执行更新箱码SQL: 
            UPDATE transit_code
            SET parentCode = '00703435470002290235',
                palletCode = '00703435470002290235',
                updateTime = '2025-07-21 07:32:40'
            WHERE levelCode = 3
              AND codeFlag = 2
              AND code IN ('00503435470000872620')
        
2025-07-21 07:31:40.168  INFO [聚合] |新建包装| 更新箱码信息成功
2025-07-21 07:31:40.169  INFO [聚合] |新建包装| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290235',
                amount = 1,
                codeFlag = 2,
                updateTime = '2025-07-21 07:36:40'
            WHERE levelCode = 4
              AND code = '00703435470002290235'
        
2025-07-21 07:31:40.170  INFO [聚合] |新建包装| 更新托盘码信息成功
2025-07-21 07:31:40.171  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-21 07:32:07.143  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:32:07.154  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:32:07.157  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:32:07.159  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:32:07.159  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:32:07.294  INFO [查询序列码] 托盘内箱码数量: 1
2025-07-21 07:32:07.582  INFO [查询序列码] 托盘内瓶码数量: 6
2025-07-21 07:32:07.583  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:32:11.002  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:32:11.012  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:32:11.015  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:32:11.017  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:32:11.017  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:32:11.106  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 07:32:11.107  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:32:13.505  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:32:13.515  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:32:13.518  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:32:13.521  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:32:13.522  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:32:13.639  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 07:32:13.640  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:32:16.952  INFO [聚合] 开始聚合操作流程
2025-07-21 07:32:16.953  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-21 07:32:16.965  INFO [聚合] 数据库连接获取成功
2025-07-21 07:32:16.966  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-21 07:32:16.967  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-21 07:32:16.968  INFO [聚合] |放入| 处理箱码 1/1: 015034354727603921100000127124
2025-07-21 07:32:16.970  INFO [聚合] |放入| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 07:32:16.971  INFO [聚合] |放入| 开始执行放入操作
2025-07-21 07:32:16.971  INFO [聚合] |放入| 处理箱码: 015034354727603921100000127124
2025-07-21 07:32:16.972  INFO [聚合] |放入| 开始执行更新瓶码SQL: 
                UPDATE transit_code
                SET palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 07:32:16'
                WHERE levelCode = 1
                  AND codeFlag = 2
                  AND boxCode = '015034354727603921100000127124'
            
2025-07-21 07:32:17.104  INFO [聚合] |放入| 更新瓶码信息成功
2025-07-21 07:32:17.105  INFO [聚合] |放入| 开始执行更新箱码SQL: 
                UPDATE transit_code
                SET parentCode = '00703435470002290235',
                    palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 07:33:16'
                WHERE levelCode = 3
                  AND codeFlag = 2
                  AND code = '015034354727603921100000127124'
            
2025-07-21 07:32:17.107  INFO [聚合] |放入| 更新箱码信息成功
2025-07-21 07:32:17.108  INFO [聚合] |放入| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount + 1,
                updateTime = '2025-07-21 07:35:16'
            WHERE levelCode = 4
              AND codeFlag = 2
              AND code = '00703435470002290235'
        
2025-07-21 07:32:17.109  INFO [聚合] |放入| 更新托盘码信息成功
2025-07-21 07:32:17.111  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-21 07:36:40.507  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:36:40.517  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:36:40.520  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:36:40.522  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:36:40.522  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:36:40.612  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 07:36:40.613  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:36:42.664  INFO [取出] 开始取出操作流程
2025-07-21 07:36:42.665  INFO [取出] 接收到请求参数: code=00503435470000872620
2025-07-21 07:36:42.678  INFO [取出] 数据库连接获取成功
2025-07-21 07:36:42.679  INFO [取出] 开始查询序列码: 00503435470000872620
2025-07-21 07:36:42.680  INFO [取出] 查询到序列码信息: code_flag=2, level_code=3, zero_box=1, batch_no=R202500040
2025-07-21 07:36:42.681  INFO [取出] 序列码状态正常: code_flag=2
2025-07-21 07:36:42.682  INFO [取出] 开始判断箱码是否符合取出条件
2025-07-21 07:36:42.682  INFO [取出] 大箱关联的托盘码: 00703435470002290235
2025-07-21 07:36:42.683  INFO [取出] 开始从托盘中取出箱码
2025-07-21 07:36:42.683  INFO [取出] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount - 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-21 07:36:42.685  INFO [取出] 更新托盘码信息成功
2025-07-21 07:36:42.687  INFO [取出] 开始执行解除箱码与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00503435470000872620'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-21 07:36:42.689  INFO [取出] 解除箱码与托盘的绑定成功
2025-07-21 07:36:42.689  INFO [取出] 开始执行解除小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '00503435470000872620'
              AND boxCode = '00503435470000872620'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-21 07:36:42.712  INFO [取出] 解除小瓶与托盘的绑定成功
2025-07-21 07:36:42.713  INFO [取出] 事务提交成功，取出操作完成
2025-07-21 07:36:51.370  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:36:51.380  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:36:51.382  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:36:51.385  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:36:51.386  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 07:36:51.548  INFO [查询序列码] 托盘内箱码数量: 1
2025-07-21 07:36:51.859  INFO [查询序列码] 托盘内瓶码数量: 12
2025-07-21 07:36:51.860  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:36:54.800  INFO [查询序列码] 开始查询序列码信息
2025-07-21 07:36:54.811  INFO [查询序列码] 数据库连接获取成功
2025-07-21 07:36:54.814  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 07:36:54.817  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 07:36:54.817  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 07:36:54.907  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 07:36:54.908  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 07:36:58.942  INFO [聚合] 开始聚合操作流程
2025-07-21 07:36:58.943  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-21 07:36:58.954  INFO [聚合] 数据库连接获取成功
2025-07-21 07:36:58.955  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-21 07:36:58.957  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-21 07:36:58.957  INFO [聚合] |放入| 处理箱码 1/1: 00503435470000872620
2025-07-21 07:36:58.959  INFO [聚合] |放入| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 07:36:58.959  INFO [聚合] |放入| 开始执行放入操作
2025-07-21 07:36:58.960  INFO [聚合] |放入| 处理箱码: 00503435470000872620
2025-07-21 07:36:58.960  INFO [聚合] |放入| 开始执行更新瓶码SQL: 
                UPDATE transit_code
                SET palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 07:36:58'
                WHERE levelCode = 1
                  AND codeFlag = 2
                  AND boxCode = '00503435470000872620'
            
2025-07-21 07:36:59.072  INFO [聚合] |放入| 更新瓶码信息成功
2025-07-21 07:36:59.072  INFO [聚合] |放入| 开始执行更新箱码SQL: 
                UPDATE transit_code
                SET parentCode = '00703435470002290235',
                    palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 07:37:58'
                WHERE levelCode = 3
                  AND codeFlag = 2
                  AND code = '00503435470000872620'
            
2025-07-21 07:36:59.074  INFO [聚合] |放入| 更新箱码信息成功
2025-07-21 07:36:59.074  INFO [聚合] |放入| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount + 1,
                updateTime = '2025-07-21 07:39:58'
            WHERE levelCode = 4
              AND codeFlag = 2
              AND code = '00703435470002290235'
        
2025-07-21 07:36:59.075  INFO [聚合] |放入| 更新托盘码信息成功
2025-07-21 07:36:59.076  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-21 08:14:53.169  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 08:14:53.170  INFO 数据库连接管理器创建成功
2025-07-21 08:14:53.220  INFO 数据库连接池创建成功
2025-07-21 08:14:53.224  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 08:15:35.744  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 08:15:35.754  INFO 数据库连接管理器创建成功
2025-07-21 08:15:35.797  INFO 数据库连接池创建成功
2025-07-21 08:15:35.804  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 08:19:39.650  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 08:19:39.652  INFO 数据库连接管理器创建成功
2025-07-21 08:19:39.689  INFO 数据库连接池创建成功
2025-07-21 08:19:39.697  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 08:20:38.595  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 08:20:38.596  INFO 数据库连接管理器创建成功
2025-07-21 08:20:38.629  INFO 数据库连接池创建成功
2025-07-21 08:20:38.634  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 08:21:16.109  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 08:21:16.110  INFO 数据库连接管理器创建成功
2025-07-21 08:21:16.150  INFO 数据库连接池创建成功
2025-07-21 08:21:16.157  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 08:23:36.807  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 08:23:36.810  INFO 数据库连接管理器创建成功
2025-07-21 08:23:36.856  INFO 数据库连接池创建成功
2025-07-21 08:23:36.862  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 08:24:02.378  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 08:24:02.379  INFO 数据库连接管理器创建成功
2025-07-21 08:24:02.444  INFO 数据库连接池创建成功
2025-07-21 08:24:02.455  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 08:26:03.623  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 08:26:03.626  INFO 数据库连接管理器创建成功
2025-07-21 08:26:03.667  INFO 数据库连接池创建成功
2025-07-21 08:26:03.676  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 08:27:01.174  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 08:27:01.175  INFO 数据库连接管理器创建成功
2025-07-21 08:27:01.210  INFO 数据库连接池创建成功
2025-07-21 08:27:01.216  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 09:33:20.279  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 09:33:20.280  INFO 数据库连接管理器创建成功
2025-07-21 09:33:20.322  INFO 数据库连接池创建成功
2025-07-21 09:33:20.327  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 09:38:39.740  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 09:38:39.741  INFO 数据库连接管理器创建成功
2025-07-21 09:38:39.788  INFO 数据库连接池创建成功
2025-07-21 09:38:39.793  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 09:39:19.960  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 09:39:19.961  INFO 数据库连接管理器创建成功
2025-07-21 09:39:20.006  INFO 数据库连接池创建成功
2025-07-21 09:39:20.011  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 09:43:34.247  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 09:43:34.248  INFO 数据库连接管理器创建成功
2025-07-21 09:43:34.282  INFO 数据库连接池创建成功
2025-07-21 09:43:34.287  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 09:44:46.475  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 09:44:46.476  INFO 数据库连接管理器创建成功
2025-07-21 09:44:46.525  INFO 数据库连接池创建成功
2025-07-21 09:44:46.530  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 09:46:14.355  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 09:46:14.360  INFO 数据库连接管理器创建成功
2025-07-21 09:46:14.441  INFO 数据库连接池创建成功
2025-07-21 09:46:14.449  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 09:46:16.219  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 09:46:16.220  INFO 数据库连接管理器创建成功
2025-07-21 09:46:16.250  INFO 数据库连接池创建成功
2025-07-21 09:46:16.254  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 09:46:45.087  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 09:46:45.089  INFO 数据库连接管理器创建成功
2025-07-21 09:46:45.147  INFO 数据库连接池创建成功
2025-07-21 09:46:45.158  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 10:49:59.280  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 10:49:59.281  INFO 数据库连接管理器创建成功
2025-07-21 10:49:59.315  INFO 数据库连接池创建成功
2025-07-21 10:49:59.320  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 10:53:53.629  INFO 网络连接完成，耗时: 59ms
2025-07-21 10:58:30.448  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 10:58:30.449  INFO 数据库连接管理器创建成功
2025-07-21 10:58:30.485  INFO 数据库连接池创建成功
2025-07-21 10:58:30.491  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 11:14:31.929  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 11:14:31.930  INFO 数据库连接管理器创建成功
2025-07-21 11:14:31.985  INFO 数据库连接池创建成功
2025-07-21 11:14:31.990  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 11:29:22.880  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 11:29:22.881  INFO 数据库连接管理器创建成功
2025-07-21 11:29:22.952  INFO 数据库连接池创建成功
2025-07-21 11:29:22.962  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 11:36:39.178  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 11:36:39.180  INFO 数据库连接管理器创建成功
2025-07-21 11:36:39.243  INFO 数据库连接池创建成功
2025-07-21 11:36:39.254  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 12:27:11.445  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 12:27:11.446  INFO 数据库连接管理器创建成功
2025-07-21 12:27:11.480  INFO 数据库连接池创建成功
2025-07-21 12:27:11.485  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 13:28:25.639  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 13:28:25.640  INFO 数据库连接管理器创建成功
2025-07-21 13:28:25.688  INFO 数据库连接池创建成功
2025-07-21 13:28:25.694  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 16:50:54.144  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 16:50:54.145  INFO 数据库连接管理器创建成功
2025-07-21 16:50:54.190  INFO 数据库连接池创建成功
2025-07-21 16:50:54.196  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 16:54:16.009  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 16:54:16.010  INFO 数据库连接管理器创建成功
2025-07-21 16:54:16.055  INFO 数据库连接池创建成功
2025-07-21 16:54:16.060  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 16:56:08.299  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 16:56:08.300  INFO 数据库连接管理器创建成功
2025-07-21 16:56:08.347  INFO 数据库连接池创建成功
2025-07-21 16:56:08.355  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 17:26:44.685  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 17:26:44.686  INFO 数据库连接管理器创建成功
2025-07-21 17:26:44.719  INFO 数据库连接池创建成功
2025-07-21 17:26:44.725  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 17:38:55.282  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 17:38:55.284  INFO 数据库连接管理器创建成功
2025-07-21 17:38:55.336  INFO 数据库连接池创建成功
2025-07-21 17:38:55.345  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 17:46:23.009  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 17:46:23.010  INFO 数据库连接管理器创建成功
2025-07-21 17:46:23.059  INFO 数据库连接池创建成功
2025-07-21 17:46:23.065  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 17:46:54.558  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 17:46:54.559  INFO 数据库连接管理器创建成功
2025-07-21 17:46:54.611  INFO 数据库连接池创建成功
2025-07-21 17:46:54.618  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 17:50:01.793  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 17:50:01.794  INFO 数据库连接管理器创建成功
2025-07-21 17:50:01.835  INFO 数据库连接池创建成功
2025-07-21 17:50:01.841  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 19:53:34.975  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 19:53:34.976  INFO 数据库连接管理器创建成功
2025-07-21 19:53:35.018  INFO 数据库连接池创建成功
2025-07-21 19:53:35.023  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 19:53:46.597  INFO 网络连接完成，耗时: 40ms
2025-07-21 20:00:32.216  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 20:00:32.217  INFO 数据库连接管理器创建成功
2025-07-21 20:00:32.244  INFO 数据库连接池创建成功
2025-07-21 20:00:32.249  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 20:01:52.989  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 20:01:52.990  INFO 数据库连接管理器创建成功
2025-07-21 20:01:53.025  INFO 数据库连接池创建成功
2025-07-21 20:01:53.030  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 20:02:42.490  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 20:02:42.491  INFO 数据库连接管理器创建成功
2025-07-21 20:02:42.561  INFO 数据库连接池创建成功
2025-07-21 20:02:42.568  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 20:08:00.916  INFO [查询序列码] 开始查询序列码信息
2025-07-21 20:08:00.963  INFO [查询序列码] 数据库连接获取成功
2025-07-21 20:08:00.987  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 20:08:00.995  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 20:08:00.995  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 20:08:01.157  INFO [查询序列码] 托盘内箱码数量: 2
2025-07-21 20:08:01.461  INFO [查询序列码] 托盘内瓶码数量: 18
2025-07-21 20:08:01.462  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 20:08:51.869  INFO [查询序列码] 开始查询序列码信息
2025-07-21 20:08:51.880  INFO [查询序列码] 数据库连接获取成功
2025-07-21 20:08:51.885  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 20:08:51.887  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 20:08:51.888  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 20:08:52.014  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 20:08:52.015  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 20:08:54.119  INFO [取出] 开始取出操作流程
2025-07-21 20:08:54.120  INFO [取出] 接收到请求参数: code=015034354727603921100000127124
2025-07-21 20:08:54.132  INFO [取出] 数据库连接获取成功
2025-07-21 20:08:54.133  INFO [取出] 开始查询序列码: 015034354727603921100000127124
2025-07-21 20:08:54.136  INFO [取出] 查询到序列码信息: code_flag=2, level_code=3, zero_box=0, batch_no=R202500040
2025-07-21 20:08:54.137  INFO [取出] 序列码状态正常: code_flag=2
2025-07-21 20:08:54.141  INFO [取出] 开始判断箱码是否符合取出条件
2025-07-21 20:08:54.141  INFO [取出] 大箱关联的托盘码: 00703435470002290235
2025-07-21 20:08:54.142  INFO [取出] 开始从托盘中取出箱码
2025-07-21 20:08:54.142  INFO [取出] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount - 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-21 20:08:54.153  INFO [取出] 更新托盘码信息成功
2025-07-21 20:08:54.153  INFO [取出] 开始执行解除箱码与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '015034354727603921100000127124'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-21 20:08:54.158  INFO [取出] 解除箱码与托盘的绑定成功
2025-07-21 20:08:54.159  INFO [取出] 开始执行解除小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '015034354727603921100000127124'
              AND boxCode = '015034354727603921100000127124'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-21 20:08:54.183  INFO [取出] 解除小瓶与托盘的绑定成功
2025-07-21 20:08:54.186  INFO [取出] 事务提交成功，取出操作完成
2025-07-21 20:09:19.869  INFO [查询序列码] 开始查询序列码信息
2025-07-21 20:09:19.879  INFO [查询序列码] 数据库连接获取成功
2025-07-21 20:09:19.882  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 20:09:19.884  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 20:09:19.885  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 20:09:20.042  INFO [查询序列码] 托盘内箱码数量: 1
2025-07-21 20:09:20.418  INFO [查询序列码] 托盘内瓶码数量: 6
2025-07-21 20:09:20.419  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 20:09:24.352  INFO [查询序列码] 开始查询序列码信息
2025-07-21 20:09:24.363  INFO [查询序列码] 数据库连接获取成功
2025-07-21 20:09:24.366  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 20:09:24.369  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 20:09:24.369  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 20:09:24.489  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 20:09:24.490  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 20:09:27.194  INFO [聚合] 开始聚合操作流程
2025-07-21 20:09:27.195  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-21 20:09:27.206  INFO [聚合] 数据库连接获取成功
2025-07-21 20:09:27.207  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-21 20:09:27.208  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-21 20:09:27.209  INFO [聚合] 开始检查产品每托最大箱数
2025-07-21 20:09:27.215 ERROR [聚合] 解析包装规则失败: invalid type: integer `0`, expected a string at line 1 column 10
2025-07-21 20:09:27.215 ERROR [聚合] |放入| 验证箱数限制失败: 解析包装规则失败: invalid type: integer `0`, expected a string at line 1 column 10
2025-07-21 20:12:54.952  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 20:12:54.953  INFO 数据库连接管理器创建成功
2025-07-21 20:12:55.008  INFO 数据库连接池创建成功
2025-07-21 20:12:55.017  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 20:15:14.724  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 20:15:14.725  INFO 数据库连接管理器创建成功
2025-07-21 20:15:14.765  INFO 数据库连接池创建成功
2025-07-21 20:15:14.770  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 20:18:40.648  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 20:18:40.650  INFO 数据库连接管理器创建成功
2025-07-21 20:18:40.689  INFO 数据库连接池创建成功
2025-07-21 20:18:40.695  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 20:20:00.721  INFO [聚合] 开始聚合操作流程
2025-07-21 20:20:00.721  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-21 20:20:00.765  INFO [聚合] 数据库连接获取成功
2025-07-21 20:20:00.766  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-21 20:20:00.771  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-21 20:20:00.771  INFO [聚合] 开始检查产品每托最大箱数
2025-07-21 20:20:00.773  INFO [聚合] 查询到产品信息: id=3
2025-07-21 20:20:00.774 ERROR [包装规则分析] 无效的包装规则数据
2025-07-21 20:20:00.774  INFO [聚合] 产品每托最大箱数: 0
2025-07-21 20:20:00.775 ERROR [聚合] 未找到产品每托最大箱数限制，无法继续聚合操作
2025-07-21 20:20:00.776 ERROR [聚合] |放入| 验证箱数限制失败: 未找到产品每托最大箱数限制，请先设置产品包装规则
2025-07-21 20:24:04.645  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 20:24:04.646  INFO 数据库连接管理器创建成功
2025-07-21 20:24:04.681  INFO 数据库连接池创建成功
2025-07-21 20:24:04.687  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 20:24:13.904  INFO [聚合] 开始聚合操作流程
2025-07-21 20:24:13.904  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-21 20:24:13.949  INFO [聚合] 数据库连接获取成功
2025-07-21 20:24:13.950  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-21 20:24:13.953  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-21 20:24:13.954  INFO [聚合] 开始检查产品每托最大箱数
2025-07-21 20:24:13.956  INFO [聚合] 查询到产品信息: id=3
2025-07-21 20:24:13.957  INFO [聚合] 产品包装规则: []
2025-07-21 20:24:13.958 ERROR [包装规则分析] 无效的包装规则数据
2025-07-21 20:24:13.958  INFO [聚合] 产品每托最大箱数: 0
2025-07-21 20:24:13.959 ERROR [聚合] 未找到产品每托最大箱数限制，无法继续聚合操作
2025-07-21 20:24:13.960 ERROR [聚合] |放入| 验证箱数限制失败: 未找到产品每托最大箱数限制，请先设置产品包装规则
2025-07-21 20:33:48.354  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 20:33:48.355  INFO 数据库连接管理器创建成功
2025-07-21 20:33:48.396  INFO 数据库连接池创建成功
2025-07-21 20:33:48.402  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 20:35:10.327  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 20:35:10.329  INFO 数据库连接管理器创建成功
2025-07-21 20:35:10.376  INFO 数据库连接池创建成功
2025-07-21 20:35:10.384  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 20:35:22.428  INFO [聚合] 开始聚合操作流程
2025-07-21 20:35:22.429  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-21 20:35:22.481  INFO [聚合] 数据库连接获取成功
2025-07-21 20:35:22.482  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-21 20:35:22.485  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-21 20:35:22.486  INFO [聚合] 开始检查产品每托最大箱数，产品编码: 50190500001
2025-07-21 20:35:22.491  INFO [聚合] 查询到产品信息: id=3, productCode=50190500001
2025-07-21 20:35:22.491  INFO [聚合] 产品原始包装规则字段: [{"type":0,"level":1,"name":"Primary","filterValue":"0","GTIN":"00343547276034","amount":12,"__is_add":false,"available":30200,"threshold":30000,"verify":10},{"type":0,"level":3,"name":"Case","filterValue":"5","GTIN":"50343547276039","amount":2,"controlCode":"12345-01","__is_add":false,"available":1048,"threshold":2500,"verify":10},{"type":1,"level":4,"name":"Pallet","filterValue":"7","GTIN":"0343547","amount":1,"controlCode":"54321-01","__is_add":false,"partialFilterValue":"5","partialThreshold":"300","partialControlCode":"12345-01","available":1946,"threshold":200,"verify":10},{"type":1,"level":5,"name":"Partial Case","filterValue":"5","GTIN":"0343547","controlCode":"12345-01","__is_add":false,"available":2871,"threshold":300,"verify":10}]
2025-07-21 20:35:22.492  INFO [聚合] 产品包装规则: [Object {"GTIN": String("00343547276034"), "__is_add": Bool(false), "amount": Number(12), "available": Number(30200), "filterValue": String("0"), "level": Number(1), "name": String("Primary"), "threshold": Number(30000), "type": Number(0), "verify": Number(10)}, Object {"GTIN": String("50343547276039"), "__is_add": Bool(false), "amount": Number(2), "available": Number(1048), "controlCode": String("12345-01"), "filterValue": String("5"), "level": Number(3), "name": String("Case"), "threshold": Number(2500), "type": Number(0), "verify": Number(10)}, Object {"GTIN": String("0343547"), "__is_add": Bool(false), "amount": Number(1), "available": Number(1946), "controlCode": String("54321-01"), "filterValue": String("7"), "level": Number(4), "name": String("Pallet"), "partialControlCode": String("12345-01"), "partialFilterValue": String("5"), "partialThreshold": String("300"), "threshold": Number(200), "type": Number(1), "verify": Number(10)}, Object {"GTIN": String("0343547"), "__is_add": Bool(false), "available": Number(2871), "controlCode": String("12345-01"), "filterValue": String("5"), "level": Number(5), "name": String("Partial Case"), "threshold": Number(300), "type": Number(1), "verify": Number(10)}]
2025-07-21 20:35:22.493 ERROR [聚合] 解析包装规则失败: invalid type: integer `0`, expected a string, 规则值: Object {"GTIN": String("00343547276034"), "__is_add": Bool(false), "amount": Number(12), "available": Number(30200), "filterValue": String("0"), "level": Number(1), "name": String("Primary"), "threshold": Number(30000), "type": Number(0), "verify": Number(10)}
2025-07-21 20:35:22.503 ERROR [聚合] 解析包装规则失败: invalid type: integer `0`, expected a string, 规则值: Object {"GTIN": String("50343547276039"), "__is_add": Bool(false), "amount": Number(2), "available": Number(1048), "controlCode": String("12345-01"), "filterValue": String("5"), "level": Number(3), "name": String("Case"), "threshold": Number(2500), "type": Number(0), "verify": Number(10)}
2025-07-21 20:35:22.504 ERROR [聚合] 解析包装规则失败: invalid type: integer `1`, expected a string, 规则值: Object {"GTIN": String("0343547"), "__is_add": Bool(false), "amount": Number(1), "available": Number(1946), "controlCode": String("54321-01"), "filterValue": String("7"), "level": Number(4), "name": String("Pallet"), "partialControlCode": String("12345-01"), "partialFilterValue": String("5"), "partialThreshold": String("300"), "threshold": Number(200), "type": Number(1), "verify": Number(10)}
2025-07-21 20:35:22.504 ERROR [聚合] 解析包装规则失败: invalid type: integer `1`, expected a string, 规则值: Object {"GTIN": String("0343547"), "__is_add": Bool(false), "available": Number(2871), "controlCode": String("12345-01"), "filterValue": String("5"), "level": Number(5), "name": String("Partial Case"), "threshold": Number(300), "type": Number(1), "verify": Number(10)}
2025-07-21 20:35:22.505 ERROR [聚合] 未能成功解析任何包装规则，尝试使用固定包装规则数据
2025-07-21 20:35:22.505  INFO [聚合] 使用固定包装规则数据: [{"type":0,"level":1,"name":"Primary","filterValue":"0","GTIN":"00343547276034","amount":12,"__is_add":false,"available":30200,"threshold":30000,"verify":10},{"type":0,"level":3,"name":"Case","filterValue":"5","GTIN":"50343547276039","amount":2,"controlCode":"12345-01","__is_add":false,"available":1048,"threshold":2500,"verify":10},{"type":1,"level":4,"name":"Pallet","filterValue":"7","GTIN":"0343547","amount":1,"controlCode":"54321-01","__is_add":false,"partialFilterValue":"5","partialThreshold":"300","partialControlCode":"12345-01","available":1946,"threshold":200,"verify":10},{"type":1,"level":5,"name":"Partial Case","filterValue":"5","GTIN":"0343547","controlCode":"12345-01","__is_add":false,"available":2871,"threshold":300,"verify":10}]
2025-07-21 20:35:22.505  INFO [聚合] 固定包装规则JSON解析成功
2025-07-21 20:35:22.506 ERROR [聚合] 固定包装规则解析失败: invalid type: integer `0`, expected a string, 规则值: Object {"GTIN": String("00343547276034"), "__is_add": Bool(false), "amount": Number(12), "available": Number(30200), "filterValue": String("0"), "level": Number(1), "name": String("Primary"), "threshold": Number(30000), "type": Number(0), "verify": Number(10)}
2025-07-21 20:35:22.506 ERROR [聚合] 固定包装规则解析失败: invalid type: integer `0`, expected a string, 规则值: Object {"GTIN": String("50343547276039"), "__is_add": Bool(false), "amount": Number(2), "available": Number(1048), "controlCode": String("12345-01"), "filterValue": String("5"), "level": Number(3), "name": String("Case"), "threshold": Number(2500), "type": Number(0), "verify": Number(10)}
2025-07-21 20:35:22.506 ERROR [聚合] 固定包装规则解析失败: invalid type: integer `1`, expected a string, 规则值: Object {"GTIN": String("0343547"), "__is_add": Bool(false), "amount": Number(1), "available": Number(1946), "controlCode": String("54321-01"), "filterValue": String("7"), "level": Number(4), "name": String("Pallet"), "partialControlCode": String("12345-01"), "partialFilterValue": String("5"), "partialThreshold": String("300"), "threshold": Number(200), "type": Number(1), "verify": Number(10)}
2025-07-21 20:35:22.507 ERROR [聚合] 固定包装规则解析失败: invalid type: integer `1`, expected a string, 规则值: Object {"GTIN": String("0343547"), "__is_add": Bool(false), "available": Number(2871), "controlCode": String("12345-01"), "filterValue": String("5"), "level": Number(5), "name": String("Partial Case"), "threshold": Number(300), "type": Number(1), "verify": Number(10)}
2025-07-21 20:35:22.507 ERROR [聚合] 固定包装规则也无法解析
2025-07-21 20:35:22.507 ERROR [聚合] |放入| 验证箱数限制失败: 未能成功解析产品包装规则
2025-07-21 20:38:52.235  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 20:38:52.236  INFO 数据库连接管理器创建成功
2025-07-21 20:38:52.287  INFO 数据库连接池创建成功
2025-07-21 20:38:52.294  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 20:40:45.603  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 20:40:45.605  INFO 数据库连接管理器创建成功
2025-07-21 20:40:45.651  INFO 数据库连接池创建成功
2025-07-21 20:40:45.659  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 20:49:23.786  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 20:49:23.789  INFO 数据库连接管理器创建成功
2025-07-21 20:49:23.833  INFO 数据库连接池创建成功
2025-07-21 20:49:23.840  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 20:50:45.740  INFO [聚合] 开始聚合操作流程
2025-07-21 20:50:45.740  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-21 20:50:45.784  INFO [聚合] 数据库连接获取成功
2025-07-21 20:50:45.785  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-21 20:50:45.802  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-21 20:50:45.802  INFO [聚合] 开始检查产品每托最大箱数，产品编码: 50190500001
2025-07-21 20:50:45.810  INFO [聚合] 查询到产品信息: id=3, productCode=50190500001
2025-07-21 20:50:45.811  INFO [聚合] 产品原始包装规则字段: [{"type":0,"level":1,"name":"Primary","filterValue":"0","GTIN":"00343547276034","amount":12,"__is_add":false,"available":30200,"threshold":30000,"verify":10},{"type":0,"level":3,"name":"Case","filterValue":"5","GTIN":"50343547276039","amount":2,"controlCode":"12345-01","__is_add":false,"available":1048,"threshold":2500,"verify":10},{"type":1,"level":4,"name":"Pallet","filterValue":"7","GTIN":"0343547","amount":1,"controlCode":"54321-01","__is_add":false,"partialFilterValue":"5","partialThreshold":"300","partialControlCode":"12345-01","available":1946,"threshold":200,"verify":10},{"type":1,"level":5,"name":"Partial Case","filterValue":"5","GTIN":"0343547","controlCode":"12345-01","__is_add":false,"available":2871,"threshold":300,"verify":10}]
2025-07-21 20:50:45.812  INFO [聚合] 产品包装规则: [Object {"GTIN": String("00343547276034"), "__is_add": Bool(false), "amount": Number(12), "available": Number(30200), "filterValue": String("0"), "level": Number(1), "name": String("Primary"), "threshold": Number(30000), "type": Number(0), "verify": Number(10)}, Object {"GTIN": String("50343547276039"), "__is_add": Bool(false), "amount": Number(2), "available": Number(1048), "controlCode": String("12345-01"), "filterValue": String("5"), "level": Number(3), "name": String("Case"), "threshold": Number(2500), "type": Number(0), "verify": Number(10)}, Object {"GTIN": String("0343547"), "__is_add": Bool(false), "amount": Number(1), "available": Number(1946), "controlCode": String("54321-01"), "filterValue": String("7"), "level": Number(4), "name": String("Pallet"), "partialControlCode": String("12345-01"), "partialFilterValue": String("5"), "partialThreshold": String("300"), "threshold": Number(200), "type": Number(1), "verify": Number(10)}, Object {"GTIN": String("0343547"), "__is_add": Bool(false), "available": Number(2871), "controlCode": String("12345-01"), "filterValue": String("5"), "level": Number(5), "name": String("Partial Case"), "threshold": Number(300), "type": Number(1), "verify": Number(10)}]
2025-07-21 20:50:45.812  INFO [聚合] 将type字段从整数0转换为字符串
2025-07-21 20:50:45.812  INFO [聚合] 解析包装规则成功: level=1, name=Primary, amount=12, type=Some("0")
2025-07-21 20:50:45.813  INFO [聚合] 将type字段从整数0转换为字符串
2025-07-21 20:50:45.813  INFO [聚合] 解析包装规则成功: level=3, name=Case, amount=2, type=Some("0")
2025-07-21 20:50:45.813  INFO [聚合] 将type字段从整数1转换为字符串
2025-07-21 20:50:45.813  INFO [聚合] 解析包装规则成功: level=4, name=Pallet, amount=1, type=Some("1")
2025-07-21 20:50:45.814  INFO [聚合] 将type字段从整数1转换为字符串
2025-07-21 20:50:45.814  INFO [聚合] 解析包装规则成功: level=5, name=Partial Case, amount=0, type=Some("1")
2025-07-21 20:50:45.814  INFO [聚合] 产品每托最大箱数: 2
2025-07-21 20:50:46.944  INFO [聚合] |放入| 托盘当前箱数: 1
2025-07-21 20:50:46.945  INFO [聚合] |放入| 箱数验证通过: 当前=1, 扫描=1, 总数=2, 最大=2
2025-07-21 20:50:46.947  INFO [聚合] 箱数验证成功完成
2025-07-21 20:50:46.948  INFO [聚合] |放入| 箱数限制验证通过，开始处理箱码列表
2025-07-21 20:50:46.948  INFO [聚合] |放入| 处理箱码 1/1: 015034354727603921100000127123
2025-07-21 20:50:46.955  INFO [聚合] |放入| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 20:50:46.955  INFO [聚合] |放入| 开始执行放入操作
2025-07-21 20:50:46.956  INFO [聚合] |放入| 处理箱码: 015034354727603921100000127123
2025-07-21 20:50:46.956  INFO [聚合] |放入| 开始执行更新瓶码SQL: 
                UPDATE transit_code
                SET palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 20:50:46'
                WHERE levelCode = 1
                  AND codeFlag = 2
                  AND boxCode = '015034354727603921100000127123'
            
2025-07-21 20:50:47.248  INFO [聚合] |放入| 更新瓶码信息成功
2025-07-21 20:50:47.248  INFO [聚合] |放入| 开始执行更新箱码SQL: 
                UPDATE transit_code
                SET parentCode = '00703435470002290235',
                    palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 20:51:46'
                WHERE levelCode = 3
                  AND codeFlag = 2
                  AND code = '015034354727603921100000127123'
            
2025-07-21 20:50:47.254  INFO [聚合] |放入| 更新箱码信息成功
2025-07-21 20:50:47.254  INFO [聚合] |放入| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount + 1,
                updateTime = '2025-07-21 20:53:46'
            WHERE levelCode = 4
              AND codeFlag = 2
              AND code = '00703435470002290235'
        
2025-07-21 20:50:47.261  INFO [聚合] |放入| 更新托盘码信息成功
2025-07-21 20:50:47.263  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-21 20:58:43.064  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 20:58:43.065  INFO 数据库连接管理器创建成功
2025-07-21 20:58:43.101  INFO 数据库连接池创建成功
2025-07-21 20:58:43.108  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 21:01:49.134  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 21:01:49.135  INFO 数据库连接管理器创建成功
2025-07-21 21:01:49.175  INFO 数据库连接池创建成功
2025-07-21 21:01:49.182  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 21:02:08.811  INFO [查询序列码] 开始查询序列码信息
2025-07-21 21:02:08.853  INFO [查询序列码] 数据库连接获取成功
2025-07-21 21:02:08.872  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 21:02:08.878  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 21:02:08.878  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 21:02:09.076  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 21:02:09.077  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 21:02:11.054  INFO [取出] 开始取出操作流程
2025-07-21 21:02:11.054  INFO [取出] 接收到请求参数: code=015034354727603921100000127123
2025-07-21 21:02:11.069  INFO [取出] 数据库连接获取成功
2025-07-21 21:02:11.069  INFO [取出] 开始查询序列码: 015034354727603921100000127123
2025-07-21 21:02:11.073  INFO [取出] 查询到序列码信息: code_flag=2, level_code=3, zero_box=0, batch_no=R202500040
2025-07-21 21:02:11.073  INFO [取出] 序列码状态正常: code_flag=2
2025-07-21 21:02:11.074  INFO [取出] 开始判断箱码是否符合取出条件
2025-07-21 21:02:11.075  INFO [取出] 大箱关联的托盘码: 00703435470002290235
2025-07-21 21:02:11.076  INFO [取出] 开始从托盘中取出箱码
2025-07-21 21:02:11.076  INFO [取出] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount - 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-21 21:02:11.082  INFO [取出] 更新托盘码信息成功
2025-07-21 21:02:11.095  INFO [取出] 开始执行解除箱码与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '015034354727603921100000127123'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-21 21:02:11.102  INFO [取出] 解除箱码与托盘的绑定成功
2025-07-21 21:02:11.103  INFO [取出] 开始执行解除小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '015034354727603921100000127123'
              AND boxCode = '015034354727603921100000127123'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-21 21:02:11.126  INFO [取出] 解除小瓶与托盘的绑定成功
2025-07-21 21:02:11.128  INFO [取出] 事务提交成功，取出操作完成
2025-07-21 21:02:27.230  INFO [查询序列码] 开始查询序列码信息
2025-07-21 21:02:27.241  INFO [查询序列码] 数据库连接获取成功
2025-07-21 21:02:27.242  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 21:02:27.244  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 21:02:27.244  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 21:02:27.502  INFO [查询序列码] 托盘内箱码数量: 1
2025-07-21 21:02:28.055  INFO [查询序列码] 托盘内瓶码数量: 6
2025-07-21 21:02:28.055  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 21:02:32.340  INFO [查询序列码] 开始查询序列码信息
2025-07-21 21:02:32.350  INFO [查询序列码] 数据库连接获取成功
2025-07-21 21:02:32.353  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 21:02:32.355  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 21:02:32.355  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 21:02:32.477  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 21:02:32.478  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 21:02:35.195  INFO [聚合] 开始聚合操作流程
2025-07-21 21:02:35.196  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-21 21:02:35.207  INFO [聚合] 数据库连接获取成功
2025-07-21 21:02:35.208  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-21 21:02:35.211  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-21 21:02:35.214  INFO [聚合] 开始检查产品每托最大箱数，产品编码: 50190500001
2025-07-21 21:02:35.220  INFO [聚合] 产品每托最大箱数: 2
2025-07-21 21:02:35.356  INFO [聚合] |放入| 托盘当前箱数: 1
2025-07-21 21:02:35.356  INFO [聚合] |放入| 箱数验证通过: 当前=1, 扫描=1, 总数=2, 最大=2
2025-07-21 21:02:35.358  INFO [聚合] 箱数验证成功完成
2025-07-21 21:02:35.359  INFO [聚合] |放入| 箱数限制验证通过，开始处理箱码列表
2025-07-21 21:02:35.359  INFO [聚合] |放入| 处理箱码 1/1: 015034354727603921100000127123
2025-07-21 21:02:35.360  INFO [聚合] |放入| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 21:02:35.361  INFO [聚合] |放入| 开始执行放入操作
2025-07-21 21:02:35.361  INFO [聚合] |放入| 处理箱码: 015034354727603921100000127123
2025-07-21 21:02:35.362  INFO [聚合] |放入| 开始执行更新瓶码SQL: 
                UPDATE transit_code
                SET palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 21:02:35'
                WHERE levelCode = 1
                  AND codeFlag = 2
                  AND boxCode = '015034354727603921100000127123'
            
2025-07-21 21:02:35.512  INFO [聚合] |放入| 更新瓶码信息成功
2025-07-21 21:02:35.512  INFO [聚合] |放入| 开始执行更新箱码SQL: 
                UPDATE transit_code
                SET parentCode = '00703435470002290235',
                    palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 21:03:35'
                WHERE levelCode = 3
                  AND codeFlag = 2
                  AND code = '015034354727603921100000127123'
            
2025-07-21 21:02:35.517  INFO [聚合] |放入| 更新箱码信息成功
2025-07-21 21:02:35.518  INFO [聚合] |放入| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount + 1,
                updateTime = '2025-07-21 21:05:35'
            WHERE levelCode = 4
              AND codeFlag = 2
              AND code = '00703435470002290235'
        
2025-07-21 21:02:35.522  INFO [聚合] |放入| 更新托盘码信息成功
2025-07-21 21:02:35.523  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-21 21:05:28.109  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 21:05:28.111  INFO 数据库连接管理器创建成功
2025-07-21 21:05:28.145  INFO 数据库连接池创建成功
2025-07-21 21:05:28.152  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 21:06:26.982  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 21:06:26.984  INFO 数据库连接管理器创建成功
2025-07-21 21:06:27.024  INFO 数据库连接池创建成功
2025-07-21 21:06:27.032  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 21:07:06.420  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 21:07:06.421  INFO 数据库连接管理器创建成功
2025-07-21 21:07:06.457  INFO 数据库连接池创建成功
2025-07-21 21:07:06.462  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 21:47:43.861  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 21:47:43.863  INFO 数据库连接管理器创建成功
2025-07-21 21:47:43.898  INFO 数据库连接池创建成功
2025-07-21 21:47:43.903  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 21:47:48.884  INFO [查询序列码] 开始查询序列码信息
2025-07-21 21:47:48.958  INFO [查询序列码] 数据库连接获取成功
2025-07-21 21:47:48.960  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 21:47:48.962  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 21:47:48.962  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 21:47:49.095  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 21:47:49.095  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 21:47:50.868  INFO [取出] 开始取出操作流程
2025-07-21 21:47:50.869  INFO [取出] 接收到请求参数: code=015034354727603921100000127123
2025-07-21 21:47:50.880  INFO [取出] 数据库连接获取成功
2025-07-21 21:47:50.883  INFO [取出] 开始查询序列码: 015034354727603921100000127123
2025-07-21 21:47:50.885  INFO [取出] 查询到序列码信息: code_flag=2, level_code=3, zero_box=0, batch_no=R202500040
2025-07-21 21:47:50.888  INFO [取出] 序列码状态正常: code_flag=2
2025-07-21 21:47:50.892  INFO [取出] 开始判断箱码是否符合取出条件
2025-07-21 21:47:50.895  INFO [取出] 大箱关联的托盘码: 00703435470002290235
2025-07-21 21:47:50.896  INFO [取出] 开始从托盘中取出箱码
2025-07-21 21:47:50.902  INFO [取出] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount - 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-21 21:47:50.905  INFO [取出] 更新托盘码信息成功
2025-07-21 21:47:50.905  INFO [取出] 开始执行解除箱码与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '015034354727603921100000127123'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-21 21:47:50.907  INFO [取出] 解除箱码与托盘的绑定成功
2025-07-21 21:47:50.907  INFO [取出] 开始执行解除小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '015034354727603921100000127123'
              AND boxCode = '015034354727603921100000127123'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-21 21:47:50.926  INFO [取出] 解除小瓶与托盘的绑定成功
2025-07-21 21:47:50.927  INFO [取出] 事务提交成功，取出操作完成
2025-07-21 21:48:18.921  INFO [查询序列码] 开始查询序列码信息
2025-07-21 21:48:18.933  INFO [查询序列码] 数据库连接获取成功
2025-07-21 21:48:18.936  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 21:48:18.938  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 21:48:18.938  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 21:48:19.079  INFO [查询序列码] 托盘内箱码数量: 1
2025-07-21 21:48:19.386  INFO [查询序列码] 托盘内瓶码数量: 6
2025-07-21 21:48:19.388  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 21:48:23.161  INFO [查询序列码] 开始查询序列码信息
2025-07-21 21:48:23.174  INFO [查询序列码] 数据库连接获取成功
2025-07-21 21:48:23.177  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 21:48:23.180  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 21:48:23.182  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 21:48:23.303  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 21:48:23.304  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 21:48:28.739  INFO [聚合] 开始聚合操作流程
2025-07-21 21:48:28.741  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-21 21:48:28.758  INFO [聚合] 数据库连接获取成功
2025-07-21 21:48:28.759  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-21 21:48:28.760  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-21 21:48:28.761  INFO [聚合] 开始检查产品每托最大箱数，产品编码: 50190500001
2025-07-21 21:48:28.763  INFO [聚合] 解析到产品每托最大箱数: 2
2025-07-21 21:48:29.033  INFO [聚合] |放入| 托盘当前箱数: 1
2025-07-21 21:48:29.033  INFO [聚合] |放入| 箱数验证通过: 当前=1, 扫描=1, 总数=2, 最大=2
2025-07-21 21:48:29.035  INFO [聚合] 箱数限制验证通过
2025-07-21 21:48:29.036  INFO [聚合] |放入| 处理箱码 1/1: 015034354727603921100000127124
2025-07-21 21:48:29.038  INFO [聚合] |放入| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 21:48:29.039  INFO [聚合] |放入| 开始执行放入操作
2025-07-21 21:48:29.040  INFO [聚合] |放入| 处理箱码: 015034354727603921100000127124
2025-07-21 21:48:29.041  INFO [聚合] |放入| 开始执行更新瓶码SQL: 
                UPDATE transit_code
                SET palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 21:48:29'
                WHERE levelCode = 1
                  AND codeFlag = 2
                  AND boxCode = '015034354727603921100000127124'
            
2025-07-21 21:48:29.186  INFO [聚合] |放入| 更新瓶码信息成功
2025-07-21 21:48:29.186  INFO [聚合] |放入| 开始执行更新箱码SQL: 
                UPDATE transit_code
                SET parentCode = '00703435470002290235',
                    palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 21:49:29'
                WHERE levelCode = 3
                  AND codeFlag = 2
                  AND code = '015034354727603921100000127124'
            
2025-07-21 21:48:29.189  INFO [聚合] |放入| 更新箱码信息成功
2025-07-21 21:48:29.190  INFO [聚合] |放入| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount + 1,
                updateTime = '2025-07-21 21:51:29'
            WHERE levelCode = 4
              AND codeFlag = 2
              AND code = '00703435470002290235'
        
2025-07-21 21:48:29.192  INFO [聚合] |放入| 更新托盘码信息成功
2025-07-21 21:48:29.193  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-21 21:50:40.734  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 21:50:40.735  INFO 数据库连接管理器创建成功
2025-07-21 21:50:40.773  INFO 数据库连接池创建成功
2025-07-21 21:50:40.779  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 21:58:03.488  INFO [查询序列码] 开始查询序列码信息
2025-07-21 21:58:03.532  INFO [查询序列码] 数据库连接获取成功
2025-07-21 21:58:03.535  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 21:58:03.537  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 21:58:03.538  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 21:58:03.663  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 21:58:03.664  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 21:58:05.811  INFO [查询序列码] 开始查询序列码信息
2025-07-21 21:58:05.821  INFO [查询序列码] 数据库连接获取成功
2025-07-21 21:58:05.826  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 21:58:05.829  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 21:58:05.829  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 21:58:05.970  INFO [查询序列码] 托盘内箱码数量: 2
2025-07-21 21:58:06.341  INFO [查询序列码] 托盘内瓶码数量: 18
2025-07-21 21:58:06.343  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 21:58:25.148  INFO [查询序列码] 开始查询序列码信息
2025-07-21 21:58:25.159  INFO [查询序列码] 数据库连接获取成功
2025-07-21 21:58:25.162  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 21:58:25.164  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 21:58:25.165  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 21:58:25.284  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 21:58:25.285  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 21:58:30.394  INFO [查询序列码] 开始查询序列码信息
2025-07-21 21:58:30.403  INFO [查询序列码] 数据库连接获取成功
2025-07-21 21:58:30.406  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 21:58:30.408  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 21:58:30.409  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 21:58:30.525  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 21:58:30.526  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 21:58:32.570  INFO [取出] 开始取出操作流程
2025-07-21 21:58:32.571  INFO [取出] 接收到请求参数: code=015034354727603921100000127124
2025-07-21 21:58:32.582  INFO [取出] 数据库连接获取成功
2025-07-21 21:58:32.582  INFO [取出] 开始查询序列码: 015034354727603921100000127124
2025-07-21 21:58:32.584  INFO [取出] 查询到序列码信息: code_flag=2, level_code=3, zero_box=0, batch_no=R202500040
2025-07-21 21:58:32.584  INFO [取出] 序列码状态正常: code_flag=2
2025-07-21 21:58:32.586  INFO [取出] 开始判断箱码是否符合取出条件
2025-07-21 21:58:32.586  INFO [取出] 大箱关联的托盘码: 00703435470002290235
2025-07-21 21:58:32.587  INFO [取出] 开始从托盘中取出箱码
2025-07-21 21:58:32.587  INFO [取出] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount - 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-21 21:58:32.589  INFO [取出] 更新托盘码信息成功
2025-07-21 21:58:32.589  INFO [取出] 开始执行解除箱码与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '015034354727603921100000127124'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-21 21:58:32.591  INFO [取出] 解除箱码与托盘的绑定成功
2025-07-21 21:58:32.593  INFO [取出] 开始执行解除小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '015034354727603921100000127124'
              AND boxCode = '015034354727603921100000127124'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-21 21:58:32.611  INFO [取出] 解除小瓶与托盘的绑定成功
2025-07-21 21:58:32.614  INFO [取出] 事务提交成功，取出操作完成
2025-07-21 21:58:40.296  INFO [查询序列码] 开始查询序列码信息
2025-07-21 21:58:40.307  INFO [查询序列码] 数据库连接获取成功
2025-07-21 21:58:40.310  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 21:58:40.312  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 21:58:40.312  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 21:58:40.456  INFO [查询序列码] 托盘内箱码数量: 1
2025-07-21 21:58:40.766  INFO [查询序列码] 托盘内瓶码数量: 6
2025-07-21 21:58:40.767  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 21:58:59.066  INFO [查询序列码] 开始查询序列码信息
2025-07-21 21:58:59.076  INFO [查询序列码] 数据库连接获取成功
2025-07-21 21:58:59.079  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 21:58:59.081  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 21:58:59.081  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 21:58:59.200  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 21:58:59.201  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 21:59:02.664  INFO [聚合] 开始聚合操作流程
2025-07-21 21:59:02.665  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-21 21:59:02.676  INFO [聚合] 数据库连接获取成功
2025-07-21 21:59:02.677  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-21 21:59:02.681  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-21 21:59:02.681  INFO [聚合] 开始检查产品每托最大箱数，产品编码: 50190500001
2025-07-21 21:59:02.683  INFO [聚合] 解析到产品每托最大箱数: 2
2025-07-21 21:59:02.935  INFO [聚合] |放入| 托盘当前箱数: 1
2025-07-21 21:59:02.936  INFO [聚合] |放入| 箱数限制验证通过: 当前=1, 扫描=1, 总数=2, 最大=2
2025-07-21 21:59:02.937  INFO [聚合] |放入| 处理箱码 1/1: 015034354727603921100000127123
2025-07-21 21:59:02.938  INFO [聚合] |放入| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 21:59:02.939  INFO [聚合] |放入| 开始执行放入操作
2025-07-21 21:59:02.939  INFO [聚合] |放入| 处理箱码: 015034354727603921100000127123
2025-07-21 21:59:02.940  INFO [聚合] |放入| 开始执行更新瓶码SQL: 
                UPDATE transit_code
                SET palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 21:59:02'
                WHERE levelCode = 1
                  AND codeFlag = 2
                  AND boxCode = '015034354727603921100000127123'
            
2025-07-21 21:59:03.080  INFO [聚合] |放入| 更新瓶码信息成功
2025-07-21 21:59:03.081  INFO [聚合] |放入| 开始执行更新箱码SQL: 
                UPDATE transit_code
                SET parentCode = '00703435470002290235',
                    palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 22:00:02'
                WHERE levelCode = 3
                  AND codeFlag = 2
                  AND code = '015034354727603921100000127123'
            
2025-07-21 21:59:03.082  INFO [聚合] |放入| 更新箱码信息成功
2025-07-21 21:59:03.082  INFO [聚合] |放入| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount + 1,
                updateTime = '2025-07-21 22:02:02'
            WHERE levelCode = 4
              AND codeFlag = 2
              AND code = '00703435470002290235'
        
2025-07-21 21:59:03.083  INFO [聚合] |放入| 更新托盘码信息成功
2025-07-21 21:59:03.085  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-21 21:59:13.472  INFO [查询序列码] 开始查询序列码信息
2025-07-21 21:59:13.483  INFO [查询序列码] 数据库连接获取成功
2025-07-21 21:59:13.487  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 21:59:13.489  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 21:59:13.490  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 21:59:13.606  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 21:59:13.607  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 22:00:21.259  INFO [查询序列码] 开始查询序列码信息
2025-07-21 22:00:21.272  INFO [查询序列码] 数据库连接获取成功
2025-07-21 22:00:21.275  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 22:00:21.277  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 22:00:21.280  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 22:00:21.414  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 22:00:21.414  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 22:00:24.706  INFO [聚合] 开始聚合操作流程
2025-07-21 22:00:24.707  INFO [聚合] 接收到请求参数: aggregate_type=newPackage, box_codes数量=1
2025-07-21 22:00:24.717  INFO [聚合] 数据库连接获取成功
2025-07-21 22:00:24.719  INFO [聚合] |新建包装| 开始执行新建包装模式
2025-07-21 22:00:24.719  INFO [聚合] |新建包装| 处理箱码 1/1: 015034354727603921100000127124
2025-07-21 22:00:24.721  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 22:00:24.722  INFO [聚合] 开始检查产品每托最大箱数，产品编码: 50190500001
2025-07-21 22:00:24.723  INFO [聚合] 解析到产品每托最大箱数: 2
2025-07-21 22:00:24.724  INFO [聚合] |新建包装| 箱数限制验证通过: 扫描=1, 最大=2
2025-07-21 22:00:24.724  INFO [聚合] |新建包装| 箱数限制验证通过，开始执行新建包装操作
2025-07-21 22:00:24.725  INFO [聚合] |新建包装| 开始执行新建包装操作
2025-07-21 22:00:24.727  INFO [取码函数] 查询生产订单信息SQL: SELECT TOP 1 id, productId, poNumber, batchNo FROM [produce_order] WHERE id = '4'
2025-07-21 22:00:24.732  INFO [取码函数] 查询产品信息SQL: SELECT TOP 1 id, productCode, packageRules, codeStyle FROM [basic_product] WHERE id = '3'
2025-07-21 22:00:24.746  INFO [取码函数] 查询序列码信息SQL: SELECT TOP 1 tc.id, tc.code, tc.snCode, tc.fileID 
        FROM [transit_code] tc
        JOIN [sn_record] sr ON sr.id = tc.fileID
        WHERE tc.orderId = '4' 
        AND tc.levelCode = '4' 
        AND tc.typeFlag = '1' 
        AND tc.codeFlag IN (0, 1)
        AND sr.filterValue = '7'
        AND sr.resCode = '0343547'
        AND sr.typeFlag = '1'
        AND sr.codeStyle = 'tracelink'
        ORDER BY tc.id
2025-07-21 22:00:24.768  INFO [取码函数] 数据库中符合条件的序列码数量: 1
2025-07-21 22:00:24.768  INFO [聚合] |新建包装| 获取托盘码结果: {"data": Array [String("00703435470002290242")], "errcode": Number(0), "errmsg": String("序列码获取成功")}
2025-07-21 22:00:24.769  INFO [聚合] |新建包装| 获取托盘码数据: [String("00703435470002290242")]
2025-07-21 22:00:24.770  INFO [聚合] |新建包装| 获取到托盘码: 00703435470002290242
2025-07-21 22:00:24.771  INFO [聚合] |新建包装| 开始执行更新瓶码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290242',
                updateTime = '2025-07-21 22:00:24'
            WHERE levelCode = 1
              AND codeFlag = 2
              AND boxCode IN ('015034354727603921100000127124')
        
2025-07-21 22:00:24.915  INFO [聚合] |新建包装| 更新瓶码信息成功
2025-07-21 22:00:24.917  INFO [聚合] |新建包装| 开始执行更新箱码SQL: 
            UPDATE transit_code
            SET parentCode = '00703435470002290242',
                palletCode = '00703435470002290242',
                updateTime = '2025-07-21 22:01:24'
            WHERE levelCode = 3
              AND codeFlag = 2
              AND code IN ('015034354727603921100000127124')
        
2025-07-21 22:00:24.926  INFO [聚合] |新建包装| 更新箱码信息成功
2025-07-21 22:00:24.927  INFO [聚合] |新建包装| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290242',
                amount = 1,
                codeFlag = 2,
                updateTime = '2025-07-21 22:05:24'
            WHERE levelCode = 4
              AND code = '00703435470002290242'
        
2025-07-21 22:00:24.931  INFO [聚合] |新建包装| 更新托盘码信息成功
2025-07-21 22:00:24.932  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-21 22:03:38.046  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-21 22:03:38.047  INFO 数据库连接管理器创建成功
2025-07-21 22:03:38.082  INFO 数据库连接池创建成功
2025-07-21 22:03:38.087  INFO 服务器运行在 0.0.0.0:3000
2025-07-21 22:03:42.448  INFO [查询序列码] 开始查询序列码信息
2025-07-21 22:03:42.495  INFO [查询序列码] 数据库连接获取成功
2025-07-21 22:03:42.498  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 22:03:42.501  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 22:03:42.501  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 22:03:42.618  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 22:03:42.619  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
