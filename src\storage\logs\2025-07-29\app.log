2025-07-29 09:28:08.960  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-29 09:28:08.960  INFO 数据库连接管理器创建成功
2025-07-29 09:28:08.996  INFO 数据库连接池创建成功
2025-07-29 09:28:09.001  INFO 服务器运行在 0.0.0.0:3000
2025-07-29 09:28:11.585  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-29 09:28:11.587  INFO 数据库连接管理器创建成功
2025-07-29 09:28:11.615  INFO 数据库连接池创建成功
2025-07-29 09:28:11.620  INFO 服务器运行在 0.0.0.0:3000
2025-07-29 10:22:57.884  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-29 10:22:57.886  INFO 数据库连接管理器创建成功
2025-07-29 10:22:57.925  INFO 数据库连接池创建成功
2025-07-29 10:22:57.931  INFO 服务器运行在 0.0.0.0:3000
2025-07-29 10:23:09.689  INFO [查询序列码] 开始查询序列码信息
2025-07-29 10:23:09.723  INFO [查询序列码] 数据库连接获取成功
2025-07-29 10:23:09.736  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-29 10:23:09.740  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-29 10:23:09.740  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-29 10:23:09.930  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-29 10:23:09.931  INFO [查询序列码] 查询序列码信息完成: code=015034354734703621100000045720, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-29 10:24:17.894  INFO [查询序列码] 开始查询序列码信息
2025-07-29 10:24:17.905  INFO [查询序列码] 数据库连接获取成功
2025-07-29 10:24:17.908  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-29 10:24:17.909  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-29 10:24:17.910  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-29 10:24:17.910  INFO [查询序列码] 瓶码不需要查询数量
2025-07-29 10:24:17.911  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356703, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-29 10:24:30.608  INFO [查询序列码] 开始查询序列码信息
2025-07-29 10:24:30.618  INFO [查询序列码] 数据库连接获取成功
2025-07-29 10:24:30.621  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-29 10:24:30.623  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-29 10:24:30.623  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-29 10:24:30.624  INFO [查询序列码] 瓶码不需要查询数量
2025-07-29 10:24:30.625  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356743, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-29 11:20:17.065  INFO [拆箱] 开始拆箱操作流程
2025-07-29 11:20:17.065  INFO [拆箱] 接收到请求参数: case=015034354734703621100000045720, codes=["010034354734703121100002356703", "010034354734703121100002356743"]
2025-07-29 11:20:17.067  INFO [拆箱] 发送HTTP请求到拆箱接口: {"case":"015034354734703621100000045720","codes":["010034354734703121100002356703","010034354734703121100002356743"]}
2025-07-29 11:20:17.067 DEBUG starting new connection: http://localhost:8000/    
2025-07-29 11:20:17.068 DEBUG proxy(http://127.0.0.1:7897) intercepts 'http://localhost:8000/'    
2025-07-29 11:20:17.068 DEBUG connecting to 127.0.0.1:7897
2025-07-29 11:20:17.069 DEBUG connected to 127.0.0.1:7897
2025-07-29 11:20:17.070 DEBUG flushed 272 bytes
2025-07-29 11:20:17.711 DEBUG parsed 10 headers
2025-07-29 11:20:17.712 DEBUG incoming body is chunked encoding
2025-07-29 11:20:17.712 DEBUG incoming chunked header: 0x359 (857 bytes)
2025-07-29 11:20:17.713 DEBUG incoming body completed
2025-07-29 11:20:17.713  INFO [拆箱] 拆箱成功，响应数据: {"code":0,"data":[{"EXP":"06/08/2028","MDF":"06/09/2025","amount":2,"barCode":"(00)503435470000873900","batchNo":"2025060901","case":"00503435470000873900","control_code":"204967-01","flag":1,"lot":"2025060901","make_merchant":"Manufactured by: Zhejiang Huahai Pharmaceutical co., Ltd.Zhejiang 317024,China","materialCode":"501989","ndc":"43547-347-03","productName":"PAROXETINE TABLETS, USP","type":"CASE"},{"EXP":"06/08/2028","MDF":"06/09/2025","amount":22,"barCode":"(00)503435470000873917","batchNo":"2025060901","case":"00503435470000873917","control_code":"204967-01","flag":1,"lot":"2025060901","make_merchant":"Manufactured by: Zhejiang Huahai Pharmaceutical co., Ltd.Zhejiang 317024,China","materialCode":"501989","ndc":"43547-347-03","productName":"PAROXETINE TABLETS, USP","type":"CASE"}],"message":"大箱拆零成功"}
2025-07-29 15:28:55.035  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-29 15:28:55.036  INFO 数据库连接管理器创建成功
2025-07-29 15:28:55.070  INFO 数据库连接池创建成功
2025-07-29 15:28:55.075  INFO 服务器运行在 0.0.0.0:3000
2025-07-29 15:31:20.379  INFO [网络检测] 网络连接完成，耗时: 36ms
2025-07-29 15:31:25.195  INFO [数据同步] 开始获取各表数据条数
2025-07-29 15:31:25.207  INFO [数据同步] 数据库连接获取成功
2025-07-29 15:31:25.242  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
