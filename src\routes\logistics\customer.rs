use crate::app::http::controllers::logistics::CustomerController;
use crate::app::providers::DatabaseServiceProvider;
use axum::{routing::any, Router};
use std::sync::Arc;

/// 客户管理路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().merge(
        Router::new()
            .route("/customer/page", any(CustomerController::customer_page))
            .route("/customer/list", any(CustomerController::customer_list))
            .route("/customer/add", any(CustomerController::customer_add))
            .route("/customer/edit", any(CustomerController::customer_edit))
            .route("/customer/del", any(CustomerController::customer_del)),
    )
}
