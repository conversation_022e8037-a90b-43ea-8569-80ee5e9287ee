use crate::app::models::basic::product::Product;
use crate::app::models::transit::code::Code;
use crate::app::utils::analyze::{Analyzer, PackageRule};
use crate::app::utils::assistant;
use futures_util::TryStreamExt;
use tiberius::Client;
use tokio::net::TcpStream;
use tokio_util::compat::Compat;
use tracing::{error, info};

/// PDA返工管理核心服务
pub struct IndustryService;

impl IndustryService {
    /// 解绑托盘下所有大箱及相关瓶码与托盘的关联关系
    pub async fn unbind_pallet_associations(
        conn: &mut Client<Compat<TcpStream>>,
        code_row: &Code,
        pallet_code: &str,
    ) -> Result<(), String> {
        info!("[解绑] 开始解绑托盘下所有大箱及相关瓶码与托盘的关联关系");

        // 更新托盘码信息，将托盘重置为初始状态
        let update_pallet_sql = r#"
            UPDATE transit_code
            SET amount = 0,
                parentCode = NULL,
                boxCode = NULL,
                palletCode = NULL,
                codeFlag = 1
            WHERE orderId = @P1
              AND batchNo = @P2
              AND code = @P3
              AND levelCode = 4
              AND codeFlag = 2
        "#;

        // 记录实际执行的SQL
        let log_pallet_sql = update_pallet_sql
            .replace("@P1", &format!("'{}'", code_row.order_id))
            .replace("@P2", &format!("'{}'", code_row.batch_no))
            .replace("@P3", &format!("'{}'", pallet_code));
        info!("[解绑] 开始执行更新托盘码SQL: {}", log_pallet_sql);

        if let Err(e) = conn
            .execute(
                update_pallet_sql,
                &[&code_row.order_id, &code_row.batch_no, &pallet_code],
            )
            .await
        {
            error!("[解绑] 更新托盘码信息失败: {}", e);
            return Err(format!("更新托盘码信息失败: {}", e));
        }
        info!("[解绑] 更新托盘码信息成功");

        // 更新大箱码信息，解除与托盘的绑定
        let update_box_sql = r#"
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = @P1
              AND batchNo = @P2
              AND parentCode = @P3
              AND palletCode = @P4
              AND levelCode = 3
              AND codeFlag = 2
        "#;

        // 记录实际执行的SQL
        let log_box_sql = update_box_sql
            .replace("@P1", &format!("'{}'", code_row.order_id))
            .replace("@P2", &format!("'{}'", code_row.batch_no))
            .replace("@P3", &format!("'{}'", pallet_code))
            .replace("@P4", &format!("'{}'", pallet_code));
        info!(
            "[解绑] 开始执行解除托盘下所有大箱与托盘的绑定SQL: {}",
            log_box_sql
        );

        if let Err(e) = conn
            .execute(
                update_box_sql,
                &[
                    &code_row.order_id,
                    &code_row.batch_no,
                    &pallet_code,
                    &pallet_code,
                ],
            )
            .await
        {
            error!("[解绑] 解除托盘下所有大箱与托盘的绑定失败: {}", e);
            return Err(format!("解除托盘下所有大箱与托盘的绑定失败: {}", e));
        }
        info!("[解绑] 解除托盘下所有大箱与托盘的绑定成功");

        // 更新小瓶码信息，解除与托盘的绑定
        let update_bottles_sql = r#"
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = @P1
              AND batchNo = @P2
              AND palletCode = @P3
              AND levelCode = 1
              AND codeFlag = 2
        "#;

        // 记录实际执行的SQL
        let log_bottles_sql = update_bottles_sql
            .replace("@P1", &format!("'{}'", code_row.order_id))
            .replace("@P2", &format!("'{}'", code_row.batch_no))
            .replace("@P3", &format!("'{}'", pallet_code));
        info!(
            "[解绑] 开始执行解除托盘下所有小瓶与托盘的绑定SQL: {}",
            log_bottles_sql
        );

        if let Err(e) = conn
            .execute(
                update_bottles_sql,
                &[&code_row.order_id, &code_row.batch_no, &pallet_code],
            )
            .await
        {
            error!("[解绑] 解除托盘下所有小瓶与托盘的绑定失败: {}", e);
            return Err(format!("解除托盘下所有小瓶与托盘的绑定失败: {}", e));
        }
        info!("[解绑] 解除托盘下所有小瓶与托盘的绑定成功");

        Ok(())
    }

    /// 从托盘中取出箱码
    pub async fn extract_box_from_pallet(
        conn: &mut Client<Compat<TcpStream>>,
        code_row: &Code,
        pallet_code: &str,
    ) -> Result<(), String> {
        info!("[取出] 开始从托盘中取出箱码");

        // 更新托盘码信息，减少数量
        let update_pallet_sql = r#"
            UPDATE transit_code
            SET amount = amount - 1
            WHERE orderId = @P1
              AND batchNo = @P2
              AND code = @P3
              AND levelCode = 4
              AND codeFlag = 2
        "#;

        // 记录实际执行的SQL
        let log_pallet_sql = update_pallet_sql
            .replace("@P1", &format!("'{}'", code_row.order_id))
            .replace("@P2", &format!("'{}'", code_row.batch_no))
            .replace("@P3", &format!("'{}'", pallet_code));
        info!("[取出] 开始执行更新托盘码SQL: {}", log_pallet_sql);

        if let Err(e) = conn
            .execute(
                update_pallet_sql,
                &[&code_row.order_id, &code_row.batch_no, &pallet_code],
            )
            .await
        {
            error!("[取出] 更新托盘码信息失败: {}", e);
            return Err(format!("更新托盘码信息失败: {}", e));
        }
        info!("[取出] 更新托盘码信息成功");

        // 更新大箱码信息，解除与托盘的绑定
        let update_box_sql = r#"
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = @P1
              AND batchNo = @P2
              AND code = @P3
              AND levelCode = 3
              AND codeFlag = 2
        "#;

        // 记录实际执行的SQL
        let log_box_sql = update_box_sql
            .replace("@P1", &format!("'{}'", code_row.order_id))
            .replace("@P2", &format!("'{}'", code_row.batch_no))
            .replace("@P3", &format!("'{}'", code_row.code));
        info!("[取出] 开始执行解除箱码与托盘的绑定SQL: {}", log_box_sql);

        if let Err(e) = conn
            .execute(
                update_box_sql,
                &[&code_row.order_id, &code_row.batch_no, &code_row.code],
            )
            .await
        {
            error!("[取出] 解除箱码与托盘的绑定失败: {}", e);
            return Err(format!("解除箱码与托盘的绑定失败: {}", e));
        }
        info!("[取出] 解除箱码与托盘的绑定成功");

        // 更新小瓶码信息，解除与托盘的绑定
        let update_bottles_sql = r#"
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = @P1
              AND batchNo = @P2
              AND parentCode = @P3
              AND boxCode = @P4
              AND palletCode = @P5
              AND levelCode = 1
              AND codeFlag = 2
        "#;

        // 记录实际执行的SQL
        let log_bottles_sql = update_bottles_sql
            .replace("@P1", &format!("'{}'", code_row.order_id))
            .replace("@P2", &format!("'{}'", code_row.batch_no))
            .replace("@P3", &format!("'{}'", code_row.code))
            .replace("@P4", &format!("'{}'", code_row.code))
            .replace("@P5", &format!("'{}'", pallet_code));
        info!(
            "[取出] 开始执行解除小瓶与托盘的绑定SQL: {}",
            log_bottles_sql
        );

        if let Err(e) = conn
            .execute(
                update_bottles_sql,
                &[
                    &code_row.order_id,
                    &code_row.batch_no,
                    &code_row.code,
                    &code_row.code,
                    &pallet_code,
                ],
            )
            .await
        {
            error!("[取出] 解除小瓶与托盘的绑定失败: {}", e);
            return Err(format!("解除小瓶与托盘的绑定失败: {}", e));
        }
        info!("[取出] 解除小瓶与托盘的绑定成功");

        Ok(())
    }

    /// 从箱中取出瓶码
    pub async fn extract_bottle_from_box(
        conn: &mut Client<Compat<TcpStream>>,
        bottle_code: &Code,
        box_code: &str,
    ) -> Result<(), String> {
        info!("[取出] 开始从大箱中取出瓶码: 大箱码={}", box_code);

        // 查询大箱码信息
        let box_sql = r#"
            SELECT id, dataID, poNumber, productCode, orderId, batchNo, levelCode, levelName, 
                   resCode, snCode, code, amount, parentCode, boxCode, palletCode, typeFlag, reqID, 
                   codeFlag, boxFlag, estate, CONVERT(varchar(19), createTime, 120) as createTime, 
                   CONVERT(varchar(19), updateTime, 120) as updateTime, fileID, helperCode, zeroBox, 
                   qualityLevel, field1, field2, boxSn
            FROM transit_code WITH (NOLOCK)
            WHERE code = @P1 AND levelCode = 3 AND codeFlag = 2
        "#;

        let log_box_sql = box_sql.replace("@P1", &format!("'{}'", box_code));
        info!("[取出] 开始执行查询大箱码信息SQL: {}", log_box_sql);

        // 查询大箱码信息
        let box_row = match conn.query(box_sql, &[&box_code]).await {
            Ok(mut stream) => {
                let mut found = false;
                let mut box_result = None;

                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        found = true;
                        let box_info = Code::renovation(&row);
                        info!("[取出] 查询到大箱码信息: zero_box={}", box_info.zero_box);
                        box_result = Some(box_info);
                        break;
                    }
                }

                if found {
                    box_result.unwrap()
                } else {
                    error!("[取出] 未查询到大箱码信息");
                    return Err("未查询到大箱码信息".to_string());
                }
            }
            Err(e) => {
                error!("[取出] 查询大箱码失败: {}", e);
                return Err(format!("查询大箱码失败: {}", e));
            }
        };

        // 判断所属大箱是否为零箱
        if box_row.zero_box == 1 {
            info!("[取出] 大箱为零箱，直接更新");

            // 更新大箱
            let update_box_sql = r#"
                UPDATE transit_code 
                SET amount = amount - 1 
                WHERE orderId = @P1 AND batchNo = @P2 AND levelCode = 3 
                AND codeFlag = 2 AND code = @P3
            "#;

            let log_box_sql = update_box_sql
                .replace("@P1", &format!("'{}'", bottle_code.order_id))
                .replace("@P2", &format!("'{}'", bottle_code.batch_no))
                .replace("@P3", &format!("'{}'", box_code));
            info!("[取出] 开始执行更新大箱SQL: {}", log_box_sql);

            if let Err(e) = conn
                .execute(
                    update_box_sql,
                    &[&bottle_code.order_id, &bottle_code.batch_no, &box_code],
                )
                .await
            {
                error!("[取出] 更新大箱信息失败: {}", e);
                return Err(format!("更新大箱信息失败: {}", e));
            }
            info!("[取出] 更新大箱信息成功");

            // 更新小瓶
            let update_bottle_sql = r#"
                UPDATE transit_code 
                SET parentCode = NULL, boxCode = NULL, codeFlag = 1 
                WHERE orderId = @P1 AND batchNo = @P2 AND levelCode = 1 
                AND codeFlag = 2 AND code = @P3
            "#;

            let log_bottle_sql = update_bottle_sql
                .replace("@P1", &format!("'{}'", bottle_code.order_id))
                .replace("@P2", &format!("'{}'", bottle_code.batch_no))
                .replace("@P3", &format!("'{}'", bottle_code.code));
            info!("[取出] 开始执行更新小瓶SQL: {}", log_bottle_sql);

            if let Err(e) = conn
                .execute(
                    update_bottle_sql,
                    &[
                        &bottle_code.order_id,
                        &bottle_code.batch_no,
                        &bottle_code.code,
                    ],
                )
                .await
            {
                error!("[取出] 更新小瓶信息失败: {}", e);
                return Err(format!("更新小瓶信息失败: {}", e));
            }
            info!("[取出] 更新小瓶信息成功");
        } else {
            info!("[取出] 大箱为整箱，需获取零箱码");

            // 通过assistant获取零箱码
            let zero_box_code_result = assistant::get_code(bottle_code.order_id, 3, 1, 1).await;

            // 检查结果
            let errcode = zero_box_code_result
                .get("errcode")
                .and_then(|v| v.as_i64())
                .unwrap_or(1);
            let errmsg = zero_box_code_result
                .get("errmsg")
                .and_then(|v| v.as_str())
                .unwrap_or("未知错误");

            info!("[取出] 解析取码函数响应信息: {:?}", zero_box_code_result);

            // 检查errcode
            if errcode != 0 {
                error!(
                    "[取出] 解析取码函数响应信息失败: errcode={}, errmsg={}",
                    errcode, errmsg
                );
                return Err(format!("解析取码函数响应信息失败: {}", errmsg));
            }

            let data = match zero_box_code_result.get("data").and_then(|v| v.as_array()) {
                Some(array) => {
                    info!("[取出] 解析取码函数响应信息的数据内容成功: {:?}", array);
                    array.clone()
                }
                None => {
                    error!("[取出] 解析取码函数响应信息的数据内容失败");
                    Vec::new()
                }
            };

            // 检查data数组是否为空
            if data.is_empty() {
                error!("[取出] 解析取码函数响应信息的数据内容失败: 数据为空");
                return Err("解析取码函数响应信息的数据内容失败: 数据为空".to_string());
            }

            // 获取零箱码
            info!(
                "[取出] 解析取码函数响应信息的数据内容中零箱码: {:?}",
                data[0]
            );
            let zero_box_code = if data[0].is_string() {
                data[0].as_str().unwrap_or("").to_string()
            } else {
                error!(
                    "[取出] 解析取码函数响应信息的数据内容中零箱码失败: {:?}",
                    data[0]
                );
                data[0]
                    .get("code")
                    .and_then(|v| v.as_str())
                    .unwrap_or("")
                    .to_string()
            };

            // 检查零箱码是否为空
            if zero_box_code.is_empty() {
                error!("[取出] 解析取码函数响应信息的数据内容中零箱码为空");
                return Err("解析取码函数响应信息的数据内容中零箱码为空".to_string());
            }

            info!(
                "[取出] 解析取码函数响应信息的数据内容中零箱码: {}",
                zero_box_code
            );

            // 获取当前大箱码数量
            let box_amount = box_row.amount;

            info!("[取出] 开始执行更新操作");

            // 1. 更新当前小瓶
            let update_current_bottle_sql = r#"
                UPDATE transit_code 
                SET amount = 0, parentCode = NULL, boxCode = NULL, codeFlag = 1 
                WHERE orderId = @P1 AND batchNo = @P2 AND levelCode = 1 
                AND codeFlag = 2 AND code = @P3
            "#;

            let log_current_bottle_sql = update_current_bottle_sql
                .replace("@P1", &format!("'{}'", bottle_code.order_id))
                .replace("@P2", &format!("'{}'", bottle_code.batch_no))
                .replace("@P3", &format!("'{}'", bottle_code.code));
            info!("[取出] 开始执行更新当前小瓶SQL: {}", log_current_bottle_sql);

            if let Err(e) = conn
                .execute(
                    update_current_bottle_sql,
                    &[
                        &bottle_code.order_id,
                        &bottle_code.batch_no,
                        &bottle_code.code,
                    ],
                )
                .await
            {
                error!("[取出] 当前小瓶更新失败: {}", e);
                return Err(format!("当前小瓶更新失败: {}", e));
            }
            info!("[取出] 当前小瓶更新成功");

            // 2. 更新零箱码
            let update_zero_box_sql = r#"
                UPDATE transit_code 
                SET amount = @P4, 
                    parentCode = @P5, 
                    palletCode = @P6, 
                    boxCode = @P3,
                    codeFlag = 2, 
                    updateTime = @P7 
                WHERE orderId = @P1 AND batchNo = @P2 AND levelCode = 3 
                AND code = @P3
            "#;

            let new_amount = box_amount - 1;

            let log_zero_box_sql = update_zero_box_sql
                .replace("@P1", &format!("'{}'", bottle_code.order_id))
                .replace("@P2", &format!("'{}'", bottle_code.batch_no))
                .replace("@P3", &format!("'{}'", zero_box_code))
                .replace("@P4", &format!("{}", new_amount));
            info!("[取出] 开始执行更新零箱码SQL: {}", log_zero_box_sql);

            if let Err(e) = conn
                .execute(
                    update_zero_box_sql,
                    &[
                        &bottle_code.order_id,
                        &bottle_code.batch_no,
                        &zero_box_code,
                        &new_amount,
                    ],
                )
                .await
            {
                error!("[取出] 零箱码更新失败: {}", e);
                return Err(format!("零箱码更新失败: {}", e));
            }
            info!("[取出] 零箱码更新成功");

            // 3. 更新之前大箱码
            let update_old_box_sql = r#"
                UPDATE transit_code 
                SET amount = 0, parentCode = NULL, boxCode = NULL, palletCode = NULL, codeFlag = 1 
                WHERE orderId = @P1 AND batchNo = @P2 AND levelCode = 3 
                AND codeFlag = 2 AND code = @P3
            "#;

            let log_old_box_sql = update_old_box_sql
                .replace("@P1", &format!("'{}'", bottle_code.order_id))
                .replace("@P2", &format!("'{}'", bottle_code.batch_no))
                .replace("@P3", &format!("'{}'", box_code));
            info!("[取出] 开始执行更新原大箱SQL: {}", log_old_box_sql);

            if let Err(e) = conn
                .execute(
                    update_old_box_sql,
                    &[&bottle_code.order_id, &bottle_code.batch_no, &box_code],
                )
                .await
            {
                error!("[取出] 原大箱更新失败: {}", e);
                return Err(format!("原大箱更新失败: {}", e));
            }
            info!("[取出] 原大箱信息更新成功");

            // 4. 替换大箱内所有小瓶关联的大箱码为零箱码
            let update_bottles_sql = r#"
                UPDATE transit_code 
                SET parentCode = @P4, boxCode = @P4 
                WHERE orderId = @P1 AND batchNo = @P2 AND levelCode = 1 
                AND codeFlag = 2 AND parentCode = @P3 AND boxCode = @P3
            "#;

            let log_bottles_sql = update_bottles_sql
                .replace("@P1", &format!("'{}'", bottle_code.order_id))
                .replace("@P2", &format!("'{}'", bottle_code.batch_no))
                .replace("@P3", &format!("'{}'", box_code))
                .replace("@P4", &format!("'{}'", zero_box_code));
            info!(
                "[取出] 开始执行替换小瓶关联的大箱码SQL: {}",
                log_bottles_sql
            );

            if let Err(e) = conn
                .execute(
                    update_bottles_sql,
                    &[
                        &bottle_code.order_id,
                        &bottle_code.batch_no,
                        &box_code,
                        &zero_box_code,
                    ],
                )
                .await
            {
                error!("[取出] 替换小瓶关联的大箱码失败: {}", e);
                return Err(format!("替换小瓶关联的大箱码失败: {}", e));
            }
            info!("[取出] 替换小瓶关联的大箱码成功");
        }

        info!("[取出] 从大箱中取出瓶码成功");
        Ok(())
    }

    /// 查询箱码信息
    pub async fn get_box_info(
        conn: &mut Client<Compat<TcpStream>>,
        box_code: &str,
    ) -> Result<(Option<String>, Option<String>), String> {
        let box_sql = r#"
            SELECT parentCode, palletCode
            FROM transit_code WITH (NOLOCK)
            WHERE code = @P1
              AND levelCode = 3
              AND codeFlag = 2
        "#;

        // 记录实际执行的SQL（将参数合并到SQL中）
        let log_box_sql = box_sql.replace("@P1", &format!("'{}'", box_code));
        info!("[解绑] 开始执行查询箱码信息SQL: {}", log_box_sql);

        let mut box_parent_code: Option<String> = None;
        let mut box_pallet_code: Option<String> = None;

        match conn.query(box_sql, &[&box_code]).await {
            Ok(mut stream) => {
                if let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        box_parent_code = row.get::<&str, _>("parentCode").map(|s| s.to_string());
                        box_pallet_code = row.get::<&str, _>("palletCode").map(|s| s.to_string());
                    }
                }
                info!(
                    "[解绑] 查询箱码信息成功: parent_code={:?}, pallet_code={:?}",
                    box_parent_code, box_pallet_code
                );
                Ok((box_parent_code, box_pallet_code))
            }
            Err(e) => {
                error!("[解绑] 查询箱码信息失败: {}", e);
                Err(format!("查询箱码信息失败: {}", e))
            }
        }
    }

    /// 处理托盘码的取样/报废
    pub async fn process_pallet_sampling_scrap(
        conn: &mut Client<Compat<TcpStream>>,
        code_row: &Code,
        status_code: i32,
    ) -> Result<(String, Option<String>), String> {
        info!("[取样★报废] 开始处理托盘码及其下所有关联码的操作");

        // 更新托盘及下面大箱、瓶码的信息
        let update_sql = r#"
            UPDATE transit_code 
            SET codeFlag = @P1 
            WHERE orderId = @P2 
              AND batchNo = @P3 
              AND codeFlag = 2 
              AND palletCode = @P4
        "#;

        // 记录实际执行的SQL
        let log_sql = update_sql
            .replace("@P1", &status_code.to_string())
            .replace("@P2", &format!("'{}'", code_row.order_id))
            .replace("@P3", &format!("'{}'", code_row.batch_no))
            .replace("@P4", &format!("'{}'", code_row.code));
        info!("[取样★报废] 开始执行更新托盘及关联码SQL: {}", log_sql);

        if let Err(e) = conn
            .execute(
                update_sql,
                &[
                    &status_code,
                    &code_row.order_id,
                    &code_row.batch_no,
                    &code_row.code,
                ],
            )
            .await
        {
            error!("[取样★报废] 更新托盘及关联码信息失败: {}", e);
            return Err(format!("更新托盘及关联码信息失败: {}", e));
        }

        info!("[取样★报废] 更新托盘及关联码信息成功");
        Ok((String::new(), None))
    }

    /// 处理箱码的取样/报废
    pub async fn process_box_sampling_scrap(
        conn: &mut Client<Compat<TcpStream>>,
        code_row: &Code,
        status_code: i32,
        has_pallet: bool,
    ) -> Result<(), String> {
        info!("[取样★报废] 开始处理箱码及其中瓶码的操作");

        if has_pallet {
            // 如果关联了托盘码，需要更新托盘信息
            let pallet_code = code_row.pallet_code.as_ref().unwrap();

            // 更新托盘信息，减少数量
            let update_pallet_sql = r#"
                UPDATE transit_code 
                SET amount = amount - 1 
                WHERE orderId = @P1 
                  AND batchNo = @P2 
                  AND codeFlag = 2 
                  AND code = @P3
            "#;

            // 记录实际执行的SQL
            let log_pallet_sql = update_pallet_sql
                .replace("@P1", &format!("'{}'", code_row.order_id))
                .replace("@P2", &format!("'{}'", code_row.batch_no))
                .replace("@P3", &format!("'{}'", pallet_code.as_str()));
            info!("[取样★报废] 开始执行更新托盘码SQL: {}", log_pallet_sql);

            if let Err(e) = conn
                .execute(
                    update_pallet_sql,
                    &[
                        &code_row.order_id,
                        &code_row.batch_no,
                        &pallet_code.as_str(),
                    ],
                )
                .await
            {
                error!("[取样★报废] 更新托盘码信息失败: {}", e);
                return Err(format!("更新托盘码信息失败: {}", e));
            }
            info!("[取样★报废] 更新托盘码信息成功");
        }

        // 更新箱码及其中的瓶码信息
        let update_box_sql = r#"
            UPDATE transit_code 
            SET codeFlag = @P1 
            WHERE orderId = @P2 
              AND batchNo = @P3 
              AND codeFlag = 2 
              AND boxCode = @P4
        "#;

        // 记录实际执行的SQL
        let log_box_sql = update_box_sql
            .replace("@P1", &status_code.to_string())
            .replace("@P2", &format!("'{}'", code_row.order_id))
            .replace("@P3", &format!("'{}'", code_row.batch_no))
            .replace("@P4", &format!("'{}'", code_row.code));
        info!("[取样★报废] 开始执行更新箱码及瓶码SQL: {}", log_box_sql);

        if let Err(e) = conn
            .execute(
                update_box_sql,
                &[
                    &status_code,
                    &code_row.order_id,
                    &code_row.batch_no,
                    &code_row.code,
                ],
            )
            .await
        {
            error!("[取样★报废] 更新箱码及瓶码信息失败: {}", e);
            return Err(format!("更新箱码及瓶码信息失败: {}", e));
        }

        info!("[取样★报废] 更新箱码及瓶码信息成功");
        Ok(())
    }

    /// 处理箱码的取样/报废（带托盘）
    pub async fn process_box_with_pallet_sampling_scrap(
        conn: &mut Client<Compat<TcpStream>>,
        code_row: &Code,
        status_code: i32,
        pallet_code: &str,
    ) -> Result<(String, Option<String>), String> {
        info!(
            "[取样★报废] 开始处理带托盘的{}箱码及其中瓶码的操作",
            if code_row.zero_box == 1 { "零" } else { "整" }
        );

        // 更新托盘信息，减少数量
        let update_pallet_sql = r#"
            UPDATE transit_code 
            SET amount = amount - 1 
            WHERE orderId = @P1 
              AND batchNo = @P2 
              AND codeFlag = 2 
              AND code = @P3
        "#;

        // 记录实际执行的SQL
        let log_pallet_sql = update_pallet_sql
            .replace("@P1", &format!("'{}'", code_row.order_id))
            .replace("@P2", &format!("'{}'", code_row.batch_no))
            .replace("@P3", &format!("'{}'", pallet_code));
        info!("[取样★报废] 开始执行更新托盘码SQL: {}", log_pallet_sql);

        if let Err(e) = conn
            .execute(
                update_pallet_sql,
                &[&code_row.order_id, &code_row.batch_no, &pallet_code],
            )
            .await
        {
            error!("[取样★报废] 更新托盘码信息失败: {}", e);
            return Err(format!("更新托盘码信息失败: {}", e));
        }
        info!("[取样★报废] 更新托盘码信息成功");

        // 更新箱码及其中的瓶码信息
        let update_box_sql = r#"
            UPDATE transit_code 
            SET codeFlag = @P1 
            WHERE orderId = @P2 
              AND batchNo = @P3 
              AND codeFlag = 2 
              AND boxCode = @P4
        "#;

        // 记录实际执行的SQL
        let log_box_sql = update_box_sql
            .replace("@P1", &status_code.to_string())
            .replace("@P2", &format!("'{}'", code_row.order_id))
            .replace("@P3", &format!("'{}'", code_row.batch_no))
            .replace("@P4", &format!("'{}'", code_row.code));
        info!("[取样★报废] 开始执行更新箱码及瓶码SQL: {}", log_box_sql);

        if let Err(e) = conn
            .execute(
                update_box_sql,
                &[
                    &status_code,
                    &code_row.order_id,
                    &code_row.batch_no,
                    &code_row.code,
                ],
            )
            .await
        {
            error!("[取样★报废] 更新箱码及瓶码信息失败: {}", e);
            return Err(format!("更新箱码及瓶码信息失败: {}", e));
        }

        info!("[取样★报废] 更新箱码及瓶码信息成功");
        Ok((pallet_code.to_string(), None))
    }

    /// 处理箱码的取样/报废（不带托盘）
    pub async fn process_box_without_pallet_sampling_scrap(
        conn: &mut Client<Compat<TcpStream>>,
        code_row: &Code,
        status_code: i32,
    ) -> Result<(String, Option<String>), String> {
        info!(
            "[取样★报废] 开始处理不带托盘的{}箱码及其中瓶码的操作",
            if code_row.zero_box == 1 { "零" } else { "整" }
        );

        // 更新箱码及其中的瓶码信息
        let update_box_sql = r#"
            UPDATE transit_code 
            SET codeFlag = @P1 
            WHERE orderId = @P2 
              AND batchNo = @P3 
              AND codeFlag = 2 
              AND boxCode = @P4
        "#;

        // 记录实际执行的SQL
        let log_box_sql = update_box_sql
            .replace("@P1", &status_code.to_string())
            .replace("@P2", &format!("'{}'", code_row.order_id))
            .replace("@P3", &format!("'{}'", code_row.batch_no))
            .replace("@P4", &format!("'{}'", code_row.code));
        info!("[取样★报废] 开始执行更新箱码及瓶码SQL: {}", log_box_sql);

        if let Err(e) = conn
            .execute(
                update_box_sql,
                &[
                    &status_code,
                    &code_row.order_id,
                    &code_row.batch_no,
                    &code_row.code,
                ],
            )
            .await
        {
            error!("[取样★报废] 更新箱码及瓶码信息失败: {}", e);
            return Err(format!("更新箱码及瓶码信息失败: {}", e));
        }

        info!("[取样★报废] 更新箱码及瓶码信息成功");
        Ok((String::new(), None))
    }

    /// 处理瓶码的取样/报废
    pub async fn process_bottle_sampling_scrap(
        conn: &mut Client<Compat<TcpStream>>,
        code_row: &Code,
        status_code: i32,
    ) -> Result<(String, Option<String>), String> {
        info!("[取样★报废] 开始处理瓶码的操作");

        // 检查瓶码是否关联了大箱码
        let has_box = code_row.box_code.as_ref().map_or(false, |b| !b.is_empty());
        let has_parent = code_row
            .parent_code
            .as_ref()
            .map_or(false, |p| !p.is_empty());
        let parent_eq_box = if has_parent && has_box {
            code_row.parent_code.as_ref().unwrap() == code_row.box_code.as_ref().unwrap()
        } else {
            false
        };

        // 根据瓶码关联状态进行处理
        if has_box && has_parent && parent_eq_box {
            // 瓶码关联了大箱，处理大箱相关逻辑
            Self::process_bottle_with_box(conn, code_row, status_code).await
        } else {
            // 瓶码没有关联大箱，直接更新瓶码状态
            Self::update_bottle_status(conn, code_row, status_code).await?;
            Ok((String::new(), None))
        }
    }

    /// 处理关联了大箱的瓶码
    async fn process_bottle_with_box(
        conn: &mut Client<Compat<TcpStream>>,
        code_row: &Code,
        status_code: i32,
    ) -> Result<(String, Option<String>), String> {
        // 获取大箱码
        let box_code = code_row.box_code.as_ref().unwrap().as_str();

        // 查询大箱码信息
        let box_row = Self::query_box_info(conn, box_code).await?;

        // 根据大箱类型处理
        if box_row.zero_box == 1 {
            // 大箱为零箱，直接更新大箱数量和瓶码状态
            Self::process_bottle_with_zero_box(conn, code_row, box_code, status_code).await?;
            let pallet_code = code_row
                .pallet_code
                .as_ref()
                .map_or(String::new(), |p| p.clone());
            Ok((pallet_code, Some(box_code.to_string())))
        } else {
            // 大箱为整箱，需要获取零箱码并进行复杂处理
            Self::process_bottle_with_full_box(conn, code_row, &box_row, status_code).await
        }
    }

    /// 查询大箱码信息
    async fn query_box_info(
        conn: &mut Client<Compat<TcpStream>>,
        box_code: &str,
    ) -> Result<Code, String> {
        let box_sql = r#"
                SELECT id, dataID, poNumber, productCode, orderId, batchNo, levelCode, levelName, 
                       resCode, snCode, code, amount, parentCode, boxCode, palletCode, typeFlag, reqID, 
                       codeFlag, boxFlag, estate, CONVERT(varchar(19), createTime, 120) as createTime, 
                       CONVERT(varchar(19), updateTime, 120) as updateTime, fileID, helperCode, zeroBox, 
                       qualityLevel, field1, field2, boxSn
                FROM transit_code WITH (NOLOCK)
                WHERE code = @P1 AND levelCode = 3 AND codeFlag = 2
            "#;

        let log_box_sql = box_sql.replace("@P1", &format!("'{}'", box_code));
        info!("[取样★报废] 开始执行查询大箱码信息SQL: {}", log_box_sql);

        match conn.query(box_sql, &[&box_code]).await {
            Ok(mut stream) => {
                let mut found = false;
                let mut box_result = None;

                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        found = true;
                        let box_info = Code::renovation(&row);
                        info!(
                            "[取样★报废] 查询到大箱码信息: zero_box={}",
                            box_info.zero_box
                        );
                        box_result = Some(box_info);
                        break;
                    }
                }

                if found {
                    Ok(box_result.unwrap())
                } else {
                    error!("[取样★报废] 未查询到大箱码信息");
                    Err("未查询到大箱码信息".to_string())
                }
            }
            Err(e) => {
                error!("[取样★报废] 查询大箱码失败: {}", e);
                Err(format!("查询大箱码失败: {}", e))
            }
        }
    }

    /// 处理瓶码关联零箱的情况
    async fn process_bottle_with_zero_box(
        conn: &mut Client<Compat<TcpStream>>,
        code_row: &Code,
        box_code: &str,
        status_code: i32,
    ) -> Result<(), String> {
        // 1. 更新大箱信息，减少数量
        let update_box_sql = r#"
            UPDATE transit_code 
            SET amount = amount - 1 
            WHERE orderId = @P1 
              AND batchNo = @P2 
              AND codeFlag = 2 
              AND code = @P3
        "#;

        let log_box_sql = update_box_sql
            .replace("@P1", &format!("'{}'", code_row.order_id))
            .replace("@P2", &format!("'{}'", code_row.batch_no))
            .replace("@P3", &format!("'{}'", box_code));
        info!("[取样★报废] 开始执行更新零箱码SQL: {}", log_box_sql);

        if let Err(e) = conn
            .execute(
                update_box_sql,
                &[&code_row.order_id, &code_row.batch_no, &box_code],
            )
            .await
        {
            error!("[取样★报废] 更新零箱码信息失败: {}", e);
            return Err(format!("更新零箱码信息失败: {}", e));
        }
        info!("[取样★报废] 更新零箱码信息成功");

        // 2. 更新瓶码状态
        Self::update_bottle_status(conn, code_row, status_code).await
    }

    /// 处理瓶码关联整箱的情况
    async fn process_bottle_with_full_box(
        conn: &mut Client<Compat<TcpStream>>,
        code_row: &Code,
        box_row: &Code,
        status_code: i32,
    ) -> Result<(String, Option<String>), String> {
        // 1. 获取零箱码
        let zero_box_code = Self::get_zero_box_code(code_row.order_id).await?;

        // 2. 更新瓶码状态
        Self::update_bottle_status(conn, code_row, status_code).await?;

        // 3. 更新零箱码
        let new_amount = box_row.amount - 1;
        let update_time_str = box_row.update_time.format("%Y-%m-%d %H:%M:%S");

        let update_zero_box_sql = r#"
                    UPDATE transit_code 
                    SET amount = @P4, 
                        parentCode = @P5, 
                        palletCode = @P6, 
                        boxCode = @P3,
                        codeFlag = 2, 
                        updateTime = @P7 
                    WHERE orderId = @P1 AND batchNo = @P2 AND levelCode = 3 
                    AND code = @P3
                "#;

        let log_zero_box_sql = update_zero_box_sql
            .replace("@P1", &format!("'{}'", code_row.order_id))
            .replace("@P2", &format!("'{}'", code_row.batch_no))
            .replace("@P3", &format!("'{}'", zero_box_code))
            .replace("@P4", &new_amount.to_string())
            .replace(
                "@P5",
                &format!(
                    "'{}'",
                    box_row.parent_code.as_ref().unwrap_or(&"".to_string())
                ),
            )
            .replace(
                "@P6",
                &format!(
                    "'{}'",
                    box_row.pallet_code.as_ref().unwrap_or(&"".to_string())
                ),
            )
            .replace("@P7", &format!("'{}'", update_time_str));
        info!("[取样★报废] 开始执行更新零箱码SQL: {}", log_zero_box_sql);

        if let Err(e) = conn
            .execute(
                update_zero_box_sql,
                &[
                    &code_row.order_id,
                    &code_row.batch_no,
                    &zero_box_code,
                    &new_amount,
                    &box_row.parent_code.as_deref(),
                    &box_row.pallet_code.as_deref(),
                    &update_time_str,
                ],
            )
            .await
        {
            error!("[取样★报废] 零箱码更新失败: {}", e);
            return Err(format!("零箱码更新失败: {}", e));
        }
        info!("[取样★报废] 零箱码更新成功");

        // 4. 更新原大箱状态
        let box_code = box_row.code.as_str();
        let update_old_box_sql = r#"
                    UPDATE transit_code 
                    SET amount = 0, parentCode = NULL, boxCode = NULL, palletCode = NULL, codeFlag = 1 
                    WHERE orderId = @P1 AND batchNo = @P2 AND levelCode = 3 
                    AND codeFlag = 2 AND code = @P3
                "#;

        let log_old_box_sql = update_old_box_sql
            .replace("@P1", &format!("'{}'", code_row.order_id))
            .replace("@P2", &format!("'{}'", code_row.batch_no))
            .replace("@P3", &format!("'{}'", box_code));
        info!("[取样★报废] 开始执行更新原大箱SQL: {}", log_old_box_sql);

        if let Err(e) = conn
            .execute(
                update_old_box_sql,
                &[&code_row.order_id, &code_row.batch_no, &box_code],
            )
            .await
        {
            error!("[取样★报废] 更新原大箱信息失败: {}", e);
            return Err(format!("更新原大箱信息失败: {}", e));
        }
        info!("[取样★报废] 更新原大箱信息成功");

        // 5. 替换大箱内所有瓶码关联
        let update_bottles_sql = r#"
                    UPDATE transit_code 
                    SET parentCode = @P1, boxCode = @P2 
                    WHERE orderId = @P3 AND batchNo = @P4 AND levelCode = 1 
                    AND codeFlag = 2 AND parentCode = @P5 AND boxCode = @P6
                "#;

        let log_bottles_sql = update_bottles_sql
            .replace("@P1", &format!("'{}'", zero_box_code))
            .replace("@P2", &format!("'{}'", zero_box_code))
            .replace("@P3", &format!("'{}'", code_row.order_id))
            .replace("@P4", &format!("'{}'", code_row.batch_no))
            .replace("@P5", &format!("'{}'", box_code))
            .replace("@P6", &format!("'{}'", box_code));
        info!("[取样★报废] 开始执行更新瓶码关联SQL: {}", log_bottles_sql);

        if let Err(e) = conn
            .execute(
                update_bottles_sql,
                &[
                    &zero_box_code,
                    &zero_box_code,
                    &code_row.order_id,
                    &code_row.batch_no,
                    &box_code,
                    &box_code,
                ],
            )
            .await
        {
            error!("[取样★报废] 更新瓶码关联信息失败: {}", e);
            return Err(format!("更新瓶码关联信息失败: {}", e));
        }
        info!("[取样★报废] 更新瓶码关联信息成功");

        let pallet_code = code_row
            .pallet_code
            .as_ref()
            .map_or(String::new(), |p| p.clone());
        Ok((pallet_code, Some(zero_box_code)))
    }

    /// 获取零箱码
    async fn get_zero_box_code(order_id: i32) -> Result<String, String> {
        // 通过assistant获取零箱码
        let zero_box_code_result = assistant::get_code(order_id, 3, 1, 1).await;

        // 检查结果
        let errcode = zero_box_code_result
            .get("errcode")
            .and_then(|v| v.as_i64())
            .unwrap_or(1);
        let errmsg = zero_box_code_result
            .get("errmsg")
            .and_then(|v| v.as_str())
            .unwrap_or("未知错误");

        info!(
            "[取样★报废] 解析取码函数响应信息: {:?}",
            zero_box_code_result
        );

        // 检查errcode
        if errcode != 0 {
            error!(
                "[取样★报废] 解析取码函数响应信息失败: errcode={}, errmsg={}",
                errcode, errmsg
            );
            return Err(format!("解析取码函数响应信息失败: {}", errmsg));
        }

        let data = match zero_box_code_result.get("data").and_then(|v| v.as_array()) {
            Some(array) => {
                info!("[取样★报废] 解析取码函数响应信息的数据内容: {:?}", array);
                array.clone()
            }
            None => {
                error!("[取样★报废] 解析取码函数响应信息的数据内容失败");
                Vec::new()
            }
        };

        // 检查data数组是否为空
        if data.is_empty() {
            error!("[取样★报废] 解析取码函数响应信息的数据内容失败: 零箱码数组为空");
            return Err("解析取码函数响应信息的数据内容失败: 零箱码数组为空".to_string());
        }

        // 获取零箱码
        info!(
            "[取样★报废] 解析取码函数响应信息的数据内容中零箱码: {:?}",
            data[0]
        );
        let zero_box_code = if data[0].is_string() {
            data[0].as_str().unwrap_or("").to_string()
        } else {
            error!(
                "[取样★报废] 解析取码函数响应信息的数据内容中零箱码失败: {:?}",
                data[0]
            );
            data[0]
                .get("code")
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string()
        };

        // 检查零箱码是否为空
        if zero_box_code.is_empty() {
            error!("[取样★报废] 解析取码函数响应信息的数据内容中零箱码为空");
            return Err("获取零箱码失败: 解析取码函数响应信息的数据内容中零箱码为空".to_string());
        }

        info!(
            "[取样★报废] 解析取码函数响应信息的数据内容中零箱码成功: {}",
            zero_box_code
        );

        Ok(zero_box_code)
    }

    /// 更新瓶码状态
    async fn update_bottle_status(
        conn: &mut Client<Compat<TcpStream>>,
        code_row: &Code,
        status_code: i32,
    ) -> Result<(), String> {
        let update_bottle_sql = r#"
                UPDATE transit_code 
                SET codeFlag = @P1 
                WHERE orderId = @P2 
                  AND batchNo = @P3 
                  AND codeFlag = 2 
                  AND code = @P4
            "#;

        let log_bottle_sql = update_bottle_sql
            .replace("@P1", &status_code.to_string())
            .replace("@P2", &format!("'{}'", code_row.order_id))
            .replace("@P3", &format!("'{}'", code_row.batch_no))
            .replace("@P4", &format!("'{}'", code_row.code));
        info!("[取样★报废] 开始执行更新瓶码SQL: {}", log_bottle_sql);

        if let Err(e) = conn
            .execute(
                update_bottle_sql,
                &[
                    &status_code,
                    &code_row.order_id,
                    &code_row.batch_no,
                    &code_row.code,
                ],
            )
            .await
        {
            error!("[取样★报废] 更新瓶码信息失败: {}", e);
            return Err(format!("更新瓶码信息失败: {}", e));
        }
        info!("[取样★报废] 更新瓶码信息成功");

        Ok(())
    }

    /// 替换序列码
    pub async fn replace_code(
        conn: &mut Client<Compat<TcpStream>>,
        origin_code: &Code,
        replace_code: &Code,
        origin_code_str: &str,
        replace_code_str: &str,
    ) -> Result<(), String> {
        info!("[替换] 开始执行替换操作");

        // 1. 更新替换序列码信息
        let update_replace_sql = r#"
            UPDATE transit_code
            SET amount = @P1,
                parentCode = @P2,
                boxCode = @P3,
                palletCode = @P4,
                codeFlag = @P5,
                updateTime = @P6,
                helperCode = @P7,
                qualityLevel = @P8
            WHERE code = @P9
        "#;

        // 处理可能为空的字段
        let parent_code_param = origin_code.parent_code.as_ref().map(|s| s.as_str());
        let box_code_param = origin_code.box_code.as_ref().map(|s| s.as_str());
        let pallet_code_param = origin_code.pallet_code.as_ref().map(|s| s.as_str());
        let helper_code_param = origin_code.helper_code.as_ref().map(|s| s.as_str());
        let quality_level_param = origin_code.quality_level.as_ref().map(|s| s.as_str());

        // 使用当前时间作为更新时间
        let update_time = chrono::Local::now().naive_local();

        // 记录实际执行的SQL
        let log_replace_sql = update_replace_sql
            .replace("@P1", &format!("{}", origin_code.amount))
            .replace("@P2", &format!("{:?}", parent_code_param))
            .replace("@P3", &format!("{:?}", box_code_param))
            .replace("@P4", &format!("{:?}", pallet_code_param))
            .replace("@P5", &format!("{}", origin_code.code_flag))
            .replace("@P6", &format!("{}", update_time))
            .replace("@P7", &format!("{:?}", helper_code_param))
            .replace("@P8", &format!("{:?}", quality_level_param))
            .replace("@P9", &format!("'{}'", replace_code_str));
        info!("[替换] 开始执行更新替换序列码SQL: {}", log_replace_sql);

        if let Err(e) = conn
            .execute(
                update_replace_sql,
                &[
                    &origin_code.amount,
                    &parent_code_param,
                    &box_code_param,
                    &pallet_code_param,
                    &(origin_code.code_flag as i32),
                    &update_time,
                    &helper_code_param,
                    &quality_level_param,
                    &replace_code_str,
                ],
            )
            .await
        {
            error!("[替换] 更新替换序列码失败: {}", e);
            return Err(format!("更新替换序列码失败: {}", e));
        }
        info!("[替换] 更新替换序列码成功");

        // 2. 更新原始序列码信息
        let update_origin_sql = r#"
            UPDATE transit_code
            SET amount = 0,
                parentCode = NULL,
                boxCode = NULL,
                palletCode = NULL,
                codeFlag = @P1,
                updateTime = createTime,
                helperCode = NULL,
                qualityLevel = NULL
            WHERE code = @P2
        "#;

        // 记录实际执行的SQL
        let log_origin_sql = update_origin_sql
            .replace("@P1", &format!("{}", replace_code.code_flag))
            .replace("@P2", &format!("'{}'", origin_code_str));
        info!("[替换] 开始执行更新原始序列码SQL: {}", log_origin_sql);

        if let Err(e) = conn
            .execute(
                update_origin_sql,
                &[&(replace_code.code_flag as i32), &origin_code_str],
            )
            .await
        {
            error!("[替换] 更新原始序列码失败: {}", e);
            return Err(format!("更新原始序列码失败: {}", e));
        }
        info!("[替换] 更新原始序列码成功");

        // 3. 如果不是小瓶（levelCode不为1），则需要更新关联关系
        if origin_code.level_code != 1 {
            info!("[替换] 不是小瓶，需要更新关联关系");

            // 更新parentCode
            let update_parent_sql = r#"
                UPDATE transit_code
                SET parentCode = @P1
                WHERE parentCode = @P2
            "#;

            // 记录实际执行的SQL
            let log_parent_sql = update_parent_sql
                .replace("@P1", &format!("'{}'", replace_code_str))
                .replace("@P2", &format!("'{}'", origin_code_str));
            info!("[替换] 开始执行更新parentCode SQL: {}", log_parent_sql);

            if let Err(e) = conn
                .execute(update_parent_sql, &[&replace_code_str, &origin_code_str])
                .await
            {
                error!("[替换] 更新parentCode失败: {}", e);
                return Err(format!("更新parentCode失败: {}", e));
            }
            info!("[替换] 更新parentCode成功");

            // 更新boxCode
            let update_box_sql = r#"
                UPDATE transit_code
                SET boxCode = @P1
                WHERE boxCode = @P2
            "#;

            // 记录实际执行的SQL
            let log_box_sql = update_box_sql
                .replace("@P1", &format!("'{}'", replace_code_str))
                .replace("@P2", &format!("'{}'", origin_code_str));
            info!("[替换] 开始执行更新boxCode SQL: {}", log_box_sql);

            if let Err(e) = conn
                .execute(update_box_sql, &[&replace_code_str, &origin_code_str])
                .await
            {
                error!("[替换] 更新boxCode失败: {}", e);
                return Err(format!("更新boxCode失败: {}", e));
            }
            info!("[替换] 更新boxCode成功");

            // 更新palletCode
            let update_pallet_sql = r#"
                UPDATE transit_code
                SET palletCode = @P1
                WHERE palletCode = @P2
            "#;

            // 记录实际执行的SQL
            let log_pallet_sql = update_pallet_sql
                .replace("@P1", &format!("'{}'", replace_code_str))
                .replace("@P2", &format!("'{}'", origin_code_str));
            info!("[替换] 开始执行更新palletCode SQL: {}", log_pallet_sql);

            if let Err(e) = conn
                .execute(update_pallet_sql, &[&replace_code_str, &origin_code_str])
                .await
            {
                error!("[替换] 更新palletCode失败: {}", e);
                return Err(format!("更新palletCode失败: {}", e));
            }
            info!("[替换] 更新palletCode成功");
        } else {
            info!("[替换] 是小瓶(levelCode=1)，无需更新关联关系");
        }

        info!("[替换] 替换操作执行完成");
        Ok(())
    }

    /// 执行放入操作，将箱码放入托盘
    pub async fn execute_put_into(
        conn: &mut Client<Compat<TcpStream>>,
        pallet_code: &str,
        box_codes: &[String],
    ) -> Result<(), String> {
        info!("[聚合] |放入| 开始执行放入操作");

        // 获取当前时间
        let now = chrono::Local::now();
        let box_update_time = now + chrono::Duration::minutes(1);
        let pallet_update_time = now + chrono::Duration::minutes(3);

        // 格式化时间为SQL Server格式
        let now_str = now.format("%Y-%m-%d %H:%M:%S").to_string();
        let box_update_time_str = box_update_time.format("%Y-%m-%d %H:%M:%S").to_string();
        let pallet_update_time_str = pallet_update_time.format("%Y-%m-%d %H:%M:%S").to_string();

        // 遍历所有箱码
        for box_code in box_codes {
            info!("[聚合] |放入| 处理箱码: {}", box_code);

            // 1. 更新大箱下瓶码的信息
            let update_bottles_sql = r#"
                UPDATE transit_code
                SET palletCode = @P1,
                    updateTime = @P2
                WHERE levelCode = 1
                  AND codeFlag = 2
                  AND boxCode = @P3
            "#;

            // 记录实际执行的SQL
            let log_bottles_sql = update_bottles_sql
                .replace("@P1", &format!("'{}'", pallet_code))
                .replace("@P2", &format!("'{}'", now_str))
                .replace("@P3", &format!("'{}'", box_code));
            info!("[聚合] |放入| 开始执行更新瓶码SQL: {}", log_bottles_sql);

            if let Err(e) = conn
                .execute(
                    update_bottles_sql,
                    &[&pallet_code, &now_str.as_str(), &box_code.as_str()],
                )
                .await
            {
                error!("[聚合] |放入| 更新瓶码信息失败: {}", e);
                return Err(format!("更新瓶码信息失败: {}", e));
            }
            info!("[聚合] |放入| 更新瓶码信息成功");

            // 2. 更新大箱的信息
            let update_box_sql = r#"
                UPDATE transit_code
                SET parentCode = @P1,
                    palletCode = @P1,
                    updateTime = @P2
                WHERE levelCode = 3
                  AND codeFlag = 2
                  AND code = @P3
            "#;

            // 记录实际执行的SQL
            let log_box_sql = update_box_sql
                .replace("@P1", &format!("'{}'", pallet_code))
                .replace("@P2", &format!("'{}'", box_update_time_str))
                .replace("@P3", &format!("'{}'", box_code));
            info!("[聚合] |放入| 开始执行更新箱码SQL: {}", log_box_sql);

            if let Err(e) = conn
                .execute(
                    update_box_sql,
                    &[
                        &pallet_code,
                        &box_update_time_str.as_str(),
                        &box_code.as_str(),
                    ],
                )
                .await
            {
                error!("[聚合] |放入| 更新箱码信息失败: {}", e);
                return Err(format!("更新箱码信息失败: {}", e));
            }
            info!("[聚合] |放入| 更新箱码信息成功");
        }

        // 3. 更新托盘信息
        let update_pallet_sql = r#"
            UPDATE transit_code
            SET amount = amount + @P3,
                updateTime = @P2
            WHERE levelCode = 4
              AND codeFlag = 2
              AND code = @P1
        "#;

        // 记录实际执行的SQL
        let log_pallet_sql = update_pallet_sql
            .replace("@P1", &format!("'{}'", pallet_code))
            .replace("@P2", &format!("'{}'", pallet_update_time_str))
            .replace("@P3", &format!("{}", box_codes.len()));
        info!("[聚合] |放入| 开始执行更新托盘码SQL: {}", log_pallet_sql);

        if let Err(e) = conn
            .execute(
                update_pallet_sql,
                &[
                    &pallet_code,
                    &pallet_update_time_str.as_str(),
                    &(box_codes.len() as i32),
                ],
            )
            .await
        {
            error!("[聚合] |放入| 更新托盘码信息失败: {}", e);
            return Err(format!("更新托盘码信息失败: {}", e));
        }
        info!("[聚合] |放入| 更新托盘码信息成功");

        Ok(())
    }

    /// 执行新建包装操作，创建新托盘并将箱码放入
    pub async fn execute_new_package(
        conn: &mut Client<Compat<TcpStream>>,
        first_box: &Code,
        box_codes: &[String],
    ) -> Result<String, String> {
        info!("[聚合] |新建包装| 开始执行新建包装操作");

        // 从assistant函数获取一个托盘码
        let order_id = first_box.order_id;
        let pallet_code_result = assistant::get_code(order_id, 4, 1, 1).await;

        // 检查结果
        let errcode = pallet_code_result
            .get("errcode")
            .and_then(|v| v.as_i64())
            .unwrap_or(1);
        let errmsg = pallet_code_result
            .get("errmsg")
            .and_then(|v| v.as_str())
            .unwrap_or("未知错误");

        info!("[聚合] |新建包装| 获取托盘码结果: {:?}", pallet_code_result);

        // 检查errcode
        if errcode != 0 {
            error!(
                "[聚合] |新建包装| 获取托盘码失败: errcode={}, errmsg={}",
                errcode, errmsg
            );
            return Err(format!("获取托盘码失败: {}", errmsg));
        }

        // 从data数组中获取托盘码
        let data = match pallet_code_result.get("data").and_then(|v| v.as_array()) {
            Some(array) => {
                info!("[聚合] |新建包装| 获取托盘码数据: {:?}", array);
                array.clone()
            }
            None => {
                error!("[聚合] |新建包装| 获取托盘码数据失败");
                return Err("获取托盘码数据失败".to_string());
            }
        };

        // 检查data数组是否为空
        if data.is_empty() {
            error!("[聚合] |新建包装| 获取托盘码数据为空");
            return Err("获取托盘码数据为空".to_string());
        }

        // 获取托盘码
        let pallet_code = if data[0].is_string() {
            data[0].as_str().unwrap_or("").to_string()
        } else {
            error!("[聚合] |新建包装| 解析托盘码失败: {:?}", data[0]);
            data[0]
                .get("code")
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string()
        };

        // 检查托盘码是否为空
        if pallet_code.is_empty() {
            error!("[聚合] |新建包装| 获取到的托盘码为空");
            return Err("获取到的托盘码为空".to_string());
        }

        info!("[聚合] |新建包装| 获取到托盘码: {}", pallet_code);

        // 获取当前时间
        let now = chrono::Local::now();
        let box_update_time = now + chrono::Duration::minutes(1);
        let pallet_update_time = now + chrono::Duration::minutes(5); // 5分钟而不是3分钟

        // 格式化时间为SQL Server格式
        let now_str = now.format("%Y-%m-%d %H:%M:%S").to_string();
        let box_update_time_str = box_update_time.format("%Y-%m-%d %H:%M:%S").to_string();
        let pallet_update_time_str = pallet_update_time.format("%Y-%m-%d %H:%M:%S").to_string();

        // 构建箱码列表字符串，用于IN子句
        let box_codes_list = box_codes
            .iter()
            .map(|code| format!("'{}'", code))
            .collect::<Vec<_>>()
            .join(",");

        // 1. 更新大箱下瓶码的信息
        let update_bottles_sql = format!(
            r#"
            UPDATE transit_code
            SET palletCode = '{}',
                updateTime = '{}'
            WHERE levelCode = 1
              AND codeFlag = 2
              AND boxCode IN ({})
        "#,
            pallet_code, now_str, box_codes_list
        );

        info!(
            "[聚合] |新建包装| 开始执行更新瓶码SQL: {}",
            update_bottles_sql
        );

        if let Err(e) = conn.simple_query(&update_bottles_sql).await {
            error!("[聚合] |新建包装| 更新瓶码信息失败: {}", e);
            return Err(format!("更新瓶码信息失败: {}", e));
        }
        info!("[聚合] |新建包装| 更新瓶码信息成功");

        // 2. 更新大箱的信息
        let update_box_sql = format!(
            r#"
            UPDATE transit_code
            SET parentCode = '{}',
                palletCode = '{}',
                updateTime = '{}'
            WHERE levelCode = 3
              AND codeFlag = 2
              AND code IN ({})
        "#,
            pallet_code, pallet_code, box_update_time_str, box_codes_list
        );

        info!("[聚合] |新建包装| 开始执行更新箱码SQL: {}", update_box_sql);

        if let Err(e) = conn.simple_query(&update_box_sql).await {
            error!("[聚合] |新建包装| 更新箱码信息失败: {}", e);
            return Err(format!("更新箱码信息失败: {}", e));
        }
        info!("[聚合] |新建包装| 更新箱码信息成功");

        // 3. 更新托盘码信息
        let update_pallet_sql = format!(
            r#"
            UPDATE transit_code
            SET palletCode = '{}',
                amount = {},
                codeFlag = 2,
                updateTime = '{}'
            WHERE levelCode = 4
              AND code = '{}'
        "#,
            pallet_code,
            box_codes.len(),
            pallet_update_time_str,
            pallet_code
        );

        info!(
            "[聚合] |新建包装| 开始执行更新托盘码SQL: {}",
            update_pallet_sql
        );

        if let Err(e) = conn.simple_query(&update_pallet_sql).await {
            error!("[聚合] |新建包装| 更新托盘码信息失败: {}", e);
            return Err(format!("更新托盘码信息失败: {}", e));
        }
        info!("[聚合] |新建包装| 更新托盘码信息成功");

        Ok(pallet_code)
    }

    /// 检查托盘箱数限制
    pub async fn check_pallet_box_limit(
        conn: &mut Client<Compat<TcpStream>>,
        product_code: &str,
    ) -> Result<u32, String> {
        info!(
            "[聚合] 开始检查产品每托最大箱数，产品编码: {}",
            product_code
        );

        // 查询产品信息以获取包装规则
        let product_sql = r#"
            SELECT TOP 1 id, productCode, packageRules 
            FROM basic_product WITH (NOLOCK)
            WHERE productCode = @P1
        "#;

        // 执行查询
        let product = {
            let mut product_result = None;

            // 首先通过产品编码查询
            {
                let mut stream = match conn.query(product_sql, &[&product_code]).await {
                    Ok(stream) => stream,
                    Err(e) => {
                        error!("[聚合] 查询产品信息失败: {}", e);
                        return Err(format!("查询产品信息失败: {}", e));
                    }
                };

                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        product_result = Some(Product::renovation(&row));
                        break;
                    }
                }
            }

            // 如果找不到产品，尝试通过ID=3查询
            if product_result.is_none() {
                let product_id_sql = r#"
                    SELECT TOP 1 id, productCode, packageRules 
                    FROM basic_product WITH (NOLOCK)
                    WHERE id = 3
                "#;

                let mut stream = match conn.query(product_id_sql, &[]).await {
                    Ok(stream) => stream,
                    Err(e) => {
                        error!("[聚合] 查询产品信息失败: {}", e);
                        return Err(format!("查询产品信息失败: {}", e));
                    }
                };

                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        product_result = Some(Product::renovation(&row));
                        break;
                    }
                }
            }

            product_result.ok_or_else(|| "未查询到产品信息".to_string())?
        };

        // 从产品中获取包装规则并处理
        // 处理规则来源
        let json_rules = match &product.package_rules {
            Some(rules) if !rules.is_empty() => rules.clone(),
            _ => {
                error!("[聚合] 产品未设置包装规则");
                return Err("产品未设置包装规则".to_string());
            }
        };

        // 解析规则
        let mut package_rules = Vec::new();
        for rule in json_rules {
            // 创建一个可修改的规则副本并处理type字段
            let mut modified_rule = rule.clone();
            if let Some(type_value) = modified_rule.get("type") {
                if type_value.is_number() {
                    if let Some(type_num) = type_value.as_i64() {
                        modified_rule["type"] = serde_json::Value::String(type_num.to_string());
                    }
                }
            }

            // 解析修改后的规则
            if let Ok(package_rule) = serde_json::from_value::<PackageRule>(modified_rule.clone()) {
                package_rules.push(package_rule);
            }
        }

        // 检查是否成功解析了任何规则
        if package_rules.is_empty() {
            error!("[聚合] 未能成功解析任何包装规则");
            return Err("未能成功解析产品包装规则".to_string());
        }

        // 使用analyze辅助函数获取这个产品每托最大箱数
        let max_box_per_pallet = Analyzer::calc_qty(&package_rules, 4, 3);
        info!("[聚合] 解析到产品每托最大箱数: {}", max_box_per_pallet);

        Ok(max_box_per_pallet)
    }

    /// 验证托盘箱数是否超限
    pub async fn validate_pallet_box_count(
        conn: &mut Client<Compat<TcpStream>>,
        aggregate_type: &str,
        pallet_code: Option<&str>,
        product_code: &str,
        box_codes_count: usize,
    ) -> Result<(), String> {
        // 获取产品每托最大箱数
        let max_box_per_pallet = match Self::check_pallet_box_limit(conn, product_code).await {
            Ok(limit) => limit,
            Err(e) => return Err(e),
        };

        // 如果没有设置限制（返回0），则返回错误，因为每托必须有最大箱数限制
        if max_box_per_pallet == 0 {
            error!("[聚合] 未找到产品每托最大箱数限制，无法继续聚合操作");
            return Err("未找到产品每托最大箱数限制，请先设置产品包装规则".to_string());
        }

        match aggregate_type {
            "putInto" => {
                // 获取托盘当前箱数
                let pallet_code = pallet_code.unwrap_or_default();
                let current_box_count_sql = r#"
                    SELECT COUNT(*) as count
                    FROM transit_code WITH (NOLOCK)
                    WHERE levelCode = 3 AND codeFlag = 2 AND palletCode = @P1
                "#;

                let current_box_count =
                    match conn.query(current_box_count_sql, &[&pallet_code]).await {
                        Ok(mut stream) => {
                            let mut count = 0;
                            while let Ok(Some(item)) = stream.try_next().await {
                                if let tiberius::QueryItem::Row(row) = item {
                                    count = row.get::<i32, _>("count").unwrap_or(0);
                                    break;
                                }
                            }
                            info!("[聚合] |放入| 托盘当前箱数: {}", count);
                            count
                        }
                        Err(e) => {
                            error!("[聚合] |放入| 查询托盘当前箱数失败: {}", e);
                            return Err(format!("查询托盘当前箱数失败: {}", e));
                        }
                    };

                // 验证托盘当前箱数+扫描的箱数是否大于最大箱数
                let total_box_count = current_box_count as u32 + box_codes_count as u32;
                if total_box_count > max_box_per_pallet {
                    error!(
                        "[聚合] |放入| 超过每托最大箱数: 当前={}, 扫描={}, 总数={}, 最大={}",
                        current_box_count, box_codes_count, total_box_count, max_box_per_pallet
                    );
                    return Err(format!(
                        "托盘中箱码数量超限，最大允许{}个，当前{}个，新增{}个",
                        max_box_per_pallet, current_box_count, box_codes_count
                    ));
                }

                info!(
                    "[聚合] |放入| 箱数限制验证通过: 当前={}, 扫描={}, 总数={}, 最大={}",
                    current_box_count, box_codes_count, total_box_count, max_box_per_pallet
                );
            }
            "newPackage" => {
                // 验证扫描的箱数是否大于最大箱数
                let scan_box_count = box_codes_count as u32;
                if scan_box_count > max_box_per_pallet {
                    error!(
                        "[聚合] |新建包装| 超过每托最大箱数: 扫描={}, 最大={}",
                        scan_box_count, max_box_per_pallet
                    );
                    return Err(format!(
                        "箱码数量超限，最大允许{}个，当前扫描{}个",
                        max_box_per_pallet, scan_box_count
                    ));
                }

                info!(
                    "[聚合] |新建包装| 箱数限制验证通过: 扫描={}, 最大={}",
                    scan_box_count, max_box_per_pallet
                );
            }
            _ => {
                return Err("无效的聚合模式".to_string());
            }
        }

        Ok(())
    }
}
