use crate::app::providers::DatabaseServiceProvider;
use axum::Router;
use std::sync::Arc;

mod customer;
mod invoice;
mod warehouse;

pub use customer::routes as customer_routes;
pub use invoice::routes as invoice_routes;
pub use warehouse::routes as warehouse_routes;

/// 物流相关路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().nest(
        "/logistics",
        Router::new()
            .merge(customer_routes())
            .merge(warehouse_routes())
            .merge(invoice_routes()),
    )
}
