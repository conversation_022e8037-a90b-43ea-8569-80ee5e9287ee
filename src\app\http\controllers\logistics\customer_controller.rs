use axum::{http::StatusCode, response::IntoResponse, <PERSON><PERSON>};

/// 客户管理控制器
pub struct CustomerController;

impl CustomerController {
    /// 客户列表
    pub async fn customer_list() -> impl IntoResponse {
        (StatusCode::OK, <PERSON><PERSON>("客户列表"))
    }

    /// 客户分页
    pub async fn customer_page() -> impl IntoResponse {
        (StatusCode::OK, <PERSON><PERSON>("客户分页"))
    }

    /// 添加客户
    pub async fn customer_add() -> impl IntoResponse {
        (StatusCode::OK, <PERSON><PERSON>("添加客户"))
    }

    /// 编辑客户
    pub async fn customer_edit() -> impl IntoResponse {
        (StatusCode::OK, <PERSON><PERSON>("编辑客户"))
    }

    /// 删除客户
    pub async fn customer_del() -> impl IntoResponse {
        (StatusCode::OK, <PERSON><PERSON>("删除客户"))
    }
}
