use crate::app::http::controllers::basic::ProductController;
use crate::app::providers::DatabaseServiceProvider;
use axum::{routing::get, Router};
use std::sync::Arc;

/// 产品管理路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().merge(
        Router::new()
            .route("/product/page", get(ProductController::page))
            .route("/product/list", get(ProductController::index)),
    )
}
