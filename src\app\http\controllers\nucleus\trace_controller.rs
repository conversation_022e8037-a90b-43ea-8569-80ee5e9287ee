use crate::app::models::basic::product::Product;
use crate::app::models::produce::order::Order as ProduceOrder;
use crate::app::models::transit::code::Code;
use crate::app::providers::DatabaseServiceProvider;
use crate::app::traits::response::track;
use axum::extract::{Query, State};
use axum::response::IntoResponse;
use futures_util::TryStreamExt;
use serde::Deserialize;
use std::sync::Arc;
use tiberius::ToSql;
use tracing::{error, info};

/// 查询参数
#[derive(Debug, Deserialize)]
pub struct QueryParams {
    /// 追溯码
    #[serde(rename = "code")]
    pub code: Option<String>,
}

/// 追溯控制器
pub struct TraceController;

impl TraceController {
    /// 根据追溯码查询关联关系
    pub async fn relation(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Query(params): Query<QueryParams>,
    ) -> impl IntoResponse {
        info!("[关联查询] 开始查询关联关系");

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[关联查询] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[关联查询] 数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 验证参数
        let code = match params.code {
            Some(c) if !c.is_empty() => c,
            _ => {
                error!("[关联查询] 参数错误: 缺少追溯码");
                return track().defeat().message("请传入追溯码").build();
            }
        };

        // 查询码记录
        let transit_sql = r#"
            SELECT id, dataID, poNumber, productCode, orderId, batchNo, levelCode, levelName, 
                   resCode, snCode, code, amount, parentCode, boxCode, palletCode, typeFlag, reqID, 
                   codeFlag, boxFlag, estate, CONVERT(varchar(19), createTime, 120) as createTime, 
                   CONVERT(varchar(19), updateTime, 120) as updateTime, fileID, helperCode, zeroBox, 
                   qualityLevel, field1, field2, boxSn
            FROM transit_code WITH (NOLOCK)
            WHERE code = @P1
        "#;

        // 使用 Code 模型
        let mut code_info: Option<Code> = None;

        match conn.query(transit_sql, &[&code as &dyn ToSql]).await {
            Ok(mut stream) => {
                let mut found = false;
                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        found = true;
                        // 使用模型转换数据
                        let code_model = Code::renovation(&row);
                        code_info = Some(code_model);
                        break;
                    }
                }

                info!("[关联查询] 码查询: 标识={}", found);

                if !found {
                    error!("[关联查询] 未找到码记录: code={}", code);
                    return track().defeat().message("未找到追溯信息").build();
                }
            }
            Err(e) => {
                error!("[关联查询] 码查询失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 获取码信息的JSON表示
        let transit_data = serde_json::to_value(&code_info).unwrap_or_default();
        let mut transit_map = transit_data.as_object().unwrap().clone();

        // 获取关键字段
        let code_flag = code_info.as_ref().map(|c| c.code_flag).unwrap_or(0);
        let level_code = code_info.as_ref().map(|c| c.level_code).unwrap_or(0);
        let pallet_code = code_info.as_ref().and_then(|c| c.pallet_code.clone());
        let order_id = code_info.as_ref().map(|c| c.order_id).unwrap_or(0);
        let product_code = code_info
            .as_ref()
            .map(|c| c.product_code.clone())
            .unwrap_or_default();

        info!(
            "[关联查询] 关键字段: code_flag={}, level_code={}, order_id={}",
            code_flag, level_code, order_id
        );

        // 构建基本信息
        let mut basics = serde_json::json!({
            "code": code_info.as_ref().map(|c| c.code.clone()).unwrap_or_default(),
            "codeStatus": code_flag,
            "levelName": code_info.as_ref().map(|c| c.level_name.clone()).unwrap_or_default(),
            "levelCode": level_code,
            "productName": None::<String>,
            "productCode": product_code,
            "ndc": None::<String>,
            "packageSize": None::<String>,
            "packageRatio": None::<String>
        });

        // 处理托盘码关联
        if code_flag == 2 && pallet_code.as_ref().map_or(false, |s| !s.is_empty()) {
            if level_code == 4 {
                // 如果是托盘码，直接使用当前记录
                transit_map.insert("relevance".to_string(), transit_data.clone());
                info!("[关联查询] 使用自身作为托盘码关联");
            } else {
                // 如果不是托盘码，查询托盘码记录
                info!("[关联查询] 查询托盘码: {}", pallet_code.as_ref().unwrap());
                let pallet_sql = r#"
                    SELECT id, dataID, poNumber, productCode, orderId, batchNo, levelCode, levelName, 
                           resCode, snCode, code, amount, parentCode, boxCode, palletCode, typeFlag, reqID, 
                           codeFlag, boxFlag, estate, CONVERT(varchar(19), createTime, 120) as createTime, 
                           CONVERT(varchar(19), updateTime, 120) as updateTime, fileID, helperCode, zeroBox, 
                           qualityLevel, field1, field2, boxSn 
                    FROM transit_code WITH (NOLOCK)
                    WHERE code = @P1
                "#;

                let mut pallet_info: Option<Code> = None;
                match conn
                    .query(pallet_sql, &[&pallet_code.unwrap() as &dyn ToSql])
                    .await
                {
                    Ok(mut stream) => {
                        let mut found = false;
                        while let Ok(Some(item)) = stream.try_next().await {
                            if let tiberius::QueryItem::Row(row) = item {
                                found = true;
                                // 使用模型转换数据
                                let pallet_model = Code::renovation(&row);
                                pallet_info = Some(pallet_model);
                                break;
                            }
                        }

                        info!("[关联查询] 托盘码查询: 标识={}", found);

                        if found {
                            let pallet_data =
                                serde_json::to_value(&pallet_info).unwrap_or_default();
                            transit_map.insert("relevance".to_string(), pallet_data);
                        }
                    }
                    Err(e) => {
                        error!("[关联查询] 查询托盘码记录失败: {}", e);
                        return track().defeat().message(e.to_string()).build();
                    }
                }
            }
        }

        // 查询订单信息
        if order_id > 0 {
            info!("[关联查询] 查询订单信息: order_id={}", order_id);
            let order_sql = r#"
                SELECT id, dataID, poNumber, productId, plantId, lineId, batchNo, 
                       CONVERT(varchar(19), makeDate, 120) as makeDate,
                       CONVERT(varchar(19), validDate, 120) as validDate,
                       planAmount, actualAmount, packageRules, estate, 
                       CONVERT(varchar(19), createTime, 120) as createTime,
                       CONVERT(varchar(19), updateTime, 120) as updateTime,
                       method, generateFlag, printMarket, printFrom, printLogo,
                       volumeRatio, isMonthly, ndc, recipeNo
                FROM produce_order WITH (NOLOCK)
                WHERE id = @P1
            "#;

            let mut order_info: Option<ProduceOrder> = None;
            let mut product_id = 0;

            match conn.query(order_sql, &[&order_id as &dyn ToSql]).await {
                Ok(mut stream) => {
                    let mut found = false;
                    while let Ok(Some(item)) = stream.try_next().await {
                        if let tiberius::QueryItem::Row(row) = item {
                            found = true;
                            // 使用模型转换数据
                            let order_model: ProduceOrder = ProduceOrder::renovation(&row);
                            product_id = order_model.product_id;
                            order_info = Some(order_model);
                            break;
                        }
                    }

                    info!("[关联查询] 订单查询: 找到={}", found);

                    if found {
                        // 添加订单信息到响应
                        let order_data = order_info
                            .as_ref()
                            .map_or(serde_json::Value::Null, |order| order.to_response());

                        // 将 order_data 转换为可变对象
                        let mut order_obj = order_data.as_object().unwrap().clone();

                        // 处理 packageRules 字段，如果是 JSON 字符串则解析
                        if let Some(package_rules) = order_obj.get("packageRules") {
                            if let Some(package_rules_str) = package_rules.as_str() {
                                if let Ok(package_rules_json) =
                                    serde_json::from_str::<serde_json::Value>(package_rules_str)
                                {
                                    order_obj
                                        .insert("packageRules".to_string(), package_rules_json);
                                }
                            }
                        }

                        // 将处理后的对象转换回 Value
                        let processed_order_data = serde_json::Value::Object(order_obj);
                        transit_map.insert("order".to_string(), processed_order_data);
                    }
                }
                Err(e) => {
                    error!("[关联查询] 查询订单失败: {}", e);
                    return track().defeat().message(e.to_string()).build();
                }
            }

            // 查询产品信息
            if product_id > 0 {
                info!("[关联查询] 查询产品信息: product_id={}", product_id);
                let product_sql = r#"
                    SELECT id, companyId, plantId, productName, productCode, ndc, 
                           material, dosageForm, dosageUsage, strength, packageSize, 
                           generic, composition, unitPrice, description, scheduled, 
                           imageDir, receiverCode, remark, primaryCount, productType, 
                           country, packageRules, estate, 
                           CONVERT(varchar(19), createTime, 120) as createTime,
                           CONVERT(varchar(19), updateTime, 120) as updateTime,
                           packageRatio, shelfLife, productCodeType, productCodeValue, 
                           eancode, sendStatus, dateFormat, codeStyle, printFrom, 
                           printLogo, printMarket, packageSpec, recipeNo, manufacturer, 
                           distributorBy, manufacturerBy, distributor
                    FROM basic_product WITH (NOLOCK)
                    WHERE id = @P1
                "#;

                match conn.query(product_sql, &[&product_id as &dyn ToSql]).await {
                    Ok(mut stream) => {
                        let mut found = false;
                        while let Ok(Some(item)) = stream.try_next().await {
                            if let tiberius::QueryItem::Row(row) = item {
                                found = true;
                                // 使用模型转换数据
                                let product_model = Product::renovation(&row);

                                // 更新基本信息中的产品相关字段
                                basics["productName"] =
                                    serde_json::json!(product_model.product_name);
                                basics["ndc"] = serde_json::json!(product_model.ndc);

                                if let Some(package_size) = &product_model.package_size {
                                    basics["packageSize"] = serde_json::json!(package_size);
                                }

                                basics["packageRatio"] =
                                    serde_json::json!(product_model.package_ratio);

                                break;
                            }
                        }

                        info!("[关联查询] 产品查询: 找到={}", found);
                    }
                    Err(e) => {
                        error!("[关联查询] 查询产品失败: {}", e);
                        return track().defeat().message(e.to_string()).build();
                    }
                }
            }
        }

        // 查询出库扫码信息 - 使用palletCode
        info!("[关联查询] 开始查询出库信息");

        // 获取条码记录中的托盘码
        let pallet_code = code_info
            .as_ref()
            .and_then(|c| c.pallet_code.clone())
            .unwrap_or_else(|| code.clone());

        let ware_code_sql = r#"
            SELECT id, wareCode, wareID, typeFlag, type
            FROM ware_code WITH (NOLOCK)
            WHERE code = @P1
        "#;

        let mut ware_id = 0;
        match conn
            .query(ware_code_sql, &[&pallet_code as &dyn ToSql])
            .await
        {
            Ok(mut stream) => {
                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        ware_id = row.get::<i32, _>("wareID").unwrap_or(0);
                        if ware_id > 0 {
                            break;
                        }
                    }
                }
                info!("[关联查询] 出库信息查询完成: wareID={}", ware_id);
            }
            Err(e) => {
                error!("[关联查询] 查询出库码记录失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        }

        // 如果找到出库订单ID，查询出库订单
        if ware_id > 0 {
            let ware_order_sql = r#"
                SELECT id, wareCode, orderType, outboundType, completeFlag, estate
                FROM ware_order WITH (NOLOCK)
                WHERE id = @P1
            "#;

            let mut ware_order_info: Option<serde_json::Value> = None;
            match conn.query(ware_order_sql, &[&ware_id as &dyn ToSql]).await {
                Ok(mut stream) => {
                    while let Ok(Some(item)) = stream.try_next().await {
                        if let tiberius::QueryItem::Row(row) = item {
                            let release = serde_json::json!({
                                "wareCode": row.get::<&str, _>("wareCode").unwrap_or_default().to_string(),
                                "orderType": row.get::<u8, _>("orderType").unwrap_or(0),
                                "outboundType": row.get::<&str, _>("outboundType").unwrap_or_default().to_string(),
                                "completeFlag": row.get::<u8, _>("completeFlag").unwrap_or(0),
                                "wareStatus": row.get::<u8, _>("estate").unwrap_or(0)
                            });
                            ware_order_info = Some(release);
                            break;
                        }
                    }

                    if let Some(release) = ware_order_info {
                        transit_map.insert("release".to_string(), release);
                    }
                }
                Err(e) => {
                    error!("[关联查询] 查询出库订单记录失败: {}", e);
                    return track().defeat().message(e.to_string()).build();
                }
            }
        }

        // 构建最终的返回数据结构
        let response = serde_json::json!({
            "basics": basics,
            "order": transit_map.get("order").unwrap_or(&serde_json::Value::Null),
            "release": transit_map.get("release").unwrap_or(&serde_json::Value::Null),
            "relevance": transit_map.get("relevance").unwrap_or(&serde_json::Value::Null)
        });

        info!("[关联查询] 成功返回追溯信息");
        track().victory().data(response).build()
    }

    /// 查询子级追溯信息
    pub async fn children(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Query(params): Query<QueryParams>,
    ) -> impl IntoResponse {
        info!("[子级追溯] 开始查询子级追溯信息");

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[子级追溯] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[子级追溯] 数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 验证参数
        let code = match params.code {
            Some(c) if !c.is_empty() => c,
            _ => {
                error!("[子级追溯] 参数错误: 缺少追溯码");
                return track().defeat().message("请传入追溯码").build();
            }
        };

        info!("[子级追溯] 查询参数: parentCode={}", code);

        // 构建 SQL 查询
        let sql = r#"
            SELECT id, dataID, poNumber, productCode, orderId, batchNo, levelCode, levelName, 
                   resCode, snCode, code, amount, parentCode, boxCode, palletCode, typeFlag, reqID, 
                   codeFlag, boxFlag, estate, CONVERT(varchar(19), createTime, 120) as createTime, 
                   CONVERT(varchar(19), updateTime, 120) as updateTime, fileID, helperCode, zeroBox, 
                   qualityLevel, field1, field2, boxSn 
            FROM transit_code WITH (NOLOCK)
            WHERE parentCode = @P1 AND codeFlag = 2 AND palletCode IS NOT NULL
            ORDER BY id ASC
        "#;

        info!("[子级追溯] 执行SQL查询");

        // 执行查询
        let mut codes = Vec::new();
        let mut row_count = 0;
        match conn.query(sql, &[&code as &dyn ToSql]).await {
            Ok(mut stream) => {
                info!("[子级追溯] 开始处理查询结果");

                // 使用stream处理结果
                while let Ok(Some(item)) = stream.try_next().await {
                    row_count += 1;
                    if row_count % 100 == 0 {
                        info!("[子级追溯] 已处理 {} 条记录", row_count);
                    }

                    if let tiberius::QueryItem::Row(row) = item {
                        // 使用模型转换
                        let code = Code::renovation(&row);
                        codes.push(code);
                    }
                }

                info!(
                    "[子级追溯] 查询完成: 找到={}, 处理记录数={}",
                    codes.len(),
                    row_count
                );
            }
            Err(e) => {
                error!("[子级追溯] 查询失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        }

        // 转换为JSON
        let data = serde_json::to_value(&codes).unwrap_or_else(|e| {
            error!("[子级追溯] JSON转换失败: {}", e);
            serde_json::Value::Array(Vec::new())
        });

        info!(
            "[子级追溯] 查询结束: parentCode={}, 数量={}",
            code,
            codes.len()
        );

        track().victory().data(data).build()
    }

    /// 根据追溯码查询序列码的信息
    pub async fn independent(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Query(params): Query<QueryParams>,
    ) -> impl IntoResponse {
        info!("[查询序列码] 开始查询序列码信息");

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[查询序列码] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[查询序列码] 数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 验证参数
        let code = match params.code {
            Some(c) if !c.is_empty() => c,
            _ => {
                error!("[查询序列码] 参数错误: 缺少追溯码");
                return track().defeat().message("请传入追溯码").build();
            }
        };

        // 1. 查询码记录
        let transit_sql = r#"
            SELECT id, dataID, code, levelCode, levelName, amount, parentCode, boxCode, palletCode, 
                   typeFlag, codeFlag, boxFlag, zeroBox, orderId, estate, 
                   CONVERT(varchar(19), createTime, 120) as createTime,
                   CONVERT(varchar(19), updateTime, 120) as updateTime
            FROM transit_code WITH (NOLOCK)
            WHERE code = @P1
        "#;

        // 使用 Code 模型
        let mut code_info: Option<Code> = None;
        let mut order_id = 0;
        let mut level_code = 0;

        // 执行查询
        match conn.query(transit_sql, &[&code as &dyn ToSql]).await {
            Ok(mut stream) => {
                let mut found = false;
                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        found = true;
                        // 使用模型转换数据
                        let code_model = Code::renovation(&row);
                        // 获取订单ID - 单独提取是为了后续查询使用
                        order_id = code_model.order_id;
                        level_code = code_model.level_code;
                        code_info = Some(code_model);
                        break;
                    }
                }

                if !found {
                    error!("[查询序列码] 未查询到序列码记录: code={}", code);
                    return track().defeat().message("未查询到序列码信息").build();
                }
            }
            Err(e) => {
                error!("[查询序列码] 序列码查询失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 检查订单ID
        if order_id == 0 {
            error!("[查询序列码] 序列码记录中生产订单ID为空: code={}", code);
            return track()
                .defeat()
                .message("序列码记录中生产订单ID为空")
                .build();
        }

        // 2. 查询订单信息
        let order_sql = r#"
            SELECT id, dataID, poNumber, productId, plantId, lineId, batchNo, 
                   CONVERT(varchar(19), makeDate, 120) as makeDate,
                   CONVERT(varchar(19), validDate, 120) as validDate,
                   planAmount, actualAmount, packageRules, estate, 
                   CONVERT(varchar(19), createTime, 120) as createTime,
                   CONVERT(varchar(19), updateTime, 120) as updateTime,
                   method, generateFlag, printMarket, printFrom, printLogo,
                   volumeRatio, isMonthly, ndc, recipeNo
            FROM produce_order WITH (NOLOCK)
            WHERE id = @P1
        "#;

        // 使用 Order 模型
        let mut order_info: Option<ProduceOrder> = None;
        let mut product_id = 0;
        let mut batch_no = String::new();

        // 执行查询
        match conn.query(order_sql, &[&order_id as &dyn ToSql]).await {
            Ok(mut stream) => {
                let mut found = false;
                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        found = true;
                        // 使用模型转换数据
                        let order_model = ProduceOrder::renovation(&row);
                        // 获取产品ID和批次号 - 单独提取是为了后续查询和日志使用
                        product_id = order_model.product_id;
                        batch_no = order_model.batch_no.clone();
                        order_info = Some(order_model);
                        break;
                    }
                }

                info!("[查询序列码] 开始查询生产订单信息: order_id={}", order_id);

                if !found {
                    error!("[查询序列码] 未查询到生产订单信息: order_id={}", order_id);
                    return track().defeat().message("未查询到生产订单信息").build();
                }
            }
            Err(e) => {
                error!("[查询序列码] 订单查询失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 检查产品ID
        if product_id == 0 {
            error!(
                "[查询序列码] 生产订单记录中产品ID为空: order_id={}",
                order_id
            );
            return track().defeat().message("生产订单记录中产品ID为空").build();
        }

        // 3. 查询产品信息
        let product_sql = r#"
            SELECT id, companyId, plantId, productName, productCode, ndc, 
                   material, dosageForm, dosageUsage, strength, packageSize, 
                   generic, composition, unitPrice, description, scheduled, 
                   imageDir, receiverCode, remark, primaryCount, productType, 
                   country, packageRules, estate, 
                   CONVERT(varchar(19), createTime, 120) as createTime,
                   CONVERT(varchar(19), updateTime, 120) as updateTime,
                   packageRatio, shelfLife, productCodeType, productCodeValue, 
                   eancode, sendStatus, dateFormat, codeStyle, printFrom, 
                   printLogo, printMarket, packageSpec, recipeNo, manufacturer, 
                   distributorBy, manufacturerBy, distributor
            FROM basic_product WITH (NOLOCK)
            WHERE id = @P1
        "#;

        // 使用 Product 模型
        let mut product_info: Option<Product> = None;
        let mut product_name = String::new();

        // 执行查询
        match conn.query(product_sql, &[&product_id as &dyn ToSql]).await {
            Ok(mut stream) => {
                let mut found = false;
                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        found = true;
                        // 使用模型转换数据
                        let product_model = Product::renovation(&row);
                        // 获取产品名称 - 单独提取是为了日志使用
                        product_name = product_model.product_name.clone();
                        product_info = Some(product_model);
                        break;
                    }
                }

                info!("[查询序列码] 开始查询产品信息: product_id={}", product_id);

                if !found {
                    error!("[查询序列码] 未查询到产品信息: product_id={}", product_id);
                    return track().defeat().message("未查询到产品信息").build();
                }
            }
            Err(e) => {
                error!("[查询序列码] 产品信息查询失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 4. 根据levelCode查询数量信息
        let mut bottle_count = 0;
        let mut box_count = 0;

        info!("[查询序列码] 开始查询数量信息: levelCode={}", level_code);

        if level_code == 1 {
            // 瓶码不需要查询数量
            info!("[查询序列码] 瓶码不需要查询数量");
        } else if level_code == 3 {
            // 箱码 - 查询箱内瓶码数量
            let bottle_count_sql = r#"
                SELECT COUNT(*) as count
                FROM transit_code WITH (NOLOCK)
                WHERE levelCode = 1 AND codeFlag = 2 AND boxCode = @P1
            "#;

            match conn.query(bottle_count_sql, &[&code as &dyn ToSql]).await {
                Ok(mut stream) => {
                    while let Ok(Some(item)) = stream.try_next().await {
                        if let tiberius::QueryItem::Row(row) = item {
                            bottle_count = row.get::<i32, _>("count").unwrap_or(0);
                            break;
                        }
                    }
                    info!("[查询序列码] 箱码内瓶码数量: {}", bottle_count);
                }
                Err(e) => {
                    error!("[查询序列码] 查询箱内瓶码数量失败: {}", e);
                }
            }
        } else if level_code == 4 {
            // 托盘码 - 查询托盘内箱码数量
            let box_count_sql = r#"
                SELECT COUNT(*) as count
                FROM transit_code WITH (NOLOCK)
                WHERE levelCode = 3 AND codeFlag = 2 AND palletCode = @P1
            "#;

            match conn.query(box_count_sql, &[&code as &dyn ToSql]).await {
                Ok(mut stream) => {
                    while let Ok(Some(item)) = stream.try_next().await {
                        if let tiberius::QueryItem::Row(row) = item {
                            box_count = row.get::<i32, _>("count").unwrap_or(0);
                            break;
                        }
                    }
                    info!("[查询序列码] 托盘内箱码数量: {}", box_count);
                }
                Err(e) => {
                    error!("[查询序列码] 查询托盘内箱码数量失败: {}", e);
                }
            }

            // 查询托盘内所有瓶码数量
            let bottle_count_sql = r#"
                SELECT COUNT(*) as count
                FROM transit_code WITH (NOLOCK)
                WHERE levelCode = 1 AND codeFlag = 2 AND palletCode = @P1
            "#;

            match conn.query(bottle_count_sql, &[&code as &dyn ToSql]).await {
                Ok(mut stream) => {
                    while let Ok(Some(item)) = stream.try_next().await {
                        if let tiberius::QueryItem::Row(row) = item {
                            bottle_count = row.get::<i32, _>("count").unwrap_or(0);
                            break;
                        }
                    }
                    info!("[查询序列码] 托盘内瓶码数量: {}", bottle_count);
                }
                Err(e) => {
                    error!("[查询序列码] 查询托盘内瓶码数量失败: {}", e);
                }
            }
        }

        // 构建返回数据
        let order_json = order_info
            .as_ref()
            .map_or(serde_json::Value::Null, |order| order.to_response());

        // 处理 order 中的 packageRules 字段
        let processed_order = if let Some(order_obj) = order_json.as_object() {
            let mut order_map = order_obj.clone();

            // 处理 packageRules 字段，如果是 JSON 字符串则解析
            if let Some(package_rules) = order_map.get("packageRules") {
                if let Some(package_rules_str) = package_rules.as_str() {
                    if let Ok(package_rules_json) =
                        serde_json::from_str::<serde_json::Value>(package_rules_str)
                    {
                        order_map.insert("packageRules".to_string(), package_rules_json);
                    }
                }
            }

            serde_json::Value::Object(order_map)
        } else {
            order_json
        };

        // 添加关联信息
        let mut associate_info = serde_json::json!({
            "bottleCount": 0,
            "boxCount": 0
        });

        if level_code == 1 {
            // 瓶码不需要关联数量
        } else if level_code == 3 {
            // 箱码
            associate_info["bottleCount"] = serde_json::json!(bottle_count);
        } else if level_code == 4 {
            // 托盘码
            associate_info["boxCount"] = serde_json::json!(box_count);
            associate_info["bottleCount"] = serde_json::json!(bottle_count);
        }

        let response_data = serde_json::json!({
            "order": processed_order,
            "product": product_info,
            "relevance": code_info,
            "associate": associate_info
        });

        info!(
            "[查询序列码] 查询序列码信息完成: code={}, batch={}, product={}",
            code, batch_no, product_name
        );
        track().victory().data(response_data).build()
    }
}
