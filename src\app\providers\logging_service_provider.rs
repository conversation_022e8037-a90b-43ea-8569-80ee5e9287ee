use chrono::Local;
use std::path::Path;
use tracing_appender::rolling::{RollingFileAppender, Rotation};
use tracing_subscriber::{fmt::format::FmtSpan, layer::SubscriberExt, util::SubscriberInitExt};

/// 日志服务提供者
pub struct LoggingServiceProvider;

/// 自定义本地时间格式化器，保证日志内容为本地时区（如 Asia/Shanghai）
pub struct LocalTimer;

impl tracing_subscriber::fmt::time::FormatTime for LocalTimer {
    fn format_time(&self, w: &mut tracing_subscriber::fmt::format::Writer<'_>) -> std::fmt::Result {
        // 使用 chrono 的 Local 获取本地时间，格式为：2024-05-11 16:30:15.123
        write!(
            w,
            "{}",
            chrono::Local::now().format("%Y-%m-%d %H:%M:%S%.3f")
        )
    }
}

impl LoggingServiceProvider {
    /// 初始化日志系统
    pub fn init(
        level: &str,
        path: &str,
        max_size: u64,
        max_age: u64,
        max_backups: u64,
        compress: bool,
    ) {
        // 设置时区环境变量，保证 chrono::Local 正确
        if let Ok(timezone) = std::env::var("TIMEZONE") {
            std::env::set_var("TZ", timezone);
        }

        // 获取当前日期（本地时区）
        let today = Local::now().format("%Y-%m-%d").to_string();

        // 创建日期目录
        let date_path = Path::new(path).join(&today);
        if !date_path.exists() {
            std::fs::create_dir_all(&date_path).expect("Failed to create log directory");
        }

        // 清理过期日志
        Self::cleanup_old_logs(path, max_age, max_backups, compress);

        // 配置文件日志
        let file_appender = if max_size > 0 {
            // 如果设置了最大大小，使用按天分割并限制大小
            RollingFileAppender::new(Rotation::NEVER, date_path.to_str().unwrap(), "app.log")
        } else {
            // 否则使用按天分割
            RollingFileAppender::new(Rotation::NEVER, date_path.to_str().unwrap(), "app.log")
        };

        // 使用自定义本地时间格式化器
        let timer = LocalTimer;

        // 创建文件日志层
        let file_layer = tracing_subscriber::fmt::layer()
            .with_writer(file_appender)
            .with_ansi(false)
            .with_timer(timer)
            .with_target(false)
            .with_thread_ids(false)
            .with_thread_names(false)
            .with_file(false)
            .with_line_number(false)
            .with_span_events(FmtSpan::NONE);

        // 控制台日志层同样使用本地时间
        let console_layer = tracing_subscriber::fmt::layer()
            .with_ansi(true)
            .with_timer(LocalTimer)
            .with_target(false)
            .with_thread_ids(false)
            .with_thread_names(false)
            .with_file(false)
            .with_line_number(false)
            .with_span_events(FmtSpan::NONE);

        // 初始化日志系统
        tracing_subscriber::registry()
            .with(tracing_subscriber::EnvFilter::new(format!(
                "{},tiberius=error,rustls=error",
                level
            )))
            .with(file_layer)
            .with(console_layer)
            .init();
    }

    /// 清理过期日志
    fn cleanup_old_logs(path: &str, max_age: u64, max_backups: u64, compress: bool) {
        let log_dir = Path::new(path);
        if !log_dir.exists() {
            return;
        }

        // 获取所有日期目录
        let mut date_dirs = Vec::new();
        if let Ok(entries) = std::fs::read_dir(log_dir) {
            for entry in entries.flatten() {
                if let Ok(file_type) = entry.file_type() {
                    if file_type.is_dir() {
                        if let Ok(metadata) = entry.metadata() {
                            if let Ok(modified) = metadata.modified() {
                                date_dirs.push((entry.path(), modified));
                            }
                        }
                    }
                }
            }
        }

        // 按修改时间排序
        date_dirs.sort_by(|a, b| b.1.cmp(&a.1));

        // 删除过期目录
        let now = std::time::SystemTime::now();
        for (path, modified) in date_dirs.iter().skip(max_backups as usize) {
            if let Ok(duration) = now.duration_since(*modified) {
                if duration.as_secs() > max_age * 24 * 3600 {
                    let _ = std::fs::remove_dir_all(path);
                }
            }
        }

        // 如果启用了压缩，则压缩日志文件
        if compress {
            for (path, _) in date_dirs.iter().take(max_backups as usize) {
                let log_file = path.join("app.log");
                if log_file.exists() && !log_file.with_extension("gz").exists() {
                    if let Ok(file) = std::fs::File::open(&log_file) {
                        let gz_path = log_file.with_extension("gz");
                        if let Ok(gz_file) = std::fs::File::create(&gz_path) {
                            let mut encoder = flate2::write::GzEncoder::new(
                                gz_file,
                                flate2::Compression::default(),
                            );
                            if let Ok(_) =
                                std::io::copy(&mut std::io::BufReader::new(file), &mut encoder)
                            {
                                let _ = std::fs::remove_file(log_file);
                            }
                        }
                    }
                }
            }
        } else {
            // 如果禁用了压缩，则只保留原始日志文件
            for (path, _) in date_dirs.iter().take(max_backups as usize) {
                let log_file = path.join("app.log");
                let gz_path = log_file.with_extension("gz");
                if gz_path.exists() {
                    let _ = std::fs::remove_file(gz_path);
                }
            }
        }
    }
}
