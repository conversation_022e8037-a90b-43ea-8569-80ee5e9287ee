use crate::app::models::{basic::Product, produce::Order, transit::code::Code};
use crate::app::services::socket::client_service::SocketClientService;
use encoding::{all::GBK, EncoderTrap, Encoding};
use futures_util::TryStreamExt;
use serde_json::{json, Value};
use std::collections::HashMap;
use tiberius::{Client, Row};
use tokio::net::TcpStream;
use tokio_util::compat::Compat;
use tracing::{debug, error, info, instrument, warn};

// 类型别名，简化代码
type DbClient = Client<Compat<TcpStream>>;
type DbResult<T> = Result<T, String>;

/// 打印模板类型
#[derive(Debug, Clone, Copy)]
pub enum PrintTemplateType {
    /// 整箱模板
    Box = 1,
    /// 零箱模板
    ZeroBox = 2,
    /// 托盘模板
    Pallet = 3,
}

/// 打印服务
#[derive(Debug)]
pub struct PrintService;

impl PrintService {
    // 辅助方法：查询单行数据
    #[instrument(skip_all)]
    async fn query_single_row<T>(
        conn: &mut DbClient,
        sql: &str,
        params: &[&dyn tiberius::ToSql],
        entity_name: &str,
        row_mapper: impl FnOnce(&Row) -> T,
    ) -> DbResult<T> {
        match conn.query(sql, params).await {
            Ok(mut stream) => {
                let mut found = false;
                let mut result = None;

                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        found = true;
                        result = Some(row_mapper(&row));
                        break;
                    }
                }

                if found {
                    Ok(result.unwrap())
                } else {
                    Err(format!("未查询到{}信息", entity_name))
                }
            }
            Err(e) => {
                error!("[打印服务] 查询{}信息失败: {}", entity_name, e);
                Err(format!("查询{}信息失败: {}", entity_name, e))
            }
        }
    }

    // 辅助方法：查询整数值
    #[instrument(skip_all)]
    async fn query_scalar_value(
        conn: &mut DbClient,
        sql: &str,
        params: &[&dyn tiberius::ToSql],
        column_name: &str,
        entity_name: &str,
    ) -> DbResult<i32> {
        match conn.query(sql, params).await {
            Ok(mut stream) => {
                let mut value = None;

                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        if let Some(val) = row.get::<i32, _>(column_name) {
                            value = Some(val);
                            break;
                        }
                    }
                }

                if let Some(val) = value {
                    debug!("[打印服务] {}查询结果: {}", entity_name, val);
                    Ok(val)
                } else {
                    warn!("[打印服务] 未查询到{}数据", entity_name);
                    Ok(0) // 返回默认值而不是错误
                }
            }
            Err(e) => {
                error!("[打印服务] 查询{}失败: {}", entity_name, e);
                Err(format!("查询{}失败: {}", entity_name, e))
            }
        }
    }
    /// 通过序列码获取打印数据
    ///
    /// # 参数
    /// * `conn` - 数据库连接
    /// * `code_value` - 序列码值
    ///
    /// # 返回值
    /// * `Ok(Value)` - 打印数据
    /// * `Err(String)` - 错误信息
    #[instrument(skip(conn), fields(code = %code_value))]
    pub async fn get_print_data_by_code(conn: &mut DbClient, code_value: &str) -> DbResult<Value> {
        debug!("[打印服务] 开始获取序列码信息");

        // 1. 查询序列码信息
        let code = Self::query_code_info(conn, code_value).await?;

        // 2. 查询产品信息
        let product = Self::query_product_info(conn, &code.product_code).await?;

        // 3. 查询订单信息
        let order = Self::query_order_info(conn, code.order_id).await?;

        // 4. 解析包装规则
        let package_rules = Self::parse_package_rules(&product)?;

        // 5. 根据层级码类型获取打印模板数据
        let print_data = match (code.level_code, code.zero_box) {
            (3, 0) => {
                Self::get_box_template_data(conn, &code, &product, &order, &package_rules).await?
            }
            (3, 1) => {
                Self::get_zero_box_template_data(conn, &code, &product, &order, &package_rules)
                    .await?
            }
            (4, _) => {
                Self::get_pallet_template_data(conn, &code, &product, &order, &package_rules)
                    .await?
            }
            _ => {
                return Err(format!(
                    "不支持的层级类型: level_code={}, zero_box={}",
                    code.level_code, code.zero_box
                ))
            }
        };

        Ok(print_data)
    }

    /// 查询序列码信息
    #[instrument(skip(conn), fields(code = %code_value))]
    async fn query_code_info(conn: &mut DbClient, code_value: &str) -> DbResult<Code> {
        let sql = r#"
            SELECT id, dataID, poNumber, productCode, orderId, batchNo, levelCode, levelName, 
                   resCode, snCode, code, amount, parentCode, boxCode, palletCode, typeFlag, reqID, 
                   codeFlag, boxFlag, estate, CONVERT(varchar(19), createTime, 120) as createTime, 
                   CONVERT(varchar(19), updateTime, 120) as updateTime, fileID, helperCode, zeroBox, 
                   qualityLevel, field1, field2, boxSn
            FROM transit_code WITH (NOLOCK)
            WHERE code = @P1
        "#;

        // 使用辅助函数简化查询逻辑
        Self::query_single_row(conn, sql, &[&code_value], "序列码", |row| {
            let code = Code::renovation(row);
            debug!(
                "[打印服务] 查询到序列码信息: code_flag={}, level_code={}, zero_box={}",
                code.code_flag, code.level_code, code.zero_box
            );
            code
        })
        .await
    }

    /// 查询产品信息
    #[instrument(skip(conn), fields(product_code = %product_code))]
    async fn query_product_info(conn: &mut DbClient, product_code: &str) -> DbResult<Product> {
        let sql = r#"
            SELECT id, companyId, plantId, productName, productCode, ndc, material, dosageForm, 
                   dosageUsage, strength, packageSize, generic, composition, unitPrice, description, 
                   scheduled, imageDir, receiverCode, remark, primaryCount, productType, country, 
                   packageRules, estate, CONVERT(varchar(19), createTime, 120) as createTime, 
                   CONVERT(varchar(19), updateTime, 120) as updateTime, packageRatio, shelfLife, 
                   productCodeType, productCodeValue, eancode, sendStatus, dateFormat, codeStyle, 
                   printFrom, printLogo, printMarket, packageSpec, recipeNo, manufacturer, 
                   distributorBy, manufacturerBy, distributor
            FROM basic_product WITH (NOLOCK)
            WHERE productCode = @P1
        "#;

        // 使用辅助函数简化查询逻辑
        Self::query_single_row(
            conn,
            sql,
            &[&product_code],
            &format!("产品(code={})", product_code),
            |row| {
                let product = Product::renovation(row);
                debug!(
                    "[打印服务] 查询到产品信息: product_name={}",
                    product.product_name
                );
                product
            },
        )
        .await
    }

    /// 查询订单信息
    #[instrument(skip(conn), fields(order_id = %order_id))]
    async fn query_order_info(conn: &mut DbClient, order_id: i32) -> DbResult<Order> {
        let sql = r#"
            SELECT id, dataID, poNumber, productId, plantId, lineId, batchNo, 
                   CONVERT(varchar(19), makeDate, 120) as makeDate,
                   CONVERT(varchar(19), validDate, 120) as validDate,
                   planAmount, actualAmount, estate, 
                   CONVERT(varchar(19), createTime, 120) as createTime,
                   CONVERT(varchar(19), updateTime, 120) as updateTime,
                   method, generateFlag, printMarket, printFrom, printLogo,
                   volumeRatio, isMonthly, ndc, recipeNo
            FROM produce_order WITH (NOLOCK)
            WHERE id = @P1
        "#;

        // 使用辅助函数简化查询逻辑
        Self::query_single_row(
            conn,
            sql,
            &[&order_id],
            &format!("订单(id={})", order_id),
            |row| {
                let order = Order::renovation(row);
                debug!(
                    "[打印服务] 查询到订单信息: batch_no={}, estate={}",
                    order.batch_no, order.estate
                );
                order
            },
        )
        .await
    }

    /// 解析包装规则
    #[instrument(skip(product), fields(product_name = %product.product_name))]
    fn parse_package_rules(product: &Product) -> DbResult<Vec<HashMap<String, Value>>> {
        if let Some(rules) = &product.package_rules {
            let rules_vec = rules.clone();
            let mut result = Vec::new();

            for rule in rules_vec {
                if let Some(rule_map) = rule.as_object() {
                    let mut map = HashMap::new();
                    for (key, value) in rule_map {
                        map.insert(key.clone(), value.clone());
                    }
                    result.push(map);
                }
            }

            if result.is_empty() {
                Err("包装规则格式不正确".to_string())
            } else {
                Ok(result)
            }
        } else {
            Err("产品包装规则为空".to_string())
        }
    }

    /// 获取整箱模板数据
    #[instrument(skip(conn, code, product, order, package_rules), fields(code_value = %code.code))]
    async fn get_box_template_data(
        conn: &mut DbClient,
        code: &Code,
        product: &Product,
        order: &Order,
        package_rules: &[HashMap<String, Value>],
    ) -> DbResult<Value> {
        debug!("[打印服务] 获取整箱模板数据");

        // 查询箱内瓶码数量
        let quantity = Self::query_bottle_count_in_box(conn, code).await?;

        // 获取整箱控制码
        let control_code = Self::get_control_code_from_rules(package_rules, 3)?;

        // 构建整箱模板数据
        let template_data = json!({
            "PrintType": "1",
            "PrintCount": "1",
            "Message": [
                {"Node": "distributor1", "Value": product.distributor.as_deref().unwrap_or("")},
                {"Node": "ProductName", "Value": &product.product_name},
                {"Node": "PackageDose", "Value": product.package_size.as_deref().unwrap_or("")},
                {"Node": "PackageUnit", "Value": product.dosage_usage.as_deref().unwrap_or("")},
                {"Node": "VariableCount", "Value": quantity.to_string()},
                {"Node": "exp", "Value": order.valid_date.format("%Y%m%d")},
                {"Node": "lot", "Value": &code.batch_no},
                {"Node": "ndc", "Value": &product.ndc},
                {"Node": "sn", "Value": &code.sn_code},
                {"Node": "gtin", "Value": &code.res_code},
                {"Node": "manufacturer2", "Value": product.manufacturer.as_deref().unwrap_or("")},
                {"Node": "manufacturerBy3", "Value": product.manufacturer_by.as_deref().unwrap_or("")},
                {"Node": "distributorBy4", "Value": product.distributor_by.as_deref().unwrap_or("")},
                {"Node": "remark", "Value": product.remark.as_deref().unwrap_or("")},
                {"Node": "controlCode", "Value": control_code}
            ]
        });

        Ok(template_data)
    }

    /// 获取零箱模板数据
    #[instrument(skip(conn, code, product, order, package_rules), fields(code_value = %code.code))]
    async fn get_zero_box_template_data(
        conn: &mut DbClient,
        code: &Code,
        product: &Product,
        order: &Order,
        package_rules: &[HashMap<String, Value>],
    ) -> DbResult<Value> {
        debug!("[打印服务] 获取零箱模板数据");

        // 查询箱内瓶码数量
        let quantity = Self::query_bottle_count_in_box(conn, code).await?;

        // 获取零箱控制码
        let control_code = Self::get_control_code_from_rules(package_rules, 5)?;

        // 构建零箱模板数据
        let template_data = json!({
            "PrintType": "2",
            "PrintCount": "1",
            "Message": [
                {"Node": "distributor1", "Value": product.distributor.as_deref().unwrap_or("")},
                {"Node": "ProductName", "Value": &product.product_name},
                {"Node": "PackageDose", "Value": product.package_size.as_deref().unwrap_or("")},
                {"Node": "PackageUnit", "Value": product.dosage_usage.as_deref().unwrap_or("")},
                {"Node": "VariableCount", "Value": quantity.to_string()},
                {"Node": "exp", "Value": order.valid_date.format("%Y%m%d")},
                {"Node": "lot", "Value": &code.batch_no},
                {"Node": "ndc", "Value": &product.ndc},
                {"Node": "code", "Value": &code.code},
                {"Node": "manufacturer2", "Value": product.manufacturer.as_deref().unwrap_or("")},
                {"Node": "manufacturerBy3", "Value": product.manufacturer_by.as_deref().unwrap_or("")},
                {"Node": "distributor1", "Value": product.distributor.as_deref().unwrap_or("")},
                {"Node": "distributorBy4", "Value": product.distributor_by.as_deref().unwrap_or("")},
                {"Node": "remark", "Value": product.remark.as_deref().unwrap_or("")},
                {"Node": "controlCode", "Value": control_code}
            ]
        });

        Ok(template_data)
    }

    /// 获取托盘模板数据
    #[instrument(skip(conn, code, product, order, package_rules), fields(code_value = %code.code))]
    async fn get_pallet_template_data(
        conn: &mut DbClient,
        code: &Code,
        product: &Product,
        order: &Order,
        package_rules: &[HashMap<String, Value>],
    ) -> DbResult<Value> {
        debug!("[打印服务] 获取托盘模板数据");

        // 查询托盘内箱码数量
        let quantity = Self::query_box_count_in_pallet(conn, code).await?;

        // 获取托盘控制码
        let control_code = Self::get_control_code_from_rules(package_rules, 4)?;

        // 构建托盘模板数据
        let template_data = json!({
            "PrintType": "3",
            "PrintCount": "1",
            "Message": [
                {"Node": "manufacturer1", "Value": product.manufacturer.as_deref().unwrap_or("")},
                {"Node": "manufacturerBy2", "Value": product.manufacturer_by.as_deref().unwrap_or("")},
                {"Node": "ProductName", "Value": &product.product_name},
                {"Node": "ndc", "Value": &product.ndc},
                {"Node": "exp", "Value": order.valid_date.format("%Y%m%d")},
                {"Node": "lot", "Value": &code.batch_no},
                {"Node": "Quantity", "Value": quantity.to_string()},
                {"Node": "code24", "Value": &code.code},
                {"Node": "controlCode", "Value": control_code}
            ]
        });

        Ok(template_data)
    }

    /// 查询箱内瓶码数量
    #[instrument(skip(conn, code), fields(code_value = %code.code))]
    async fn query_bottle_count_in_box(conn: &mut DbClient, code: &Code) -> DbResult<i32> {
        let sql = r#"
            SELECT COUNT(*) as quantity 
            FROM transit_code WITH (NOLOCK)
            WHERE orderId = @P1 
            AND batchNo = @P2 
            AND levelCode = 1 
            AND codeFlag = 2 
            AND parentCode = @P3 
            AND boxCode = @P3
        "#;

        // 使用辅助函数简化查询逻辑
        Self::query_scalar_value(
            conn,
            sql,
            &[&code.order_id, &code.batch_no, &code.code],
            "quantity",
            "箱内瓶码数量",
        )
        .await
    }

    /// 查询托盘内箱码数量
    #[instrument(skip(conn, code), fields(code_value = %code.code))]
    async fn query_box_count_in_pallet(conn: &mut DbClient, code: &Code) -> DbResult<i32> {
        let sql = r#"
            SELECT COUNT(*) as quantity 
            FROM transit_code WITH (NOLOCK)
            WHERE orderId = @P1 
            AND batchNo = @P2 
            AND levelCode = 3 
            AND codeFlag = 2 
            AND parentCode = @P3 
            AND palletCode = @P3
        "#;

        // 使用辅助函数简化查询逻辑
        Self::query_scalar_value(
            conn,
            sql,
            &[&code.order_id, &code.batch_no, &code.code],
            "quantity",
            "托盘内箱码数量",
        )
        .await
    }

    /// 从包装规则中获取控制码
    #[instrument(fields(level_code = %level_code))]
    fn get_control_code_from_rules(
        package_rules: &[HashMap<String, Value>],
        level_code: i8,
    ) -> DbResult<String> {
        for rule in package_rules {
            if let Some(level) = rule.get("level") {
                if let Some(level_value) = level.as_i64() {
                    if level_value == level_code as i64 {
                        if let Some(control_code) = rule.get("controlCode") {
                            if let Some(code_str) = control_code.as_str() {
                                return Ok(code_str.to_string());
                            }
                        }
                    }
                }
            }
        }

        Err(format!("未找到层级为{}的控制码", level_code))
    }

    /// 执行打印操作
    ///
    /// # 参数
    /// * `print_data` - 打印数据
    ///
    /// # 返回值
    /// * `Ok(String)` - 打印结果
    /// * `Err(String)` - 错误信息
    #[instrument(skip(print_data))]
    pub async fn execute_print(print_data: &Value) -> DbResult<String> {
        info!("[打印服务] 开始执行打印操作");

        // 将打印数据转换为JSON字符串
        let json_data = match serde_json::to_string(print_data) {
            Ok(data) => data,
            Err(e) => {
                error!("[打印服务] JSON序列化失败: {}", e);
                return Err(format!("JSON序列化失败: {}", e));
            }
        };

        debug!("[打印服务] 打印数据: {}", json_data);

        // 将JSON字符串转换为GBK编码的字节
        let gbk_bytes = match GBK.encode(&json_data, EncoderTrap::Replace) {
            Ok(bytes) => bytes,
            Err(e) => {
                error!("[打印服务] GBK编码失败: {:?}", e);
                return Err("GBK编码失败".to_string());
            }
        };

        // 创建Socket客户端并使用from_env方法
        let mut client = SocketClientService::from_env();

        // 添加回车换行到GBK编码的数据末尾
        let mut data_with_crlf = gbk_bytes;
        data_with_crlf.extend_from_slice(b"\r\n");

        // 使用try-finally模式确保连接总是被关闭
        let result = match client.connect().await {
            Ok(_) => {
                debug!("[打印服务] Socket连接成功");

                // 发送带有回车换行的GBK编码数据
                match client.send_and_receive(&data_with_crlf).await {
                    Ok(response) => {
                        // 尝试将响应从GBK转回UTF-8
                        let response_text =
                            match GBK.decode(&response, encoding::DecoderTrap::Replace) {
                                Ok(text) => text,
                                Err(_) => String::from_utf8_lossy(&response).to_string(),
                            };

                        info!("[打印服务] 打印成功，响应: {}", response_text);
                        Ok(response_text)
                    }
                    Err(e) => {
                        error!("[打印服务] Socket通信失败: {:?}", e);
                        Err(format!("Socket通信失败: {}", e))
                    }
                }
            }
            Err(e) => {
                error!("[打印服务] Socket连接失败: {:?}", e);
                Err(format!("Socket连接失败: {}", e))
            }
        };

        // 确保连接被关闭
        client.close().await;

        result
    }
}
