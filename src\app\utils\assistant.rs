use crate::app::facades::db::DB;
use serde_json::{json, Value};
use std::collections::HashMap;
use tracing::{error, info};

/// 获取序列码
///
/// # 参数
/// * `id` - 订单ID
/// * `level` - 层级，只允许3或4
/// * `amount` - 申请数量
/// * `type_flag` - 序列码类型，0为GTIN，1为SSCC
///
/// # 返回值
/// 返回结果包含错误码、错误信息和数据
pub async fn get_code(id: i32, level: i32, amount: i32, type_flag: i32) -> HashMap<String, Value> {
    // 验证层级
    if ![3, 4].contains(&level) {
        return HashMap::from([
            ("errcode".to_string(), json!(1)),
            (
                "errmsg".to_string(),
                json!("序列码层级只能允许为大箱或者托盘"),
            ),
        ]);
    }

    // 验证类型
    if ![0, 1].contains(&type_flag) {
        return HashMap::from([
            ("errcode".to_string(), json!(1)),
            (
                "errmsg".to_string(),
                json!("序列码类型只能允许为GTIN或者SSCC"),
            ),
        ]);
    }

    // 验证层级和类型的组合
    if level == 4 && type_flag == 0 {
        return HashMap::from([
            ("errcode".to_string(), json!(1)),
            ("errmsg".to_string(), json!("层级为4的是托盘，类型必须为1")),
        ]);
    }

    // 验证申请数量
    if amount <= 0 {
        return HashMap::from([
            ("errcode".to_string(), json!(1)),
            ("errmsg".to_string(), json!("申请数量必须大于0")),
        ]);
    }

    // 获取连接池
    let pool = DB::get_pool().await.clone();

    // 获取连接
    let mut conn = match pool.get().await {
        Ok(conn) => conn,
        Err(e) => {
            error!("[取码函数] 数据库连接获取失败: {:?}", e);
            return HashMap::from([
                ("errcode".to_string(), json!(1)),
                ("errmsg".to_string(), json!("数据库连接获取失败")),
            ]);
        }
    };

    // 根据订单ID获取订单信息
    let order_sql =
        "SELECT TOP 1 id, productId, poNumber, batchNo FROM [produce_order] WHERE id = @P1";

    let log_order_sql = order_sql.replace("@P1", &format!("'{}'", id));
    info!("[取码函数] 查询生产订单信息SQL: {}", log_order_sql);

    // 执行查询
    let order_rows = match conn.query(order_sql, &[&id]).await {
        Ok(result) => match result.into_first_result().await {
            Ok(rows) => {
                if rows.is_empty() {
                    return HashMap::from([
                        ("errcode".to_string(), json!(1)),
                        ("errmsg".to_string(), json!("生产订单信息不存在")),
                    ]);
                }
                rows
            }
            Err(e) => {
                error!("[取码函数] 处理生产订单查询结果失败: {}", e);
                return HashMap::from([
                    ("errcode".to_string(), json!(1)),
                    ("errmsg".to_string(), json!("处理生产订单查询结果失败")),
                ]);
            }
        },
        Err(e) => {
            error!("[取码函数] 查询生产订单信息失败: {}", e);
            return HashMap::from([
                ("errcode".to_string(), json!(1)),
                ("errmsg".to_string(), json!("查询生产订单信息失败")),
            ]);
        }
    };

    // 获取订单信息
    let order_id = order_rows[0].get::<i32, _>("id").unwrap_or_default();
    let product_id = order_rows[0].get::<i32, _>("productId").unwrap_or_default();
    let po_number = order_rows[0].get::<&str, _>("poNumber").unwrap_or_default();
    let batch_no = order_rows[0].get::<&str, _>("batchNo").unwrap_or_default();

    // 检查订单是否存在
    if order_id == 0 {
        return HashMap::from([
            ("errcode".to_string(), json!(1)),
            ("errmsg".to_string(), json!("生产订单信息不存在")),
        ]);
    }

    // 根据产品ID获取产品信息
    let product_sql =
        "SELECT TOP 1 id, productCode, packageRules, codeStyle FROM [basic_product] WHERE id = @P1";

    let log_product_sql = product_sql.replace("@P1", &format!("'{}'", product_id));
    info!("[取码函数] 查询产品信息SQL: {}", log_product_sql);

    // 执行查询
    let product_rows = match conn.query(product_sql, &[&product_id]).await {
        Ok(result) => match result.into_first_result().await {
            Ok(rows) => {
                if rows.is_empty() {
                    return HashMap::from([
                        ("errcode".to_string(), json!(1)),
                        ("errmsg".to_string(), json!("产品信息不存在")),
                    ]);
                }
                rows
            }
            Err(e) => {
                error!("[取码函数] 处理产品查询结果失败: {}", e);
                return HashMap::from([
                    ("errcode".to_string(), json!(1)),
                    ("errmsg".to_string(), json!("处理产品查询结果失败")),
                ]);
            }
        },
        Err(e) => {
            error!("[取码函数] 查询产品信息失败: {}", e);
            return HashMap::from([
                ("errcode".to_string(), json!(1)),
                ("errmsg".to_string(), json!("查询产品信息失败")),
            ]);
        }
    };

    // 获取产品信息
    let product_id = product_rows[0].get::<i32, _>("id").unwrap_or_default();
    let product_code = product_rows[0]
        .get::<&str, _>("productCode")
        .unwrap_or_default();
    let package_rules_str = product_rows[0]
        .get::<&str, _>("packageRules")
        .unwrap_or_default();
    let code_style = product_rows[0]
        .get::<&str, _>("codeStyle")
        .unwrap_or_default();

    // 检查产品是否存在
    if product_id == 0 {
        return HashMap::from([
            ("errcode".to_string(), json!(1)),
            ("errmsg".to_string(), json!("产品信息不存在")),
        ]);
    }

    // 处理package_rules_str为HashMap<String, Value>
    let package_rules_array: Vec<Value> = match serde_json::from_str(package_rules_str) {
        Ok(array) => array,
        Err(_) => Vec::new(),
    };

    // 判断是否为零箱
    let partial = level == 3 && type_flag == 1;
    let custom_level = if partial { 5 } else { level };

    // 根据层级查找包装规则
    let level_data = package_rules_array.iter().find(|rule| {
        rule.get("level")
            .and_then(|l| l.as_i64())
            .map_or(false, |l| l as i32 == custom_level)
    });

    // 获取层级规则
    let level_data = match level_data {
        Some(data) => data,
        None => {
            return HashMap::from([
                ("errcode".to_string(), json!(1)),
                (
                    "errmsg".to_string(),
                    json!(format!("层级编码为{}的规则信息不存在", custom_level)),
                ),
            ]);
        }
    };

    // 获取标志位
    let filter_value = match level_data.get("filterValue") {
        Some(val) => val.as_str().unwrap_or_default(),
        None => {
            return HashMap::from([
                ("errcode".to_string(), json!(1)),
                ("errmsg".to_string(), json!("包装规则缺少标志位")),
            ]);
        }
    };

    // 获取GTIN
    let gtin = match level_data.get("GTIN") {
        Some(val) => val.as_str().unwrap_or_default(),
        None => {
            return HashMap::from([
                ("errcode".to_string(), json!(1)),
                ("errmsg".to_string(), json!("包装规则缺少GTIN")),
            ]);
        }
    };

    // 获取大箱规则（level为3的规则信息）
    let case_data = package_rules_array.iter().find(|rule| {
        rule.get("level")
            .and_then(|l| l.as_i64())
            .map_or(false, |l| l as i32 == 3)
    });

    if case_data.is_none() {
        return HashMap::from([
            ("errcode".to_string(), json!(1)),
            ("errmsg".to_string(), json!("层级编码为3的规则信息不存在")),
        ]);
    }

    // 获取层级名称
    let level_name = if partial {
        match case_data.unwrap().get("name") {
            Some(val) => val.as_str().unwrap_or_default(),
            None => {
                return HashMap::from([
                    ("errcode".to_string(), json!(1)),
                    ("errmsg".to_string(), json!("包装规则中层级名称不存在")),
                ]);
            }
        }
    } else {
        match level_data.get("name") {
            Some(val) => val.as_str().unwrap_or_default(),
            None => {
                return HashMap::from([
                    ("errcode".to_string(), json!(1)),
                    ("errmsg".to_string(), json!("包装规则中层级名称不存在")),
                ]);
            }
        }
    };

    // 检查层级名称是否为空
    if level_name.is_empty() {
        return HashMap::from([
            ("errcode".to_string(), json!(1)),
            ("errmsg".to_string(), json!("层级名称不能为空")),
        ]);
    }

    // 确定实际层级代码
    let level_code = if partial { 3 } else { level };

    // 查询现有序列码
    let transit_sql = format!(
        "SELECT TOP {} tc.id, tc.code, tc.snCode, tc.fileID 
        FROM [transit_code] tc
        JOIN [sn_record] sr ON sr.id = tc.fileID
        WHERE tc.orderId = @P1 
        AND tc.levelCode = @P2 
        AND tc.typeFlag = @P3 
        AND tc.codeFlag IN (0, 1)
        AND sr.filterValue = @P4
        AND sr.resCode = @P5
        AND sr.typeFlag = @P6
        AND sr.codeStyle = @P7
        ORDER BY tc.id",
        amount
    );

    let log_transit_sql = transit_sql
        .replace("@P1", &format!("'{}'", order_id))
        .replace("@P2", &format!("'{}'", level_code.to_string()))
        .replace("@P3", &format!("'{}'", type_flag))
        .replace("@P4", &format!("'{}'", filter_value))
        .replace("@P5", &format!("'{}'", gtin))
        .replace("@P6", &format!("'{}'", type_flag.to_string()))
        .replace("@P7", &format!("'{}'", code_style.to_string()));
    info!("[取码函数] 查询序列码信息SQL: {}", log_transit_sql);

    // 执行查询
    let transit_rows = match conn
        .query(
            &transit_sql,
            &[
                &order_id,
                &level_code,
                &type_flag,
                &filter_value,
                &gtin,
                &type_flag,
                &code_style,
            ],
        )
        .await
    {
        Ok(result) => match result.into_first_result().await {
            Ok(rows) => rows,
            Err(e) => {
                error!("[取码函数] 处理序列码查询结果失败: {}", e);
                Vec::new()
            }
        },
        Err(e) => {
            error!("[取码函数] 查询序列码信息失败: {}", e);
            Vec::new()
        }
    };

    // 处理现有序列码
    let mut transit_codes = Vec::with_capacity(transit_rows.len());
    for row in &transit_rows {
        let code = row.get::<&str, _>("code").unwrap_or_default();
        transit_codes.push(code.to_string());
    }

    info!(
        "[取码函数] 数据库中符合条件的序列码数量: {}",
        transit_codes.len()
    );

    // 如果现有序列码足够，直接返回
    if transit_codes.len() >= amount as usize {
        let codes = transit_codes
            .into_iter()
            .take(amount as usize)
            .collect::<Vec<String>>();

        return HashMap::from([
            ("errcode".to_string(), json!(0)),
            ("errmsg".to_string(), json!("序列码获取成功")),
            ("data".to_string(), json!(codes)),
        ]);
    }

    // 计算需要申请的序列码数量
    let remain_amount = amount as usize - transit_codes.len();
    info!("[取码函数] 需要申请的序列码数量: {}", remain_amount);

    // 查询可用的序列码
    let accord_sql = format!(
        "SELECT TOP {} sc.id, sc.code, sc.snCode, sc.fileID 
        FROM [sn_code] sc
        JOIN [sn_record] sr ON sr.id = sc.fileID
        WHERE sc.filterValue = @P1 
        AND sc.resCode = @P2
        AND sc.estate = 0
        AND sc.codeStyle = @P3
        AND sr.typeFlag = @P4",
        remain_amount
    );

    let log_accord_sql = accord_sql
        .replace("@P1", &format!("'{}'", filter_value))
        .replace("@P2", &format!("'{}'", gtin))
        .replace("@P3", &format!("'{}'", code_style.to_string()))
        .replace("@P4", &format!("'{}'", type_flag.to_string()));
    info!("[取码函数] 查询可用序列码信息SQL: {}", log_accord_sql);

    // 执行查询
    let accord_rows = match conn
        .query(
            &accord_sql,
            &[&filter_value, &gtin, &code_style, &type_flag],
        )
        .await
    {
        Ok(result) => match result.into_first_result().await {
            Ok(rows) => rows,
            Err(e) => {
                error!("[取码函数] 处理可用序列码查询结果失败: {}", e);
                Vec::new()
            }
        },
        Err(e) => {
            error!("[取码函数] 查询可用序列码信息失败: {}", e);
            Vec::new()
        }
    };

    // 检查可用序列码是否足够
    if accord_rows.len() < remain_amount {
        return HashMap::from([
            ("errcode".to_string(), json!(1)),
            (
                "errmsg".to_string(),
                json!("序列码数量不足，请重新申请序列码"),
            ),
        ]);
    }

    // 处理新申请的序列码
    let mut new_codes = Vec::with_capacity(accord_rows.len());
    let mut code_ids = Vec::with_capacity(accord_rows.len());
    let mut insert_values = Vec::<String>::with_capacity(accord_rows.len());
    let mark = if partial { 1 } else { 0 };

    for row in &accord_rows {
        let id = row.get::<i32, _>("id").unwrap_or_default();
        let code = row.get::<&str, _>("code").unwrap_or_default();
        let sn_code = row.get::<&str, _>("snCode").unwrap_or_default();
        let file_id = row.get::<i32, _>("fileID").unwrap_or_default();

        new_codes.push(code.to_string());
        code_ids.push(id);

        // 构建插入语句的值部分
        insert_values.push(format!(
            "(1, '{}', '{}', {}, '{}', {}, '{}', '{}', '{}', '{}', {}, 0, 0, {}, 0, {}, {})",
            po_number,
            product_code,
            order_id,
            batch_no,
            level,
            level_name,
            gtin,
            sn_code,
            code,
            type_flag,
            mark,
            file_id,
            mark
        ));
    }

    // 如果没有需要插入的值，直接返回现有序列码
    if insert_values.is_empty() {
        return HashMap::from([
            ("errcode".to_string(), json!(0)),
            ("errmsg".to_string(), json!("序列码获取成功")),
            ("data".to_string(), json!(transit_codes)),
        ]);
    }

    // 更新序列码状态
    let code_ids_str = code_ids
        .iter()
        .map(|&x| x.to_string())
        .collect::<Vec<String>>()
        .join(",");

    let update_status_sql = format!(
        "UPDATE [sn_code] SET estate = 1, updateTime = GETDATE() WHERE id IN ({})",
        code_ids_str
    );

    let log_status_sql = update_status_sql.replace("IN ({})", &format!("IN ({})", code_ids_str));
    info!("[取码函数] 更新序列码状态SQL: {}", log_status_sql);

    // 执行更新
    if let Err(e) = conn.execute(&update_status_sql, &[]).await {
        error!("[取码函数] 更新序列码状态失败: {}", e);
        return HashMap::from([
            ("errcode".to_string(), json!(1)),
            ("errmsg".to_string(), json!("更新序列码状态失败")),
        ]);
    }

    // 插入新序列码到transit_code表
    let insert_sql = format!(
        "INSERT INTO [transit_code] (dataID, poNumber, productCode, orderId, batchNo, levelCode, levelName, resCode, snCode, code, typeFlag, reqID, codeFlag, boxFlag, estate, fileID, zeroBox) VALUES {}",
        insert_values.join(", ")
    );

    let log_insert_sql =
        insert_sql.replace("VALUES {}", &format!("VALUES {}", insert_values.join(", ")));
    info!("[取码函数] 插入序列码记录SQL: {}", log_insert_sql);

    // 执行插入
    if let Err(e) = conn.execute(&insert_sql, &[]).await {
        error!("[取码函数] 插入序列码记录失败: {}", e);
        // 回滚序列码状态
        let rollback_sql = format!(
            "UPDATE [sn_code] SET estate = 0, updateTime = GETDATE() WHERE id IN ({})",
            code_ids_str
        );
        let _ = conn.execute(&rollback_sql, &[]).await;

        return HashMap::from([
            ("errcode".to_string(), json!(1)),
            ("errmsg".to_string(), json!("插入序列码记录失败")),
        ]);
    }

    // 更新sn_record表的freeAmount
    let update_free_amount_sql = "
        UPDATE sr
        SET sr.freeAmount = ISNULL(sc.number, 0)
        FROM [sn_record] AS sr
        LEFT JOIN
            (SELECT fileID,
                count(*) AS number
            FROM [sn_code]
            WHERE estate = 0
            GROUP BY fileID
            ) AS sc ON sr.id = sc.fileID
    ";

    let log_free_amount_sql =
        update_free_amount_sql.replace("IN ({})", &format!("IN ({})", code_ids_str));
    info!("[取码函数] 更新可用数量SQL: {}", log_free_amount_sql);

    // 执行更新
    if let Err(e) = conn.execute(update_free_amount_sql, &[]).await {
        error!("[取码函数] 更新可用数量失败: {}", e);
        // 不影响主流程，继续执行
    }

    // 合并所有序列码
    let mut all_codes = transit_codes;
    all_codes.extend(new_codes);

    // 返回成功结果
    HashMap::from([
        ("errcode".to_string(), json!(0)),
        ("errmsg".to_string(), json!("序列码获取成功")),
        ("data".to_string(), json!(all_codes)),
    ])
}
