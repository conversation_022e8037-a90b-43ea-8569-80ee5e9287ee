use crate::app::providers::DatabaseServiceProvider;
use crate::resources::views::WelcomeTemplate;
use askama::Template;
use axum::{response::Html, routing::get, Router};
use std::sync::Arc;

/// Web 路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().route("/", get(welcome))
}

/// 欢迎页面
async fn welcome() -> Result<Html<String>, axum::http::StatusCode> {
    let template = WelcomeTemplate;
    template
        .render()
        .map(Html)
        .map_err(|_| axum::http::StatusCode::INTERNAL_SERVER_ERROR)
}
