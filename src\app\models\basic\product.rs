use crate::app::traits::DateTime;
use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::collections::{HashMap, HashSet};
use std::sync::OnceLock;
use tiberius::Row;

/// 产品模型
///
/// # 主要功能
/// - 智能字段检测：根据查询自动识别和处理字段
/// - 部分字段查询：只返回实际查询的字段，实现"查询多少，返回多少"
/// - 动态字段存储：处理非标准字段，适应不同业务需求
/// - 高性能数据处理：优化大批量数据加载和处理
/// - 类型安全：提供类型安全的动态字段访问
///
/// # 用法示例
///
/// ```rust
/// // 查询和加载数据
/// let row = db.query("SELECT id, productName FROM products WHERE id = 1").await?.into_row()?;
/// let product = Product::renovation(&row);
///
/// // 访问标准属性
/// println!("产品名称: {}", product.product_name);
///
/// // 使用动态字段
/// if let Some(value) = product.get_dynamic::<String>("customField") {
///     println!("自定义字段: {}", value);
/// }
///
/// // 序列化为JSON响应（只包含查询的字段）
/// let json = product.to_response();
/// ```
#[derive(Debug, Serialize, Deserialize, Default)]
pub struct Product {
    /// 产品ID
    pub id: i32,
    /// 公司ID
    #[serde(rename = "companyId")]
    pub company_id: i32,
    /// 工厂ID
    #[serde(rename = "plantId")]
    pub plant_id: i32,
    /// 产品名称
    #[serde(rename = "productName")]
    pub product_name: String,
    /// 产品编码
    #[serde(rename = "productCode")]
    pub product_code: String,
    /// NDC编码
    pub ndc: String,
    /// 物料代码
    pub material: Option<String>,
    /// 剂型
    #[serde(rename = "dosageForm")]
    pub dosage_form: Option<String>,
    /// 包装规格
    #[serde(rename = "dosageUsage")]
    pub dosage_usage: Option<String>,
    /// 浓度
    pub strength: Option<String>,
    /// 产品规格
    #[serde(rename = "packageSize")]
    pub package_size: Option<String>,
    /// 通用名称
    pub generic: Option<String>,
    /// 成分
    pub composition: Option<String>,
    /// 单价
    #[serde(rename = "unitPrice")]
    pub unit_price: Option<String>,
    /// 描述
    pub description: Option<String>,
    /// 是否计划
    pub scheduled: u8,
    /// 图片目录
    #[serde(rename = "imageDir")]
    pub image_dir: Option<String>,
    /// 接收者编码
    #[serde(rename = "receiverCode")]
    pub receiver_code: Option<String>,
    /// 存储条件
    pub remark: Option<String>,
    /// 主要计数
    #[serde(rename = "primaryCount")]
    pub primary_count: i32,
    /// 产品类型
    #[serde(rename = "productType")]
    pub product_type: u8,
    /// 国家
    pub country: Option<String>,
    /// 包装规则
    #[serde(rename = "packageRules")]
    pub package_rules: Option<Vec<Value>>,
    /// 状态
    pub estate: u8,
    /// 创建时间
    #[serde(rename = "createTime")]
    pub create_time: DateTime,
    /// 更新时间
    #[serde(rename = "updateTime")]
    pub update_time: DateTime,
    /// 包装比例
    #[serde(rename = "packageRatio")]
    pub package_ratio: String,
    /// 保质期
    #[serde(rename = "shelfLife")]
    pub shelf_life: i32,
    /// 产品编码类型
    #[serde(rename = "productCodeType")]
    pub product_code_type: Option<String>,
    /// 产品编码值
    #[serde(rename = "productCodeValue")]
    pub product_code_value: Option<String>,
    /// EAN编码
    pub eancode: Option<String>,
    /// 发送状态
    #[serde(rename = "sendStatus")]
    pub send_status: i32,
    /// 日期格式
    #[serde(rename = "dateFormat")]
    pub date_format: String,
    /// 编码样式
    #[serde(rename = "codeStyle")]
    pub code_style: String,
    /// 打印来源
    #[serde(rename = "printFrom")]
    pub print_from: Option<String>,
    /// 打印标志
    #[serde(rename = "printLogo")]
    pub print_logo: Option<String>,
    /// 打印市场
    #[serde(rename = "printMarket")]
    pub print_market: Option<String>,
    /// 包装规格
    #[serde(rename = "packageSpec")]
    pub package_spec: Option<String>,
    /// 配方号
    #[serde(rename = "recipeNo")]
    pub recipe_no: Option<String>,
    /// 制造商
    pub manufacturer: Option<String>,
    /// 分销商地址
    #[serde(rename = "distributorBy")]
    pub distributor_by: Option<String>,
    /// 制造商地址
    #[serde(rename = "manufacturerBy")]
    pub manufacturer_by: Option<String>,
    /// 分销商
    pub distributor: Option<String>,
    /// 动态字段存储，用于存储非标准字段
    #[serde(flatten, skip_serializing_if = "Option::is_none")]
    pub dynamic_fields: Option<HashMap<String, Value>>,
    /// 记录实际查询的字段，用于序列化时过滤
    #[serde(skip)]
    pub queried_fields: Option<HashSet<String>>,
}

impl Product {
    /// 获取标准字段集合
    ///
    /// 返回系统预定义的所有标准字段名称集合，用于区分标准字段和动态字段。
    /// 该方法使用OnceLock实现懒加载单例模式，确保字段集合只初始化一次。
    ///
    /// # 返回值
    /// 返回包含所有标准字段名称的静态HashSet引用
    #[inline]
    pub fn standard_fields() -> &'static HashSet<&'static str> {
        static FIELDS: OnceLock<HashSet<&'static str>> = OnceLock::new();

        FIELDS.get_or_init(|| {
            [
                "id",
                "companyId",
                "plantId",
                "productName",
                "productCode",
                "ndc",
                "material",
                "dosageForm",
                "dosageUsage",
                "strength",
                "packageSize",
                "generic",
                "composition",
                "unitPrice",
                "description",
                "scheduled",
                "imageDir",
                "receiverCode",
                "remark",
                "primaryCount",
                "productType",
                "country",
                "packageRules",
                "estate",
                "createTime",
                "updateTime",
                "packageRatio",
                "shelfLife",
                "productCodeType",
                "productCodeValue",
                "eancode",
                "sendStatus",
                "dateFormat",
                "codeStyle",
                "printFrom",
                "printLogo",
                "printMarket",
                "packageSpec",
                "recipeNo",
                "manufacturer",
                "distributorBy",
                "manufacturerBy",
                "distributor",
                "plantName",
                "companyPrefix",
                "RowNum",
            ]
            .into_iter()
            .collect()
        })
    }

    /// 从数据库行创建产品实例
    ///
    /// 支持指定要加载的字段和动态字段，只加载需要的数据以提高性能。
    /// 记录查询的字段，用于实现"查询多少，返回多少"的原则。
    ///
    /// # 参数
    /// * `row` - 数据库查询结果行
    /// * `fields` - 可选参数，指定要转换的字段集合
    /// * `dynamic_fields` - 可选参数，指定要提取的动态字段
    ///
    /// # 返回值
    /// 返回产品实例
    pub fn from_row(
        row: &Row,
        fields: Option<&HashSet<String>>,
        dynamic_fields: Option<&HashSet<String>>,
    ) -> Self {
        // 创建字段读取函数
        let should_read = |field: &str| -> bool { fields.map_or(true, |f| f.contains(field)) };

        // 构建产品实例
        let mut product = Self::build_from_row(row, &should_read);

        // 记录查询的字段
        product.queried_fields = fields.cloned();

        // 处理动态字段
        if let Some(dynamic_field_set) = dynamic_fields {
            if !dynamic_field_set.is_empty() {
                product.dynamic_fields = Self::extract_dynamic_fields(row, dynamic_field_set);

                // 如果有查询字段集合，将动态字段也添加进去
                if let Some(ref mut queried) = product.queried_fields {
                    for field in dynamic_field_set {
                        queried.insert(field.clone());
                    }
                }
            }
        }

        product
    }

    /// 判断产品是否为部分加载，用于确定是否需要补充数据
    ///
    /// 通过检查关键字段（ID、产品名称、产品编码）是否为空或默认值，
    /// 判断当前产品实例是否只包含部分数据，需要进一步补充。
    ///
    /// # 返回值
    /// * `true` - 产品为部分加载状态，缺少关键数据
    /// * `false` - 产品包含完整数据
    #[inline]
    pub fn is_partial(&self) -> bool {
        self.id <= 0 || self.product_name.is_empty() || self.product_code.is_empty()
    }

    /// 合并产品数据，用于补充部分加载的产品
    ///
    /// 当产品只包含部分字段时，使用此方法从另一个完整的产品实例中补充缺失的数据。
    /// 只有当前实例中字段为空或默认值时才会从另一个实例获取值，避免覆盖已有数据。
    /// 这对于实现增量加载或分步查询场景非常有用。
    ///
    /// # 参数
    /// * `other` - 包含完整数据的产品实例
    ///
    /// # 返回值
    /// * `true` - 进行了合并操作，有字段被更新
    /// * `false` - 未进行合并操作，当前实例已完整或无法从other中获取更多数据
    pub fn merge(&mut self, other: &Self) -> bool {
        // 如果当前不是部分加载，则无需合并
        if !self.is_partial() {
            return false;
        }

        let mut merged = false;

        // 合并所有必要字段
        if self.id <= 0 && other.id > 0 {
            self.id = other.id;
            merged = true;
        }

        Self::merge_string_field(&mut self.product_name, &other.product_name, &mut merged);
        Self::merge_string_field(&mut self.product_code, &other.product_code, &mut merged);
        Self::merge_string_field(&mut self.ndc, &other.ndc, &mut merged);
        Self::merge_string_field(&mut self.package_ratio, &other.package_ratio, &mut merged);
        Self::merge_string_field(&mut self.date_format, &other.date_format, &mut merged);
        Self::merge_string_field(&mut self.code_style, &other.code_style, &mut merged);

        if self.company_id <= 0 && other.company_id > 0 {
            self.company_id = other.company_id;
            merged = true;
        }

        if self.plant_id <= 0 && other.plant_id > 0 {
            self.plant_id = other.plant_id;
            merged = true;
        }

        // 合并动态字段
        if let Some(other_fields) = &other.dynamic_fields {
            if self.dynamic_fields.is_none() {
                self.dynamic_fields = Some(other_fields.clone());
                merged = true;
            } else if let Some(self_fields) = &mut self.dynamic_fields {
                for (key, value) in other_fields {
                    if !self_fields.contains_key(key) {
                        self_fields.insert(key.clone(), value.clone());
                        merged = true;
                    }
                }
            }
        }

        merged
    }

    /// 获取动态字段值
    ///
    /// 将动态字段值转换为指定类型并返回，支持任意可反序列化的类型。
    /// 提供类型安全的方式访问动态字段。
    ///
    /// # 类型参数
    /// * `T` - 目标类型，必须实现DeserializeOwned特征
    ///
    /// # 参数
    /// * `field` - 字段名称
    ///
    /// # 返回值
    /// * `Some(T)` - 字段存在且可以转换为指定类型时
    /// * `None` - 字段不存在或无法转换为指定类型时
    #[inline]
    pub fn get_dynamic<T>(&self, field: &str) -> Option<T>
    where
        T: serde::de::DeserializeOwned,
    {
        self.dynamic_fields
            .as_ref()
            .and_then(|fields| fields.get(field))
            .and_then(|v| serde_json::from_value(v.clone()).ok())
    }

    /// 设置动态字段值
    ///
    /// 将任意可序列化的值存储到产品的动态字段中。
    /// 允许扩展产品模型，存储标准结构之外的任何数据。
    ///
    /// # 类型参数
    /// * `T` - 值类型，必须实现Serialize特征
    ///
    /// # 参数
    /// * `field` - 字段名称
    /// * `value` - 要设置的值，可以是任何可序列化类型
    ///
    /// # 返回值
    /// * `Ok(())` - 设置成功
    /// * `Err(e)` - 序列化过程中发生错误
    #[inline]
    pub fn set_dynamic<T>(&mut self, field: &str, value: T) -> Result<(), serde_json::Error>
    where
        T: Serialize,
    {
        let json_value = serde_json::to_value(value)?;

        if self.dynamic_fields.is_none() {
            self.dynamic_fields = Some(HashMap::new());
        }

        if let Some(ref mut fields) = self.dynamic_fields {
            fields.insert(field.to_string(), json_value);
        }

        Ok(())
    }

    /// 智能转换数据库结果为产品实例
    ///
    /// 自动分析查询结果包含的字段，只返回查询的字段。
    /// 是数据加载的主要入口点，实现"查询多少，返回多少"的需求。
    ///
    /// # 类型参数
    /// * `T` - 实现了DataConversion特征的数据类型
    ///
    /// # 参数
    /// * `data` - 数据库查询结果行或行的集合
    ///
    /// # 返回值
    /// * 产品结构体或结构体集合
    #[inline]
    pub fn renovation<T>(data: &T) -> T::Output
    where
        T: DataConversion,
    {
        data.convert()
    }

    /// 将产品对象序列化为JSON响应
    ///
    /// 根据查询的字段自动过滤，只返回实际查询的字段。
    /// 实现"查询多少，返回多少"的原则，避免返回未查询的字段。
    ///
    /// # 返回值
    /// 返回只包含实际查询字段的JSON对象
    pub fn to_response(&self) -> serde_json::Value {
        // 获取原始序列化结果
        let json = serde_json::to_value(self).unwrap_or(serde_json::Value::Null);

        if let serde_json::Value::Object(obj) = json {
            let filtered: serde_json::Map<String, serde_json::Value> = match &self.queried_fields {
                // 如果有记录查询字段，只返回这些字段
                Some(queried) => obj
                    .into_iter()
                    .filter(|(key, _value)| {
                        // 转换字段名以匹配查询字段名
                        let field_name = if key.contains('_') {
                            // 转换蛇形命名为驼峰命名，匹配SQL查询字段名
                            let parts: Vec<&str> = key.split('_').collect();
                            let mut camel_case = parts[0].to_string();
                            for part in &parts[1..] {
                                camel_case.push_str(&part[0..1].to_uppercase());
                                camel_case.push_str(&part[1..]);
                            }
                            camel_case
                        } else {
                            key.clone()
                        };

                        // 只保留查询的字段，不论值是什么
                        queried.contains(&field_name)
                    })
                    .collect(),
                // 如果没有记录查询字段，过滤掉null和默认值
                None => obj
                    .into_iter()
                    .filter(|(key, value)| {
                        // 排除null值
                        if value.is_null() {
                            return false;
                        }

                        // 排除空字符串
                        if let serde_json::Value::String(s) = value {
                            if s.is_empty() {
                                return false;
                            }
                        }

                        // 排除0值（对于可选字段）
                        if let serde_json::Value::Number(n) = value {
                            if let Some(i) = n.as_i64() {
                                if i == 0 && !["id", "scheduled", "estate"].contains(&key.as_str())
                                {
                                    return false;
                                }
                            }
                        }

                        true
                    })
                    .collect(),
            };

            serde_json::Value::Object(filtered)
        } else {
            json
        }
    }

    // 私有辅助方法

    /// 从行数据构建产品实例
    ///
    /// 根据指定的字段筛选条件，从数据库行中提取数据并构建产品实例。
    /// 此方法会处理各种类型转换和数据格式问题，确保安全地读取数据。
    ///
    /// # 参数
    /// * `row` - 数据库查询结果行
    /// * `should_read` - 函数指针，判断是否应该读取特定字段
    ///
    /// # 返回值
    /// 返回构建的产品实例
    #[inline]
    fn build_from_row(row: &Row, should_read: &impl Fn(&str) -> bool) -> Self {
        // 定义读取函数
        let get_i32 = |field: &str, default: i32| -> i32 {
            if !should_read(field) {
                return default;
            }
            // 首先尝试直接获取 i32 类型
            match row.get::<i32, _>(field) {
                Some(value) => value,
                // 如果失败，尝试获取字符串并解析为 i32
                None => {
                    if let Some(str_val) = row.get::<&str, _>(field) {
                        str_val.parse::<i32>().unwrap_or(default)
                    } else {
                        default
                    }
                }
            }
        };

        let get_u8 = |field: &str, default: u8| -> u8 {
            if !should_read(field) {
                return default;
            }
            // 首先尝试直接获取 u8 类型
            match row.get::<u8, _>(field) {
                Some(value) => value,
                // 如果失败，尝试获取字符串并解析为 u8
                None => {
                    if let Some(str_val) = row.get::<&str, _>(field) {
                        str_val.parse::<u8>().unwrap_or(default)
                    } else {
                        default
                    }
                }
            }
        };

        let get_string = |field: &str| -> String {
            if !should_read(field) {
                return String::new();
            }
            row.get::<&str, _>(field).unwrap_or_default().to_string()
        };

        let get_option_string = |field: &str| -> Option<String> {
            if !should_read(field) {
                return None;
            }
            row.get::<&str, _>(field).map(|s| s.to_string())
        };

        let get_datetime = |field: &str| -> DateTime {
            if !should_read(field) {
                return DateTime(NaiveDateTime::default());
            }
            match row.get::<&str, _>(field) {
                Some(s) => {
                    // 尝试解析日期时间，如果失败则返回默认值
                    match DateTime::parse(s, "%Y-%m-%d %H:%M:%S") {
                        Some(dt) => dt,
                        None => DateTime(NaiveDateTime::default()),
                    }
                }
                None => DateTime(NaiveDateTime::default()),
            }
        };

        let get_json = |field: &str| -> Option<Vec<Value>> {
            if !should_read(field) {
                return None;
            }
            match row.get::<&str, _>(field) {
                Some(s) => {
                    // 尝试解析为JSON，如果失败则返回None
                    match serde_json::from_str::<Vec<Value>>(s) {
                        Ok(json) => Some(json),
                        Err(_) => None,
                    }
                }
                None => None,
            }
        };

        // 创建产品实例
        Self {
            id: get_i32("id", 0),
            company_id: get_i32("companyId", 0),
            plant_id: get_i32("plantId", 0),
            product_name: get_string("productName"),
            product_code: get_string("productCode"),
            ndc: get_string("ndc"),
            material: get_option_string("material"),
            dosage_form: get_option_string("dosageForm"),
            dosage_usage: get_option_string("dosageUsage"),
            strength: get_option_string("strength"),
            package_size: get_option_string("packageSize"),
            generic: get_option_string("generic"),
            composition: get_option_string("composition"),
            unit_price: get_option_string("unitPrice"),
            description: get_option_string("description"),
            scheduled: get_u8("scheduled", 0),
            image_dir: get_option_string("imageDir"),
            receiver_code: get_option_string("receiverCode"),
            remark: get_option_string("remark"),
            primary_count: get_i32("primaryCount", 0),
            product_type: get_u8("productType", 0),
            country: get_option_string("country"),
            package_rules: get_json("packageRules"),
            estate: get_u8("estate", 0),
            create_time: get_datetime("createTime"),
            update_time: get_datetime("updateTime"),
            package_ratio: get_string("packageRatio"),
            shelf_life: get_i32("shelfLife", 0),
            product_code_type: get_option_string("productCodeType"),
            product_code_value: get_option_string("productCodeValue"),
            eancode: get_option_string("eancode"),
            send_status: get_i32("sendStatus", 0),
            date_format: get_string("dateFormat"),
            code_style: get_string("codeStyle"),
            print_from: get_option_string("printFrom"),
            print_logo: get_option_string("printLogo"),
            print_market: get_option_string("printMarket"),
            package_spec: get_option_string("packageSpec"),
            recipe_no: get_option_string("recipeNo"),
            manufacturer: get_option_string("manufacturer"),
            distributor_by: get_option_string("distributorBy"),
            manufacturer_by: get_option_string("manufacturerBy"),
            distributor: get_option_string("distributor"),
            dynamic_fields: None,
            queried_fields: None,
        }
    }

    /// 提取动态字段
    #[inline]
    fn extract_dynamic_fields(
        row: &Row,
        fields: &HashSet<String>,
    ) -> Option<HashMap<String, Value>> {
        let mut result = HashMap::new();
        let standard_fields = Self::standard_fields();

        for field in fields {
            // 只处理非标准字段
            if !standard_fields.contains(field.as_str()) {
                if let Some(value) = Self::extract_field_value(row, field) {
                    result.insert(field.clone(), value);
                }
            }
        }

        if result.is_empty() {
            None
        } else {
            Some(result)
        }
    }

    /// 提取字段值
    #[inline]
    fn extract_field_value(row: &Row, field: &str) -> Option<Value> {
        // 尝试不同类型的提取
        if let Some(val) = row.get::<i32, _>(field) {
            return Some(Value::from(val));
        }

        if let Some(val) = row.get::<u8, _>(field) {
            return Some(Value::from(val));
        }

        if let Some(val) = row.get::<&str, _>(field) {
            // 尝试将字符串解析为数字
            if let Ok(num) = val.parse::<i32>() {
                return Some(Value::from(num));
            }

            if let Ok(num) = val.parse::<u8>() {
                return Some(Value::from(num));
            }

            // 尝试解析为JSON
            if let Ok(json) = serde_json::from_str::<Value>(val) {
                return Some(json);
            }

            // 尝试解析为日期时间
            if let Some(dt) = DateTime::parse(val, "%Y-%m-%d %H:%M:%S") {
                return Some(Value::String(dt.0.to_string()));
            }

            // 作为普通字符串处理
            return Some(Value::String(val.to_string()));
        }

        None
    }

    /// 合并字符串字段，避免重复代码
    #[inline]
    fn merge_string_field(target: &mut String, source: &str, merged: &mut bool) {
        if target.is_empty() && !source.is_empty() {
            *target = source.to_string();
            *merged = true;
        }
    }
}

/// 数据转换特征
///
/// 为产品模型提供统一的数据转换接口，使单行查询和批量查询
/// 可以使用相同的处理逻辑，简化代码并提高一致性。
pub trait DataConversion {
    /// 转换后的输出类型
    type Output;

    /// 执行数据转换
    fn convert(&self) -> Self::Output;

    /// 获取当前行对象的引用
    fn row(&self) -> Option<&Row>;
}

/// 单行数据转换实现
///
/// 将单个数据库行转换为产品实例，只加载查询到的字段。
/// 记录查询的字段，用于实现"查询多少，返回多少"的原则。
impl DataConversion for Row {
    type Output = Product;

    fn convert(&self) -> Self::Output {
        // 获取行中所有可用的列名
        let available_columns: HashSet<_> = self
            .columns()
            .iter()
            .map(|c| c.name().to_string())
            .collect();

        // 只读取存在于查询结果中的字段
        let fields = Some(&available_columns);

        // 获取所有可能的动态字段
        let mut dynamic_fields = HashSet::new();
        let standard_fields = Product::standard_fields();

        for field in &available_columns {
            if !standard_fields.contains(field.as_str()) {
                dynamic_fields.insert(field.clone());
            }
        }

        // 使用from_row方法创建产品实例，只填充查询到的字段
        let mut product = Product::from_row(
            self,
            fields,
            if dynamic_fields.is_empty() {
                None
            } else {
                Some(&dynamic_fields)
            },
        );

        // 确保queried_fields被设置为可用列名
        product.queried_fields = Some(available_columns);

        product
    }

    fn row(&self) -> Option<&Row> {
        Some(self)
    }
}

/// 批量数据转换实现
///
/// 将多个数据库行转换为产品实例集合，用于批量处理查询结果。
impl DataConversion for [Row] {
    type Output = Vec<Product>;

    fn convert(&self) -> Self::Output {
        self.iter().map(|row| row.convert()).collect()
    }

    fn row(&self) -> Option<&Row> {
        None
    }
}
