/// 数据库连接池 Facade
///
/// 提供对数据库连接池的全局访问。
/// 使用方式：
/// ```rust
/// let pool = DB::get_pool().await;
/// let conn = pool.get().await?;
/// ```
/// 或者直接使用连接：
/// ```rust
/// let result = DB::with_connection(|conn| {
///     // 使用连接执行操作
///     conn.query("SELECT * FROM users", &[]).await
/// }).await?;
/// ```
use super::manager::{get_facade_manager, Facade};
use bb8::Pool;
use bb8_tiberius::{ConnectionManager, Error};
use std::future::Future;
use std::sync::Arc;

/// 数据库 Facade
///
/// 该结构体实现了 Facade 特征，提供了对数据库连接池的访问。
/// 连接池使用 bb8 作为连接池管理器，tiberius 作为 SQL Server 驱动。
pub struct DB;

impl DB {
    /// 获取数据库连接池
    ///
    /// 返回一个被 Arc 包装的连接池实例，可以安全地在线程间共享。
    /// 连接池会自动管理连接的创建、复用和销毁。
    ///
    /// # 返回值
    /// - `Arc<Pool<ConnectionManager>>`: 线程安全的连接池引用
    ///
    /// # Panics
    /// - 如果连接池未在服务容器中注册，将会 panic
    /// - 如果无法获取 Facade 管理器锁，将会 panic
    pub async fn get_pool() -> Arc<Pool<ConnectionManager>> {
        Self::get_instance()
            .downcast::<Pool<ConnectionManager>>()
            .expect("无法获取数据库连接池")
    }

    /// 使用数据库连接执行操作
    ///
    /// 这是一个便捷方法，用于获取数据库连接并执行操作，无需手动管理连接的生命周期。
    /// 该方法接受一个回调函数，该函数接收一个连接并返回一个 Future。
    ///
    /// # 参数
    /// - `f`: 一个接收连接并返回 Future 的函数
    ///
    /// # 返回值
    /// - 回调函数的返回值，如果获取连接失败，则返回 `bb8::RunError<Error>`
    ///
    /// # 示例
    /// ```rust
    /// let users = DB::get_connection(|conn| async move {
    ///     conn.query("SELECT * FROM users", &[]).await
    /// }).await?;
    /// ```
    pub async fn get_connection<F, Fut, R>(f: F) -> Result<R, bb8::RunError<Error>>
    where
        F: FnOnce(bb8::PooledConnection<ConnectionManager>) -> Fut,
        Fut: Future<Output = R>,
    {
        let pool = Self::get_pool().await;
        let conn = pool.get().await?;
        Ok(f(conn).await)
    }
}

impl Facade for DB {
    /// 获取 Facade 实例
    ///
    /// 从服务容器中获取数据库连接池实例。
    /// 该方法由 Facade 特征要求实现，用于支持 Facade 模式。
    ///
    /// # 返回值
    /// - `Arc<dyn std::any::Any + Send + Sync>`: 类型擦除的实例引用
    ///
    /// # Panics
    /// - 如果连接池未在服务容器中注册，将会 panic
    /// - 如果无法获取 Facade 管理器锁，将会 panic
    fn get_instance() -> Arc<dyn std::any::Any + Send + Sync> {
        get_facade_manager()
            .lock()
            .expect("无法获取 Facade 管理器锁")
            .get::<Pool<ConnectionManager>>()
            .expect("数据库连接池未注册")
    }
}
