use crate::app::models::basic::Product;
use crate::app::providers::DatabaseServiceProvider;
use crate::app::services::socket::client_service::{SocketClientConfig, SocketClientService};
use crate::app::traits::response::track;
use crate::app::utils::assistant;
use axum::{extract::State, response::IntoResponse};
use chrono::Utc;
use encoding::all::GBK;
use encoding::{DecoderTrap, EncoderTrap, Encoding};
use futures::StreamExt;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::Instant;
use tiberius::QueryItem;
use tracing::{error, info, warn};

/// 测试参数
#[derive(Debug, Deserialize, Serialize)]
pub struct TestParams {
    /// ID参数
    pub id: Option<i32>,
}

/// 测试参数
#[derive(Debug, Deserialize, Serialize)]
pub struct ApplyParams {
    /// ID参数
    pub id: Option<i32>,
    /// 层级
    pub level: Option<i32>,
    /// 申请数量
    pub amount: Option<i32>,
    /// 类型
    #[serde(rename = "type")]
    pub type_flag: Option<i32>,
}

/// 产品参数
#[derive(Debug, Deserialize, Serialize)]
pub struct ProductParams {
    /// ID参数
    pub id: Option<i32>,
}

/// Socket参数
#[derive(Debug, Deserialize, Serialize)]
pub struct SocketParams {
    /// 主机地址
    pub host: Option<String>,
    /// 端口号
    pub port: Option<u16>,
    /// 消息内容
    pub message: Option<String>,
    /// 打印类型
    pub print_type: Option<String>,
    /// 打印数量
    pub print_count: Option<String>,
    /// 是否使用默认消息
    pub use_default: Option<bool>,
}

/// 测试控制器
pub struct TestController;

impl TestController {
    /// 常规测试
    pub async fn test(
        axum::extract::Query(params): axum::extract::Query<TestParams>,
    ) -> impl IntoResponse {
        info!("开始测试: {:?}", params);
        // 此处模拟正常业务处理
        if let Some(id) = params.id {
            info!("处理ID为 {} 的数据", id);
            // 返回成功响应
            track()
                .victory()
                .message("处理成功")
                .data(serde_json::json!({
                    "id": id,
                    "time": Utc::now().to_rfc3339()
                }))
                .build()
        } else {
            // 返回错误响应
            track().defeat().message("参数错误").build()
        }
    }

    /// 测试获取条码
    pub async fn apply(
        axum::extract::Query(params): axum::extract::Query<ApplyParams>,
    ) -> impl IntoResponse {
        info!("开始测试获取条码: {:?}", params);

        // 检查必要参数
        let order_id = match params.id {
            Some(id) => id,
            None => {
                return track().defeat().message("缺少订单ID参数").build();
            }
        };

        let level = params.level.unwrap_or(3); // 默认使用大箱
        let apply = params.amount.unwrap_or(5); // 默认申请5个条码
        let type_flag = params.type_flag.unwrap_or(0); // 默认GTIN类型

        info!(
            "调用获取条码函数，参数: order_id={}, level={}, apply={}, type_flag={}",
            order_id, level, apply, type_flag
        );

        // 调用助手函数获取条码
        let result = assistant::get_code(order_id, level, apply, type_flag).await;

        // 检查结果
        let errcode = result.get("errcode").and_then(|v| v.as_i64()).unwrap_or(1);
        let errmsg = result
            .get("errmsg")
            .and_then(|v| v.as_str())
            .unwrap_or("未知错误");
        let data = match result.get("data").and_then(|v| v.as_array()) {
            Some(array) => array.clone(),
            None => Vec::new(),
        };

        if errcode == 0 {
            info!("获取条码成功");
            // 构建成功响应
            track()
                .victory()
                .status(errcode as i32)
                .message(errmsg)
                .data(data)
                .build()
        } else {
            error!("获取条码失败: {}", errmsg);
            // 构建失败响应
            track().defeat().message(errmsg).data(data).build()
        }
    }

    /// 测试产品模型
    pub async fn product(
        State(db): State<Arc<DatabaseServiceProvider>>,
        axum::extract::Query(params): axum::extract::Query<ProductParams>,
    ) -> impl IntoResponse {
        info!("开始测试产品模型: {:?}", params);

        let product_id = params.id.unwrap_or(1);
        info!("使用产品ID: {}", product_id);

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("数据库连接获取成功");
                conn
            }
            Err(e) => {
                warn!("数据库连接获取失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("数据库连接获取失败: {}", e))
                    .build();
            }
        };

        // 测试1：查询全字段（使用部分字段查询的方式，但包含所有字段）
        let sql_all = format!(
            r#"
            SELECT 
               id,
               companyId,
               plantId,
               productName,
               productCode,
               unitPrice,
               scheduled,
               composition,
               CONVERT(varchar(19), createTime, 120) as createTime,
               CONVERT(varchar(19), updateTime, 120) as updateTime
            FROM basic_product WITH (NOLOCK) 
            WHERE id = {}
            "#,
            product_id
        );
        info!("执行全字段查询: {}", sql_all);

        let product_all = {
            let result_all = conn.query(&sql_all, &[]).await;
            match result_all {
                Ok(mut stream) => {
                    // 处理查询结果流
                    let mut rows = Vec::new();

                    // 收集所有行数据
                    while let Some(item) = stream.next().await {
                        match item {
                            Ok(QueryItem::Row(row)) => {
                                info!("全字段查询获取到行数据");
                                rows.push(row);
                            }
                            Ok(QueryItem::Metadata(metadata)) => {
                                info!("全字段查询获取到元数据: {:?}", metadata);
                            }
                            Err(e) => {
                                warn!("全字段查询流处理错误: {}", e);
                            }
                        }
                    }

                    // 使用第一行数据
                    if let Some(row) = rows.first() {
                        info!("全字段查询成功，列数: {}", row.columns().len());
                        Some(Product::renovation(row))
                    } else {
                        warn!("全字段查询无结果，产品ID: {} 可能不存在", product_id);
                        None
                    }
                }
                Err(e) => {
                    warn!("全字段查询执行失败: {}", e);
                    None
                }
            }
        };

        // 测试2：只查询部分字段
        let sql_partial = format!(
            r#"
            SELECT 
                productName,
                productCode,
                unitPrice,
                scheduled,
                composition,
                CONVERT(varchar(19), createTime, 120) as createTime,
                CONVERT(varchar(19), updateTime, 120) as updateTime
            FROM basic_product WITH (NOLOCK)
            WHERE id = {}
            "#,
            product_id
        );
        info!("执行部分字段查询: {}", sql_partial);

        let product_partial = {
            let result_partial = conn.query(&sql_partial, &[]).await;
            match result_partial {
                Ok(mut stream) => {
                    // 处理查询结果流
                    let mut rows = Vec::new();

                    // 收集所有行数据
                    while let Some(item) = stream.next().await {
                        match item {
                            Ok(QueryItem::Row(row)) => {
                                info!("部分字段查询获取到行数据");
                                rows.push(row);
                            }
                            Ok(QueryItem::Metadata(metadata)) => {
                                info!("部分字段查询获取到元数据: {:?}", metadata);
                            }
                            Err(e) => {
                                warn!("部分字段查询流处理错误: {}", e);
                            }
                        }
                    }

                    // 使用第一行数据
                    if let Some(row) = rows.first() {
                        info!("部分字段查询成功，列数: {}", row.columns().len());

                        // 打印所有列名和值
                        for col in row.columns() {
                            let col_name = col.name();
                            match col.column_type() {
                                tiberius::ColumnType::Int1 => match row.get::<u8, _>(col_name) {
                                    Some(val) => info!("列 {}: 值 = {} (u8)", col_name, val),
                                    None => info!("列 {}: 值为null (u8)", col_name),
                                },
                                tiberius::ColumnType::Int4 => match row.get::<i32, _>(col_name) {
                                    Some(val) => info!("列 {}: 值 = {} (i32)", col_name, val),
                                    None => info!("列 {}: 值为null (i32)", col_name),
                                },
                                _ => match row.get::<&str, _>(col_name) {
                                    Some(val) => info!("列 {}: 值 = '{}'", col_name, val),
                                    None => info!("列 {}: 值为null或不是字符串类型", col_name),
                                },
                            }
                        }

                        Some(Product::renovation(row))
                    } else {
                        warn!("部分字段查询无结果，产品ID: {} 可能不存在", product_id);
                        None
                    }
                }
                Err(e) => {
                    warn!("部分字段查询执行失败: {}", e);
                    None
                }
            }
        };

        // 返回测试结果
        let all_success = product_all.is_some();
        let partial_success = product_partial.is_some();

        if all_success && partial_success {
            let full = product_all.unwrap();
            let partial = product_partial.unwrap();

            // 提前获取字段数量，避免部分移动
            let full_fields_count = full.queried_fields.as_ref().map(|f| f.len()).unwrap_or(0);
            let partial_fields_count = partial
                .queried_fields
                .as_ref()
                .map(|f| f.len())
                .unwrap_or(0);

            info!(
                "测试成功，全字段查询字段数: {}，部分字段查询字段数: {}",
                full_fields_count, partial_fields_count
            );

            track()
                .victory()
                .message("产品模型测试成功")
                .data(serde_json::json!({
                    "full_query": {
                        "fields_count": full_fields_count,
                        "response": full.to_response()
                    },
                    "partial_query": {
                        "fields_count": partial_fields_count,
                        "response": partial.to_response()
                    }
                }))
                .build()
        } else {
            let all_msg = if all_success { "成功" } else { "失败" };
            let partial_msg = if partial_success { "成功" } else { "失败" };

            warn!(
                "测试失败，全字段查询: {}，部分字段查询: {}",
                all_msg, partial_msg
            );

            track()
                .defeat()
                .message(format!(
                    "产品模型测试失败：全字段查询: {}，部分字段查询: {}",
                    all_msg, partial_msg
                ))
                .build()
        }
    }

    /// Socket连接测试
    pub async fn socket(
        State(_db): State<Arc<DatabaseServiceProvider>>,
        axum::extract::Query(params): axum::extract::Query<SocketParams>,
    ) -> impl IntoResponse {
        info!("开始测试Socket: {:?}", params);

        // 获取参数
        let host = params.host.unwrap_or_else(|| "127.0.0.1".to_string());
        let port = params.port.unwrap_or(7000);

        // 判断是否使用默认消息
        let json_data = if params.use_default.unwrap_or(true) {
            // 使用默认打印消息
            let print_type = params.print_type.unwrap_or_else(|| "3".to_string());
            let print_count = params.print_count.unwrap_or_else(|| "1".to_string());

            // 根据打印类型选择不同的打印模板
            match print_type.as_str() {
                "1" => {
                    // 构建默认消息(整箱)
                    serde_json::json!({
                        "PrintType": print_type,
                        "PrintCount": print_count,
                        "Message": [
                            {"Node": "distributor1", "Value": "Solco Healthcare US, LLC"},
                            {"Node": "ProductName", "Value": "Tablets, USP"},
                            {"Node": "PackageDose", "Value": "43547-276-11"},
                            {"Node": "PackageUnit", "Value": "20240625"},
                            {"Node": "VariableCount", "Value": "100"},
                            {"Node": "exp", "Value": "20240625"},
                            {"Node": "lot", "Value": "D1802220A"},
                            {"Node": "ndc", "Value": "43547-276-11"},
                            {"Node": "sn", "Value": "100000104204"},
                            {"Node": "gtin", "Value": "50343547347036"},
                            {"Node": "manufacturer2", "Value": "2044-01"},
                            {"Node": "manufacturerBy3", "Value": "jiangnan, Linhai, Zhejiang 317000, China"},
                            {"Node": "distributorBy4", "Value": "Somerset, NJ 08873, USA"},
                            {"Node": "remark", "Value": "Store at 20-25°C, excursions permitted to 15-30°C[see USP Controlled Room Temp.]."},
                            {"Node": "controlCode", "Value": "2044-01"}
                        ]
                    })
                    .to_string()
                }
                "2" => {
                    // 构建默认消息(零箱)
                    serde_json::json!({
                        "PrintType": print_type,
                        "PrintCount": print_count,
                        "Message": [
                            {"Node": "distributor1", "Value": "Solco Healthcare US, LLC"},
                            {"Node": "ProductName", "Value": "Tablets, USP"},
                            {"Node": "PackageDose", "Value": "43547-276-11"},
                            {"Node": "PackageUnit", "Value": "20240625"},
                            {"Node": "VariableCount", "Value": "100"},
                            {"Node": "exp", "Value": "20240625"},
                            {"Node": "lot", "Value": "D1802220A"},
                            {"Node": "ndc", "Value": "24979-727-06"},
                            {"Node": "code", "Value": "12345678901234567"},
                            {"Node": "manufacturer2", "Value": "2044-01"},
                            {"Node": "manufacturerBy3", "Value": "jiangnan, Linhai, Zhejiang 317000, China"},
                            {"Node": "distributor1", "Value": "Solco Healthcare US, LLC"},
                            {"Node": "distributorBy4", "Value": "Somerset, NJ 08873, USA"},
                            {"Node": "remark", "Value": "Store at 20-25°C, excursions permitted to 15-30°C[see USP Controlled Room Temp.]."},
                            {"Node": "controlCode", "Value": "2044-01"}
                        ]
                    })
                    .to_string()
                }
                "3" => {
                    // 构建默认消息(托盘)
                    serde_json::json!({
                        "PrintType": print_type,
                        "PrintCount": print_count,
                        "Message": [
                            {"Node": "manufacturer1", "Value": " Huahai Pharmaceutical Technology Co., Ltd."},
                            {"Node": "manufacturerBy2", "Value": "Jiangnan , Linhai , Zhejiang 317000 , China"},
                            {"Node": "ProductName", "Value": "Tablets, USP"},
                            {"Node": "ndc", "Value": "43547-276-11"},
                            {"Node": "exp", "Value": "20240625"},
                            {"Node": "lot", "Value": "D1802220A"},
                            {"Node": "Quantity", "Value": "40"},
                            {"Node": "code24", "Value": "003690970000128760"},
                            {"Node": "controlCode", "Value": "2044-01"}
                        ]
                    })
                    .to_string()
                }
                _ => {
                    // 不支持的打印类型
                    error!("不支持的打印类型: {}", print_type);
                    return track()
                        .defeat()
                        .message(format!(
                            "不支持的打印类型: {}，目前仅支持类型 1(整箱)、2(零箱)、3(托盘)",
                            print_type
                        ))
                        .build();
                }
            }
        } else {
            // 使用自定义消息
            params.message.unwrap_or_else(|| "".to_string())
        };

        info!("Socket测试参数: host={}, port={}", host, port);
        info!("发送数据: {}", json_data);

        // 将JSON字符串转换为GBK编码的字节
        let gbk_bytes = match GBK.encode(&json_data, EncoderTrap::Replace) {
            Ok(bytes) => bytes,
            Err(e) => {
                error!("GBK编码失败: {:?}", e);
                return track().defeat().message("GBK编码失败").build();
            }
        };

        // 创建Socket客户端配置
        let config = SocketClientConfig {
            host: host.clone(),
            port,
            read_timeout_ms: Some(10000), // 10秒读取超时
            read_buffer_size: 8192,       // 8KB读缓冲区
        };

        // 创建Socket客户端
        let client = SocketClientService::new(config);

        // 记录开始时间
        let start = Instant::now();

        // 添加回车换行到GBK编码的数据末尾
        let mut data_with_crlf = gbk_bytes.clone();
        data_with_crlf.extend_from_slice(b"\r\n");

        // 直接发送数据（连接池自动管理连接）
        match client.send_and_receive(&data_with_crlf).await {
            Ok(response) => {
                // 尝试将响应从GBK转回UTF-8（如果响应也是GBK编码的话）
                let response_text = match GBK.decode(&response, DecoderTrap::Replace) {
                    Ok(text) => text,
                    Err(_) => String::from_utf8_lossy(&response).to_string(),
                };

                let latency = start.elapsed().as_millis();

                info!("Socket通信成功，耗时: {}ms", latency);
                info!("服务器响应: {}", response_text);

                // 返回成功响应
                track()
                    .victory()
                    .message("Socket通信成功")
                    .data(serde_json::json!({
                        "latency": format!("{}ms", latency),
                        "request": json_data,
                        "response": response_text,
                        "host": host,
                        "port": port,
                        "encoding": "GBK"
                    }))
                    .build()
            }
            Err(e) => {
                let latency = start.elapsed().as_millis();
                error!("Socket通信失败: {:?}", e);

                // 返回错误响应
                track()
                    .defeat()
                    .message(format!("Socket通信失败: {}", e))
                    .data(serde_json::json!({
                        "latency": format!("{}ms", latency),
                        "host": host,
                        "port": port
                    }))
                    .build()
            }
        }
    }
}
