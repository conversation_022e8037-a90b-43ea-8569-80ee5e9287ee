---
description: 
globs: 
alwaysApply: true
---
# Rustavel 项目规范

## 项目信息

- **项目名称**: Rustavel
- **项目描述**: 基于 Rust + Axum 框架开发的高性能 Web 应用框架

## 编码规范

### 编码风格

- 使用4个空格缩进，不使用制表符
- 行长度最大限制为100个字符
- 函数和方法应有明确的文档注释
- 公共API必须有文档注释
- 使用 `rustfmt` 格式化代码
- 避免使用 `unwrap()` 和 `expect()`，除非确定不会panic

### 命名约定

- 模块名使用蛇形命名法(snake_case)
- 结构体、枚举和特征使用驼峰命名法(CamelCase)
- 函数、方法和变量使用蛇形命名法(snake_case)
- 常量使用全大写蛇形命名法(SCREAMING_SNAKE_CASE)
- 类型参数使用驼峰命名法，通常单个大写字母(如T, E)
- 宏使用蛇形命名法(snake_case)

### 项目结构

- 遵循项目既定的模块化结构
- 控制器放在 src/app/http/controllers/ 目录下
- 中间件放在 src/app/http/middleware/ 目录下
- 模型放在 src/app/models/ 目录下
- 路由定义放在 src/routes/ 目录下
- 配置文件放在 src/config/ 目录下
- 工具类放在 src/app/utils/ 目录下

### 错误处理

- 使用 `thiserror` 或 `anyhow` 进行错误处理
- 自定义错误类型应实现 `std::error::Error` 特征
- 公共函数应返回明确的错误类型，而不是使用 `Result<T, Box<dyn Error>>`
- 在适当的地方使用 `?` 运算符进行错误传播
- 记录错误详情，包括上下文信息

### 异步编程

- 使用 `async/await` 语法进行异步编程
- 避免在异步代码中使用阻塞操作
- 使用 `tokio::spawn` 创建新的异步任务
- 合理使用 `select!` 和 `join!` 宏处理并发
- 处理异步任务的取消和超时

### 数据库操作

- 使用连接池管理数据库连接
- SQL查询应使用参数化查询避免SQL注入
- 大型查询应分页处理
- 在适当的地方使用事务
- 避免在循环中执行数据库查询
- 数据库操作应有适当的错误处理和日志记录

### 日志规范

- 使用 `tracing` 进行日志记录
- 合理使用日志级别：error、warn、info、debug、trace
- 错误日志应包含足够的上下文信息
- 避免在正常流程中记录过多的日志
- 敏感信息不应记录到日志中
- 在关键操作点记录日志

### 安全规范

- 敏感配置使用环境变量或加密存储
- 用户输入必须经过验证和清洗
- API端点必须有适当的权限控制
- 避免在日志中记录敏感信息
- 使用安全的密码哈希算法
- 实现适当的速率限制机制

### 测试规范

- 为公共API编写单元测试
- 测试文件放在与源文件相同的模块中，使用 #[cfg(test)] 标记
- 使用 `cargo test` 运行测试
- 模拟外部依赖以进行隔离测试
- 测试应该是确定性的，不依赖于外部状态
- 测试函数名应清晰描述测试目的

### 文档规范

- 所有公共API都应有文档注释
- 文档注释应包含参数、返回值和示例
- 使用 `cargo doc` 生成文档
- 复杂的算法或逻辑应有详细的注释说明
- 保持文档与代码同步更新

### 性能优化

- 避免不必要的内存分配和复制
- 使用适当的数据结构
- 考虑使用并行处理大量数据
- 避免在热路径上进行昂贵的操作
- 合理使用缓存减少重复计算或IO操作
- 使用性能分析工具识别瓶颈

### 国际化

- 所有用户可见的文本应支持国际化
- 文本资源应存储在资源文件中，不应硬编码
- 日期、时间和数字格式应考虑本地化
- 支持UTF-8编码

### 依赖管理

- 明确指定依赖版本
- 定期更新依赖以修复安全漏洞
- 避免引入过多依赖
- 为不同功能模块分组依赖
- 使用特性标志(features)控制可选功能

## 编辑器设置

### 编辑器基本设置

```json
{
    "formatOnSave": true,
    "tabSize": 4,
    "insertSpaces": true,
    "wordWrap": "on",
    "rulers": [100],
    "cursorBlinking": "smooth",
    "cursorSmoothCaretAnimation": true,
    "fontFamily": "JetBrains Mono, Consolas, 'Courier New', monospace",
    "fontSize": 14
}
```

### 文件设置

```json
{
    "eol": "\n",
    "trimTrailingWhitespace": true,
    "insertFinalNewline": true,
    "exclude": {
        "**/target": true,
        "**/.git": true,
        "**/Cargo.lock": true
    },
    "associations": {
        "*.rs": "rust"
    }
}
```

### 搜索设置

```json
{
    "exclude": {
        "**/target": true,
        "**/.git": true
    }
}
```

### Rust Analyzer设置

```json
{
    "checkOnSave": {
        "command": "clippy",
        "extraArgs": ["--", "-W", "clippy::all"]
    },
    "cargo": {
        "allFeatures": true
    },
    "inlayHints": {
        "chainingHints": true,
        "parameterHints": true,
        "typeHints": true
    }
}
```

### AI助手设置

```json
{
    "language": "zh-CN",
    "codeStyle": "idiomatic_rust",
    "documentationLanguage": "zh-CN",
    "followProjectRules": true
}
```

## 最佳实践

- 在提交代码前，确保代码符合项目规则
- 使用 `cargo fmt` 格式化代码
- 使用 `cargo clippy` 检查代码质量
- 遵循项目的目录结构和命名约定
- 为公共 API 编写文档注释
- 编写单元测试验证功能

## 规则更新流程

如果您认为某些规则需要更新或添加新规则，请遵循以下流程：

1. 提出规则变更建议
2. 团队讨论并达成共识
3. 更新规则文件

4. 通知团队成员关于规则变更的情况