use crate::app::http::controllers::basic::LineController;
use crate::app::providers::DatabaseServiceProvider;
use axum::{routing::any, Router};
use std::sync::Arc;

/// 产线管理路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().merge(
        Router::new()
            .route("/line/list", any(LineController::index))
            .route("/line/page", any(LineController::page))
            .route("/line/increase", any(LineController::store))
            .route("/line/modify", any(LineController::update))
            .route("/line/remove", any(LineController::destroy)),
    )
}
