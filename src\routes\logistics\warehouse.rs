use crate::app::http::controllers::logistics::WarehouseController;
use crate::app::providers::DatabaseServiceProvider;
use axum::{
    routing::{get, post, put},
    Router,
};
use std::sync::Arc;

/// 仓库管理路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().merge(
        Router::new()
            .route("/warehouse/page", get(WarehouseController::page))
            .route("/warehouse/list", get(WarehouseController::index))
            .route("/warehouse/add", post(WarehouseController::save_add))
            .route("/warehouse/edit", put(WarehouseController::save_edit))
            .route("/warehouse/del", post(WarehouseController::delete))
            .route(
                "/warehouse/glntype",
                post(WarehouseController::get_gln_type),
            ),
    )
}
