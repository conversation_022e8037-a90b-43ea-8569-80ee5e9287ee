2025-07-24 16:06:04.939  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 16:06:04.940  INFO 数据库连接管理器创建成功
2025-07-24 16:06:04.991  INFO 数据库连接池创建成功
2025-07-24 16:06:05.003  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 16:06:08.133  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 16:06:08.134  INFO 数据库连接管理器创建成功
2025-07-24 16:06:08.171  INFO 数据库连接池创建成功
2025-07-24 16:06:08.176  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 16:19:22.236  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 16:19:22.237  INFO 数据库连接管理器创建成功
2025-07-24 16:19:22.267  INFO 数据库连接池创建成功
2025-07-24 16:19:22.271  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 16:19:25.242  INFO 开始测试Socket: SocketParams { host: None, port: None, message: None, print_type: None, print_count: None, use_default: None }
2025-07-24 16:19:25.242  INFO Socket测试参数: host=12*******, port=7000
2025-07-24 16:19:25.243  INFO 发送数据: {"Message":[{"Node:":"Barcode","Value":"111111"},{"Node:":"Product","Value":"测试产品"}],"PrintCount":"3","PrintType":"1"}
2025-07-24 16:19:25.243  INFO [Socket客户端] 开始连接到服务器: 12*******:7000
2025-07-24 16:19:25.244 DEBUG [Socket客户端] 连接成功: 12*******:7000
2025-07-24 16:19:25.244  INFO Socket连接成功
2025-07-24 16:19:25.250  INFO Socket通信成功，耗时: 6ms
2025-07-24 16:19:25.250  INFO 服务器响应: {
  "status": "ok",
  "message": ""
}

2025-07-24 16:19:25.250  INFO [Socket客户端] 关闭连接
2025-07-24 16:19:52.476  INFO 开始测试Socket: SocketParams { host: None, port: None, message: None, print_type: None, print_count: None, use_default: None }
2025-07-24 16:19:52.477  INFO Socket测试参数: host=12*******, port=7000
2025-07-24 16:19:52.477  INFO 发送数据: {"Message":[{"Node:":"Barcode","Value":"111111"},{"Node:":"Product","Value":"测试产品"}],"PrintCount":"3","PrintType":"1"}
2025-07-24 16:19:52.477  INFO [Socket客户端] 开始连接到服务器: 12*******:7000
2025-07-24 16:19:52.478 DEBUG [Socket客户端] 连接成功: 12*******:7000
2025-07-24 16:19:52.478  INFO Socket连接成功
2025-07-24 16:19:52.483  INFO Socket通信成功，耗时: 6ms
2025-07-24 16:19:52.484  INFO 服务器响应: {
  "status": "ok",
  "message": ""
}

2025-07-24 16:19:52.484  INFO [Socket客户端] 关闭连接
2025-07-24 16:22:23.305  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 16:22:23.305  INFO 数据库连接管理器创建成功
2025-07-24 16:22:23.335  INFO 数据库连接池创建成功
2025-07-24 16:22:23.339  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 16:27:44.300  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 16:27:44.301  INFO 数据库连接管理器创建成功
2025-07-24 16:27:44.337  INFO 数据库连接池创建成功
2025-07-24 16:27:44.345  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 16:28:54.795  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 16:28:54.796  INFO 数据库连接管理器创建成功
2025-07-24 16:28:54.830  INFO 数据库连接池创建成功
2025-07-24 16:28:54.836  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 16:29:18.410  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 16:29:18.411  INFO 数据库连接管理器创建成功
2025-07-24 16:29:18.476  INFO 数据库连接池创建成功
2025-07-24 16:29:18.489  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 16:29:44.596  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 16:29:44.597  INFO 数据库连接管理器创建成功
2025-07-24 16:29:44.629  INFO 数据库连接池创建成功
2025-07-24 16:29:44.634  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 16:30:55.235  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 16:30:55.236  INFO 数据库连接管理器创建成功
2025-07-24 16:30:55.268  INFO 数据库连接池创建成功
2025-07-24 16:30:55.273  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 16:31:30.704  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 16:31:30.705  INFO 数据库连接管理器创建成功
2025-07-24 16:31:30.736  INFO 数据库连接池创建成功
2025-07-24 16:31:30.741  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 16:31:35.244  INFO 开始测试Socket: SocketParams { host: None, port: None, message: None, print_type: None, print_count: None, use_default: None }
2025-07-24 16:31:35.244  INFO Socket测试参数: host=12*******, port=7000
2025-07-24 16:31:35.247  INFO 发送数据: {"Message":[{"Node:":"manufacturerBy","Value":"Zhejiang Huahai Pharmaceutical Technology Co, Ltd. Jiangnan , Linhai , Zhejiang 317000 , China"},{"Node:":"ProductName","Value":"Tablets, USP"},{"Node:":"ndc","Value":"43547-276-11"},{"Node:":"exp","Value":"20240625"},{"Node:":"lot","Value":"D1802220A"},{"Node:":"Quantity","Value":"40"},{"Node:":"code24","Value":"003690970000128760"},{"Node:":"controlCode","Value":"2044-01"}],"PrintCount":"1","PrintType":"3"}
2025-07-24 16:31:35.247  INFO [Socket客户端] 开始连接到服务器: 12*******:7000
2025-07-24 16:31:35.248 DEBUG [Socket客户端] 连接成功: 12*******:7000
2025-07-24 16:31:35.248  INFO Socket连接成功
2025-07-24 16:31:35.254  INFO Socket通信成功，耗时: 6ms
2025-07-24 16:31:35.254  INFO 服务器响应: {
  "status": "ok",
  "message": ""
}

2025-07-24 16:31:35.254  INFO [Socket客户端] 关闭连接
2025-07-24 16:57:56.965  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 16:57:56.966  INFO 数据库连接管理器创建成功
2025-07-24 16:57:57.010  INFO 数据库连接池创建成功
2025-07-24 16:57:57.020  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 16:59:13.418  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 16:59:13.419  INFO 数据库连接管理器创建成功
2025-07-24 16:59:13.461  INFO 数据库连接池创建成功
2025-07-24 16:59:13.467  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 17:02:13.936  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 17:02:13.937  INFO 数据库连接管理器创建成功
2025-07-24 17:02:13.974  INFO 数据库连接池创建成功
2025-07-24 17:02:13.980  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 17:02:16.849  INFO 开始测试Socket: SocketParams { host: None, port: None, message: None, print_type: None, print_count: None, use_default: None }
2025-07-24 17:02:16.851  INFO Socket测试参数: host=12*******, port=7000
2025-07-24 17:02:16.852  INFO 发送数据: {"Message":[{"Node":"manufacturerBy","Value":"Zhejiang Huahai Pharmaceutical Technology Co, Ltd. Jiangnan , Linhai , Zhejiang 317000 , China"},{"Node":"ProductName","Value":"Tablets, USP"},{"Node":"ndc","Value":"43547-276-11"},{"Node":"exp","Value":"20240625"},{"Node":"lot","Value":"D1802220A"},{"Node":"Quantity","Value":"40"},{"Node":"code24","Value":"003690970000128760"},{"Node":"controlCode","Value":"2044-01"}],"PrintCount":"1","PrintType":"3"}
2025-07-24 17:02:16.853  INFO [Socket客户端] 开始连接到服务器: 12*******:7000
2025-07-24 17:02:16.857 DEBUG [Socket客户端] 连接成功: 12*******:7000
2025-07-24 17:02:16.857  INFO Socket连接成功
2025-07-24 17:02:16.918  INFO Socket通信成功，耗时: 64ms
2025-07-24 17:02:16.918  INFO 服务器响应: {
  "status": "ok",
  "message": ""
}

2025-07-24 17:02:16.918  INFO [Socket客户端] 关闭连接
2025-07-24 17:21:26.355  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 17:21:26.356  INFO 数据库连接管理器创建成功
2025-07-24 17:21:26.392  INFO 数据库连接池创建成功
2025-07-24 17:21:26.399  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 17:22:13.562  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 17:22:13.563  INFO 数据库连接管理器创建成功
2025-07-24 17:22:13.603  INFO 数据库连接池创建成功
2025-07-24 17:22:13.610  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 17:23:30.411  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 17:23:30.412  INFO 数据库连接管理器创建成功
2025-07-24 17:23:30.449  INFO 数据库连接池创建成功
2025-07-24 17:23:30.453  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 17:24:34.128  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 17:24:34.129  INFO 数据库连接管理器创建成功
2025-07-24 17:24:34.163  INFO 数据库连接池创建成功
2025-07-24 17:24:34.169  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 17:25:14.608  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 17:25:14.609  INFO 数据库连接管理器创建成功
2025-07-24 17:25:14.642  INFO 数据库连接池创建成功
2025-07-24 17:25:14.648  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 17:47:40.658  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 17:47:40.660  INFO 数据库连接管理器创建成功
2025-07-24 17:47:40.694  INFO 数据库连接池创建成功
2025-07-24 17:47:40.700  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 17:50:14.242  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 17:50:14.243  INFO 数据库连接管理器创建成功
2025-07-24 17:50:14.277  INFO 数据库连接池创建成功
2025-07-24 17:50:14.282  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 17:52:06.207  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 17:52:06.209  INFO 数据库连接管理器创建成功
2025-07-24 17:52:06.242  INFO 数据库连接池创建成功
2025-07-24 17:52:06.248  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 17:52:32.522  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 17:52:32.523  INFO 数据库连接管理器创建成功
2025-07-24 17:52:32.561  INFO 数据库连接池创建成功
2025-07-24 17:52:32.566  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 17:53:30.337  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 17:53:30.338  INFO 数据库连接管理器创建成功
2025-07-24 17:53:30.374  INFO 数据库连接池创建成功
2025-07-24 17:53:30.381  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 17:54:11.836  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 17:54:11.837  INFO 数据库连接管理器创建成功
2025-07-24 17:54:11.871  INFO 数据库连接池创建成功
2025-07-24 17:54:11.877  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 17:56:23.325  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 17:56:23.326  INFO 数据库连接管理器创建成功
2025-07-24 17:56:23.362  INFO 数据库连接池创建成功
2025-07-24 17:56:23.367  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 18:05:59.715  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 18:05:59.715  INFO 数据库连接管理器创建成功
2025-07-24 18:05:59.747  INFO 数据库连接池创建成功
2025-07-24 18:05:59.753  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 18:06:36.314  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 18:06:36.315  INFO 数据库连接管理器创建成功
2025-07-24 18:06:36.352  INFO 数据库连接池创建成功
2025-07-24 18:06:36.358  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 18:07:28.794  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 18:07:28.795  INFO 数据库连接管理器创建成功
2025-07-24 18:07:28.835  INFO 数据库连接池创建成功
2025-07-24 18:07:28.840  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 18:23:28.938  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 18:23:28.939  INFO 数据库连接管理器创建成功
2025-07-24 18:23:28.972  INFO 数据库连接池创建成功
2025-07-24 18:23:28.978  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 18:24:56.010  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 18:24:56.012  INFO 数据库连接管理器创建成功
2025-07-24 18:24:56.056  INFO 数据库连接池创建成功
2025-07-24 18:24:56.064  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 18:25:38.229  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 18:25:38.230  INFO 数据库连接管理器创建成功
2025-07-24 18:25:38.263  INFO 数据库连接池创建成功
2025-07-24 18:25:38.268  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 18:26:41.443  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 18:26:41.444  INFO 数据库连接管理器创建成功
2025-07-24 18:26:41.477  INFO 数据库连接池创建成功
2025-07-24 18:26:41.482  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 18:28:47.977  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 18:28:47.978  INFO 数据库连接管理器创建成功
2025-07-24 18:28:48.016  INFO 数据库连接池创建成功
2025-07-24 18:28:48.023  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 18:29:13.450  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 18:29:13.451  INFO 数据库连接管理器创建成功
2025-07-24 18:29:13.489  INFO 数据库连接池创建成功
2025-07-24 18:29:13.496  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 18:30:11.714  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 18:30:11.715  INFO 数据库连接管理器创建成功
2025-07-24 18:30:11.752  INFO 数据库连接池创建成功
2025-07-24 18:30:11.757  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 18:31:09.114  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 18:31:09.115  INFO 数据库连接管理器创建成功
2025-07-24 18:31:09.150  INFO 数据库连接池创建成功
2025-07-24 18:31:09.155  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 18:33:31.934  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 18:33:31.936  INFO 数据库连接管理器创建成功
2025-07-24 18:33:31.980  INFO 数据库连接池创建成功
2025-07-24 18:33:31.987  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 18:34:32.298  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 18:34:32.299  INFO 数据库连接管理器创建成功
2025-07-24 18:34:32.336  INFO 数据库连接池创建成功
2025-07-24 18:34:32.341  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 18:37:06.574  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 18:37:06.575  INFO 数据库连接管理器创建成功
2025-07-24 18:37:06.606  INFO 数据库连接池创建成功
2025-07-24 18:37:06.611  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 21:59:29.230  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 21:59:29.231  INFO 数据库连接管理器创建成功
2025-07-24 22:00:09.943  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 22:00:09.944  INFO 数据库连接管理器创建成功
2025-07-24 22:00:09.981  INFO 数据库连接池创建成功
2025-07-24 22:00:09.988  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 22:00:59.865  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 22:00:59.866  INFO 数据库连接管理器创建成功
2025-07-24 22:00:59.898  INFO 数据库连接池创建成功
2025-07-24 22:00:59.902  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 22:01:05.271  INFO 开始测试Socket: SocketParams { host: None, port: None, message: None, print_type: None, print_count: None, use_default: None }
2025-07-24 22:01:05.271  INFO Socket测试参数: host=12*******, port=7000
2025-07-24 22:01:05.274  INFO 发送数据: {"Message":[{"Node":"distributor1","Value":"Solco Healthcare US, LLC"},{"Node":"ProductName","Value":"Tablets, USP"},{"Node":"PackageDose","Value":"43547-276-11"},{"Node":"PackageUnit","Value":"20240625"},{"Node":"VariableCount","Value":"100"},{"Node":"exp","Value":"20240625"},{"Node":"lot","Value":"D1802220A"},{"Node":"ndc","Value":"24979-727-06"},{"Node":"code","Value":"12345678901234567"},{"Node":"manufacturer2","Value":"2044-01"},{"Node":"manufacturerBy3","Value":"jiangnan, Linhai, Zhejiang 317000, China"},{"Node":"distributor1","Value":"Solco Healthcare US, LLC"},{"Node":"distributorBy4","Value":"Somerset, NJ 08873, USA"},{"Node":"remark","Value":"Store at 20-25°C, excursions permitted to 15-30°C[see USP Controlled Room Temp.]."},{"Node":"controlCode","Value":"2044-01"}],"PrintCount":"1","PrintType":"3"}
2025-07-24 22:01:05.275  INFO [Socket客户端] 开始连接到服务器: 12*******:7000
2025-07-24 22:01:05.278 DEBUG [Socket客户端] 连接成功: 12*******:7000
2025-07-24 22:01:05.278  INFO Socket连接成功
2025-07-24 22:01:05.323  INFO Socket通信成功，耗时: 47ms
2025-07-24 22:01:05.323  INFO 服务器响应: {
  "status": "ok",
  "message": ""
}

2025-07-24 22:01:05.327  INFO [Socket客户端] 关闭连接
2025-07-24 22:05:41.080  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 22:05:41.081  INFO 数据库连接管理器创建成功
2025-07-24 22:05:41.112  INFO 数据库连接池创建成功
2025-07-24 22:05:41.116  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 22:12:34.052  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 22:12:34.052  INFO 数据库连接管理器创建成功
2025-07-24 22:12:34.083  INFO 数据库连接池创建成功
2025-07-24 22:12:34.087  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 22:37:32.688  INFO [重打印] 开始重打印操作流程
2025-07-24 22:37:32.688  INFO [重打印] 接收到参数: code=010034354728010921100005819137
2025-07-24 22:37:32.728  INFO [重打印] 数据库连接获取成功
2025-07-24 22:37:32.729  INFO [重打印] 开始查询序列码: 010034354728010921100005819137
2025-07-24 22:37:32.747  INFO [重打印] SQL查询执行成功，开始解析结果
2025-07-24 22:37:32.748  INFO [重打印] 查询到序列码信息: level_code=1,zero_box=0, code_flag=2, batch_no=0000128887
2025-07-24 22:37:32.749  INFO [重打印] 序列码状态正常: code_flag=2
2025-07-24 22:37:32.749  INFO [重打印] 开始查询批次号: 0000128887
2025-07-24 22:37:32.754  INFO [重打印] 生产订单SQL查询执行成功，开始解析结果
2025-07-24 22:37:32.754  INFO [重打印] 找到生产订单信息: estate=4
2025-07-24 22:37:32.755  INFO [重打印] 订单状态正常: estate=4
2025-07-24 22:37:32.755  INFO [重打印] 处理完成，返回成功响应
2025-07-24 22:40:17.211  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 22:40:17.213  INFO 数据库连接管理器创建成功
2025-07-24 22:40:17.242  INFO 数据库连接池创建成功
2025-07-24 22:40:17.248  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 22:41:59.607  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 22:41:59.608  INFO 数据库连接管理器创建成功
2025-07-24 22:41:59.637  INFO 数据库连接池创建成功
2025-07-24 22:41:59.642  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 22:43:14.224  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 22:43:14.226  INFO 数据库连接管理器创建成功
2025-07-24 22:43:14.260  INFO 数据库连接池创建成功
2025-07-24 22:43:14.266  INFO 服务器运行在 0.0.0.0:3000
2025-07-24 22:43:16.777  INFO [重打印] 开始重打印操作流程
2025-07-24 22:43:16.778  INFO [重打印] 接收到参数: code=010034354728010921100005819137
2025-07-24 22:43:16.826  INFO [重打印] 数据库连接获取成功
2025-07-24 22:43:16.826  INFO [重打印] 开始查询序列码: 010034354728010921100005819137
2025-07-24 22:43:16.836  INFO [重打印] SQL查询执行成功，开始解析结果
2025-07-24 22:43:16.837  INFO [重打印] 查询到序列码信息: level_code=1,zero_box=0, code_flag=2, batch_no=0000128887
2025-07-24 22:43:16.837 ERROR [重打印] 层级不符合要求: 1
2025-07-24 22:51:56.339  INFO [重打印] 开始重打印操作流程
2025-07-24 22:51:56.340  INFO [重打印] 接收到参数: code=015034354728010421100000217617
2025-07-24 22:51:56.353  INFO [重打印] 数据库连接获取成功
2025-07-24 22:51:56.354  INFO [重打印] 开始查询序列码: 015034354728010421100000217617
2025-07-24 22:51:56.358  INFO [重打印] SQL查询执行成功，开始解析结果
2025-07-24 22:51:56.359  INFO [重打印] 查询到序列码信息: level_code=3,zero_box=0, code_flag=1, batch_no=0000128886
2025-07-24 22:51:56.359  INFO [重打印] 层级符合要求: level_code=3
2025-07-24 22:51:56.360 ERROR [重打印] 序列码状态异常: 1
2025-07-24 22:52:26.137  INFO [重打印] 开始重打印操作流程
2025-07-24 22:52:26.138  INFO [重打印] 接收到参数: code=015034354728010421100000216888
2025-07-24 22:52:26.150  INFO [重打印] 数据库连接获取成功
2025-07-24 22:52:26.150  INFO [重打印] 开始查询序列码: 015034354728010421100000216888
2025-07-24 22:52:26.154  INFO [重打印] SQL查询执行成功，开始解析结果
2025-07-24 22:52:26.154  INFO [重打印] 查询到序列码信息: level_code=3,zero_box=0, code_flag=2, batch_no=0000128886
2025-07-24 22:52:26.154  INFO [重打印] 层级符合要求: level_code=3
2025-07-24 22:52:26.155  INFO [重打印] 序列码状态正常: code_flag=2
2025-07-24 22:52:26.155  INFO [重打印] 开始查询批次号: 0000128886
2025-07-24 22:52:26.159  INFO [重打印] 生产订单SQL查询执行成功，开始解析结果
2025-07-24 22:52:26.160  INFO [重打印] 找到生产订单信息: estate=4
2025-07-24 22:52:26.160  INFO [重打印] 订单状态正常: estate=4
2025-07-24 22:52:26.160  INFO [重打印] 处理完成，返回成功响应
2025-07-24 23:59:46.497  INFO 数据库连接字符串: server=12*******;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-24 23:59:46.498  INFO 数据库连接管理器创建成功
2025-07-24 23:59:46.527  INFO 数据库连接池创建成功
2025-07-24 23:59:46.532  INFO 服务器运行在 0.0.0.0:3000
