use crate::framework::validation::traits::ValidationRule;
use regex::Regex;
use std::sync::OnceLock;

/// 中文规则
pub struct Chinese;

fn chinese_regex() -> &'static Regex {
    static CHINESE_REGEX: OnceLock<Regex> = OnceLock::new();
    CHINESE_REGEX.get_or_init(|| Regex::new(r"^[\u4e00-\u9fa5]+$").unwrap())
}

impl ValidationRule for Chinese {
    fn name(&self) -> &'static str {
        "chinese"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        chinese_regex().is_match(value)
    }

    fn message(&self) -> &str {
        "该字段必须是中文"
    }
}

/// 字母规则
pub struct Alpha;

fn alpha_regex() -> &'static Regex {
    static ALPHA_REGEX: OnceLock<Regex> = OnceLock::new();
    ALPHA_REGEX.get_or_init(|| Regex::new(r"^[a-zA-Z]+$").unwrap())
}

impl ValidationRule for Alpha {
    fn name(&self) -> &'static str {
        "alpha"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        alpha_regex().is_match(value)
    }

    fn message(&self) -> &str {
        "该字段必须是字母"
    }
}

/// 字母数字规则
pub struct AlphaNumeric;

fn alpha_numeric_regex() -> &'static Regex {
    static ALPHA_NUMERIC_REGEX: OnceLock<Regex> = OnceLock::new();
    ALPHA_NUMERIC_REGEX.get_or_init(|| Regex::new(r"^[a-zA-Z0-9]+$").unwrap())
}

impl ValidationRule for AlphaNumeric {
    fn name(&self) -> &'static str {
        "alpha_numeric"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        alpha_numeric_regex().is_match(value)
    }

    fn message(&self) -> &str {
        "该字段必须是字母或数字"
    }
}

/// 字母数字下划线规则
pub struct AlphaNumericDash;

fn alpha_numeric_dash_regex() -> &'static Regex {
    static ALPHA_NUMERIC_DASH_REGEX: OnceLock<Regex> = OnceLock::new();
    ALPHA_NUMERIC_DASH_REGEX.get_or_init(|| Regex::new(r"^[a-zA-Z0-9_\-]+$").unwrap())
}

impl ValidationRule for AlphaNumericDash {
    fn name(&self) -> &'static str {
        "alpha_dash"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        alpha_numeric_dash_regex().is_match(value)
    }

    fn message(&self) -> &str {
        "该字段必须是字母、数字、下划线和连字符"
    }
}
