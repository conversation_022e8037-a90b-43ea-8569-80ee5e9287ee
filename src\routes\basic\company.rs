use crate::app::http::controllers::basic::CompanyController;
use crate::app::providers::DatabaseServiceProvider;
use axum::{routing::any, Router};
use std::sync::Arc;

/// 公司管理路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().merge(
        Router::new()
            .route("/company/list", any(CompanyController::index))
            .route("/company/page", any(CompanyController::page))
            .route("/company/show", any(CompanyController::show))
            .route("/company/increase", any(CompanyController::store))
            .route("/company/modify", any(CompanyController::update))
            .route("/company/remove", any(CompanyController::destroy)),
    )
}
