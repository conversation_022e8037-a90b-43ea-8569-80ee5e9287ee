2025-07-26 00:04:06.903  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 00:04:06.904  INFO 数据库连接管理器创建成功
2025-07-26 00:04:06.938  INFO 数据库连接池创建成功
2025-07-26 00:04:06.945  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 00:04:09.645  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 00:04:09.646  INFO 数据库连接管理器创建成功
2025-07-26 00:04:09.688  INFO 数据库连接池创建成功
2025-07-26 00:04:09.694  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 00:20:03.978  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:20:04.024  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:20:04.038 ERROR 对象名 'produce_batch' 无效。 code=208
2025-07-26 00:20:04.038 ERROR [数据同步] 执行批次查询失败: Token error: '对象名 'produce_batch' 无效。' on server DESKTOP-SP6261T executing  on line 1 (code: 208, state: 1, class: 16)
2025-07-26 00:22:53.862  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 00:22:53.863  INFO 数据库连接管理器创建成功
2025-07-26 00:22:53.896  INFO 数据库连接池创建成功
2025-07-26 00:22:53.903  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 00:23:07.655  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:23:07.694  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:23:07.709  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:23:18.733  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:23:18.741  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:23:18.743  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:26:37.286  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:26:37.299  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:26:37.308  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:26:48.038  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:26:48.051  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:26:48.056  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:27:35.008  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:27:35.021  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:27:35.026  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:27:49.649  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:27:49.663  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:27:49.713  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:28:04.292  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:28:04.305  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:28:04.306  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:28:15.518  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:28:15.527  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:28:15.529  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:49:49.544  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:49:49.578  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:49:49.594  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:50:55.327  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:50:55.372  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:50:55.477  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:51:04.901  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:51:04.910  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:51:04.912  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:51:18.987  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:51:19.001  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:51:19.004  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:51:36.506  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:51:36.529  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:51:36.558  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:52:17.416  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:52:17.479  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:52:17.526  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:52:26.960  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:52:26.972  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:52:26.974  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:52:44.192  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:52:44.203  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:52:44.209  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:53:05.164  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:53:05.176  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:53:05.182  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:53:19.799  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:53:19.809  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:53:19.811  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:56:34.586  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:56:34.595  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:56:34.601  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:56:48.514  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:56:48.528  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:56:48.531  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:57:08.551  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:57:08.561  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:57:08.567  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:57:25.176  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:57:25.185  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:57:25.192  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:57:40.820  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:57:40.829  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:57:40.833  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:58:36.774  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:58:36.784  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:58:36.789  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:58:49.142  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:58:49.153  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:58:49.154  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:59:02.268  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:59:02.277  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:59:02.279  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 00:59:06.828  INFO [数据同步] 开始获取各表数据条数
2025-07-26 00:59:06.839  INFO [数据同步] 数据库连接获取成功
2025-07-26 00:59:06.845  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 01:00:19.331  INFO [数据同步] 开始获取各表数据条数
2025-07-26 01:00:19.343  INFO [数据同步] 数据库连接获取成功
2025-07-26 01:00:19.350  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 01:01:38.803  INFO [数据同步] 开始获取各表数据条数
2025-07-26 01:01:38.813  INFO [数据同步] 数据库连接获取成功
2025-07-26 01:01:38.819  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 01:01:58.718  INFO [数据同步] 开始获取各表数据条数
2025-07-26 01:01:58.727  INFO [数据同步] 数据库连接获取成功
2025-07-26 01:01:58.733  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 01:02:02.865  INFO [数据同步] 开始获取各表数据条数
2025-07-26 01:02:02.876  INFO [数据同步] 数据库连接获取成功
2025-07-26 01:02:02.878  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 01:02:26.750  INFO [数据同步] 开始获取各表数据条数
2025-07-26 01:02:26.760  INFO [数据同步] 数据库连接获取成功
2025-07-26 01:02:26.765  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 01:02:37.663  INFO [数据同步] 开始获取各表数据条数
2025-07-26 01:02:37.676  INFO [数据同步] 数据库连接获取成功
2025-07-26 01:02:37.678  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 01:02:56.516  INFO [数据同步] 开始获取各表数据条数
2025-07-26 01:02:56.524  INFO [数据同步] 数据库连接获取成功
2025-07-26 01:02:56.531  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:02:02.829  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:02:02.846  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:02:02.867  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:27:50.614  INFO [网络检测] 网络连接完成，耗时: 18ms
2025-07-26 09:33:30.792  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:33:30.808  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:33:30.825  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:33:46.660  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:33:46.671  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:33:46.673  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:33:51.306  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:33:51.315  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:33:51.318  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:34:14.886  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:34:14.894  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:34:14.896  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:34:42.592  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:34:42.603  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:34:42.621  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:34:46.816  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:34:46.829  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:34:46.831  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:36:34.605  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:36:34.618  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:36:34.633  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:36:49.750  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:36:49.761  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:36:49.781  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:36:54.829  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:36:54.839  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:36:54.841  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:37:09.785  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:37:09.794  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:37:09.796  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:37:27.768  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:37:27.778  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:37:27.780  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:37:31.445  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:37:31.457  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:37:31.459  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:37:50.531  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:37:50.545  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:37:50.573  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:37:55.415  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:37:55.429  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:37:55.446  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:38:25.576  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:38:25.587  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:38:25.589  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:38:43.906  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:38:43.916  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:38:43.918  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:38:47.514  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:38:47.524  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:38:47.526  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:40:49.657  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:40:49.668  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:40:49.688  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:41:06.509  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:41:06.518  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:41:06.520  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:41:11.503  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:41:11.515  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:41:11.533  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:41:40.548  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:41:40.560  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:41:40.567  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:41:45.559  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:41:45.592  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:41:45.594  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:43:34.432  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:43:34.446  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:43:34.464  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:43:52.545  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:43:52.555  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:43:52.557  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:44:07.642  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:44:07.653  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:44:07.655  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:44:35.960  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:44:35.972  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:44:35.991  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:46:11.551  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:46:11.585  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:46:11.603  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:46:40.279  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:46:40.290  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:46:40.307  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:49:03.023  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:49:03.037  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:49:03.060  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:49:10.910  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:49:10.923  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:49:10.925  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:50:42.074  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:50:42.090  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:50:42.123  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:51:02.413  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:51:02.447  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:51:02.463  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:52:28.223  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:52:28.244  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:52:28.293  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:52:34.707  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:52:34.722  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:52:34.724  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:52:57.223  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:52:57.233  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:52:57.235  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:53:32.898  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:53:32.910  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:53:32.927  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:53:48.222  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:53:48.247  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:53:48.249  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:53:53.782  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:53:53.791  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:53:53.793  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:54:12.316  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:54:12.328  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:54:12.330  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:54:15.781  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:54:15.853  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:54:15.856  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:54:36.661  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:54:36.693  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:54:36.740  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:54:42.593  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:54:42.605  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:54:42.607  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 09:57:43.545  INFO [数据同步] 开始获取各表数据条数
2025-07-26 09:57:43.559  INFO [数据同步] 数据库连接获取成功
2025-07-26 09:57:43.578  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 10:27:18.831  INFO [数据同步] 开始获取各表数据条数
2025-07-26 10:27:18.848  INFO [数据同步] 数据库连接获取成功
2025-07-26 10:27:18.867  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 10:33:12.271  INFO [数据同步] 开始获取各表数据条数
2025-07-26 10:33:12.283  INFO [数据同步] 数据库连接获取成功
2025-07-26 10:33:12.302  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 10:33:29.038  INFO [数据同步] 开始获取各表数据条数
2025-07-26 10:33:29.046  INFO [数据同步] 数据库连接获取成功
2025-07-26 10:33:29.050  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 10:34:47.291  INFO [数据同步] 开始获取各表数据条数
2025-07-26 10:34:47.310  INFO [数据同步] 数据库连接获取成功
2025-07-26 10:34:47.339  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 10:35:06.216  INFO [数据同步] 开始获取各表数据条数
2025-07-26 10:35:06.228  INFO [数据同步] 数据库连接获取成功
2025-07-26 10:35:06.246  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 10:35:26.722  INFO [数据同步] 开始获取各表数据条数
2025-07-26 10:35:26.731  INFO [数据同步] 数据库连接获取成功
2025-07-26 10:35:26.734  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 10:35:44.692  INFO [数据同步] 开始获取各表数据条数
2025-07-26 10:35:44.703  INFO [数据同步] 数据库连接获取成功
2025-07-26 10:35:44.706  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 10:36:38.883  INFO [数据同步] 开始获取各表数据条数
2025-07-26 10:36:38.915  INFO [数据同步] 数据库连接获取成功
2025-07-26 10:36:38.945  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 10:41:35.469  INFO [数据同步] 开始获取各表数据条数
2025-07-26 10:41:35.481  INFO [数据同步] 数据库连接获取成功
2025-07-26 10:41:35.503  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 10:42:01.235  INFO [数据同步] 开始获取各表数据条数
2025-07-26 10:42:01.248  INFO [数据同步] 数据库连接获取成功
2025-07-26 10:42:01.255  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 10:42:10.766  INFO [数据同步] 开始获取各表数据条数
2025-07-26 10:42:10.775  INFO [数据同步] 数据库连接获取成功
2025-07-26 10:42:10.777  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:35:58.013  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:35:58.048  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:35:58.069  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:36:15.088  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:36:15.097  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:36:15.099  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:36:31.247  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:36:31.262  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:36:31.280  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:36:46.091  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:36:46.102  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:36:46.104  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:38:29.746  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:38:29.756  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:38:29.775  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:40:17.367  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:40:17.381  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:40:17.403  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:40:40.592  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:40:40.614  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:40:40.616  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:42:37.656  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:42:37.668  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:42:37.686  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:43:42.920  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:43:42.934  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:43:42.951  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:48:40.614  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:48:40.629  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:48:40.664  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:51:28.995  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:51:29.010  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:51:29.026  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:51:55.463  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:51:55.476  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:51:55.497  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:52:03.416  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:52:03.428  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:52:03.431  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:52:29.325  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:52:29.335  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:52:29.337  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:57:47.946  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:57:47.957  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:57:47.974  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:58:15.646  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:58:15.659  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:58:15.675  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 11:58:24.727  INFO [数据同步] 开始获取各表数据条数
2025-07-26 11:58:24.738  INFO [数据同步] 数据库连接获取成功
2025-07-26 11:58:24.742  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 12:00:55.449  INFO [数据同步] 开始获取各表数据条数
2025-07-26 12:00:55.459  INFO [数据同步] 数据库连接获取成功
2025-07-26 12:00:55.473  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 13:37:27.825  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 13:37:27.826  INFO 数据库连接管理器创建成功
2025-07-26 13:37:27.861  INFO 数据库连接池创建成功
2025-07-26 13:37:27.866  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 13:55:10.763  INFO [数据同步] 开始获取各表数据条数
2025-07-26 13:55:10.802  INFO [数据同步] 数据库连接获取成功
2025-07-26 13:55:10.822  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 13:55:48.377  INFO [数据同步] 开始获取各表数据条数
2025-07-26 13:55:48.396  INFO [数据同步] 数据库连接获取成功
2025-07-26 13:55:48.404  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 13:58:29.310  INFO [数据同步] 开始获取各表数据条数
2025-07-26 13:58:29.323  INFO [数据同步] 数据库连接获取成功
2025-07-26 13:58:29.329  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 14:00:18.522  INFO [数据同步] 开始获取各表数据条数
2025-07-26 14:00:18.555  INFO [数据同步] 数据库连接获取成功
2025-07-26 14:00:18.577  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 19:29:58.077  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 19:29:58.082  INFO 数据库连接管理器创建成功
2025-07-26 19:29:58.259  INFO 数据库连接池创建成功
2025-07-26 19:29:58.278  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 19:30:00.700  INFO [网络检测] 网络连接完成，耗时: 75ms
2025-07-26 19:30:18.308  INFO [数据同步] 开始获取各表数据条数
2025-07-26 19:30:18.322  INFO [数据同步] 数据库连接获取成功
2025-07-26 19:30:18.338  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 19:33:01.773  INFO [数据同步] 开始获取各表数据条数
2025-07-26 19:33:01.785  INFO [数据同步] 数据库连接获取成功
2025-07-26 19:33:01.803  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 19:33:16.480  INFO [数据同步] 开始获取各表数据条数
2025-07-26 19:33:16.490  INFO [数据同步] 数据库连接获取成功
2025-07-26 19:33:16.499  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 19:33:29.281  INFO [数据同步] 开始获取各表数据条数
2025-07-26 19:33:29.293  INFO [数据同步] 数据库连接获取成功
2025-07-26 19:33:29.298  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 19:33:44.370  INFO [数据同步] 开始获取各表数据条数
2025-07-26 19:33:44.382  INFO [数据同步] 数据库连接获取成功
2025-07-26 19:33:44.388  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 19:34:54.329  INFO [数据同步] 开始获取各表数据条数
2025-07-26 19:34:54.339  INFO [数据同步] 数据库连接获取成功
2025-07-26 19:34:54.345  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 19:35:40.129  INFO [数据同步] 开始获取各表数据条数
2025-07-26 19:35:40.147  INFO [数据同步] 数据库连接获取成功
2025-07-26 19:35:40.149  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 19:36:46.947  INFO [数据同步] 开始获取各表数据条数
2025-07-26 19:36:46.958  INFO [数据同步] 数据库连接获取成功
2025-07-26 19:36:46.963  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 19:37:35.403  INFO [数据同步] 开始获取各表数据条数
2025-07-26 19:37:35.414  INFO [数据同步] 数据库连接获取成功
2025-07-26 19:37:35.420  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 19:38:43.637  INFO [数据同步] 开始获取各表数据条数
2025-07-26 19:38:43.652  INFO [数据同步] 数据库连接获取成功
2025-07-26 19:38:43.657  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 19:39:00.328  INFO [数据同步] 开始获取各表数据条数
2025-07-26 19:39:00.339  INFO [数据同步] 数据库连接获取成功
2025-07-26 19:39:00.341  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-26 20:06:50.506  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 20:06:50.509  INFO 数据库连接管理器创建成功
2025-07-26 20:06:50.545  INFO 数据库连接池创建成功
2025-07-26 20:06:50.550  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 20:08:03.803  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 20:08:03.804  INFO 数据库连接管理器创建成功
2025-07-26 20:08:03.836  INFO 数据库连接池创建成功
2025-07-26 20:08:03.841  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 20:09:39.414  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 20:09:39.415  INFO 数据库连接管理器创建成功
2025-07-26 20:09:39.444  INFO 数据库连接池创建成功
2025-07-26 20:09:39.448  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 20:10:16.051  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 20:10:16.052  INFO 数据库连接管理器创建成功
2025-07-26 20:10:16.082  INFO 数据库连接池创建成功
2025-07-26 20:10:16.087  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 20:12:17.321  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 20:12:17.322  INFO 数据库连接管理器创建成功
2025-07-26 20:12:17.373  INFO 数据库连接池创建成功
2025-07-26 20:12:17.378  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 20:13:05.197  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 20:13:05.198  INFO 数据库连接管理器创建成功
2025-07-26 20:13:05.228  INFO 数据库连接池创建成功
2025-07-26 20:13:05.233  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 20:15:20.961  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 20:15:20.962  INFO 数据库连接管理器创建成功
2025-07-26 20:15:20.995  INFO 数据库连接池创建成功
2025-07-26 20:15:21.001  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 20:15:29.984  INFO [网络检测] 网络连接完成，耗时: 52ms
2025-07-26 20:19:25.215  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), version=Some("14.75"), appid=Some("HBuilder")
2025-07-26 20:19:25.216  INFO [检查更新] 检查 android 平台 HBuilder 应用的更新
2025-07-26 20:19:25.217  INFO [检查更新] 未找到应用版本信息: HBuilder
2025-07-26 20:21:38.593  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), version=Some("14.75"), appid=Some("HBuilder")
2025-07-26 20:21:38.594  INFO [检查更新] 检查 android 平台 HBuilder 应用的更新
2025-07-26 20:21:38.595  INFO [检查更新] 未找到应用版本信息: HBuilder
2025-07-26 20:23:41.904  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), version=Some("14.75"), appid=Some("HBuilder")
2025-07-26 20:23:41.905  INFO [检查更新] 检查 android 平台 HBuilder 应用的更新
2025-07-26 20:23:41.907  INFO [检查更新] 未找到应用版本信息: HBuilder
2025-07-26 20:41:08.602  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), version=Some("14.75"), appid=Some("HBuilder")
2025-07-26 20:41:08.603  INFO [检查更新] 检查 android 平台 HBuilder 应用的更新
2025-07-26 20:41:08.608  INFO [检查更新] 未找到应用版本信息: HBuilder
2025-07-26 20:45:01.421  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), version=Some("14.75"), appid=Some("HBuilder")
2025-07-26 20:45:01.422  INFO [检查更新] 检查 android 平台 HBuilder 应用的更新
2025-07-26 20:45:01.422  INFO [检查更新] 未找到应用版本信息: HBuilder
2025-07-26 20:54:22.504  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 20:54:22.506  INFO 数据库连接管理器创建成功
2025-07-26 20:54:22.536  INFO 数据库连接池创建成功
2025-07-26 20:54:22.544  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 20:55:33.092  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("HBuilder")
2025-07-26 20:55:33.092  INFO [检查更新] 检查 android 平台 HBuilder 应用的更新
2025-07-26 20:55:33.094  INFO [检查更新] 未找到应用版本信息: HBuilder
2025-07-26 20:58:04.627  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 20:58:04.628  INFO 数据库连接管理器创建成功
2025-07-26 20:58:04.664  INFO 数据库连接池创建成功
2025-07-26 20:58:04.668  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 21:02:21.710  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 21:02:21.711  INFO 数据库连接管理器创建成功
2025-07-26 21:02:21.748  INFO 数据库连接池创建成功
2025-07-26 21:02:21.756  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 21:06:38.587  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 21:06:38.588  INFO 数据库连接管理器创建成功
2025-07-26 21:06:38.620  INFO 数据库连接池创建成功
2025-07-26 21:06:38.625  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 21:07:14.020  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 21:07:14.021  INFO 数据库连接管理器创建成功
2025-07-26 21:07:14.054  INFO 数据库连接池创建成功
2025-07-26 21:07:14.059  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 21:08:34.769  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 21:08:34.770  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 21:08:34.772  INFO [检查更新] 返回应用信息: Object {"description": String("1. 修复已知问题\n2. 优化用户体验\n3. 提升性能"), "downloadUrl": String("/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "forceUpdate": Bool(false), "upgrade": Bool(false)}
2025-07-26 21:09:50.059  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 21:09:50.060  INFO 数据库连接管理器创建成功
2025-07-26 21:09:50.101  INFO 数据库连接池创建成功
2025-07-26 21:09:50.107  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 21:10:18.937  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 21:10:18.938  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 21:10:18.941  INFO [检查更新] 返回应用信息: Object {"description": String("1. 修复已知问题\n2. 优化用户体验\n3. 提升性能"), "downloadUrl": String("/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-26 21:10:23.128  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 21:10:23.128  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 21:10:23.130  INFO [检查更新] 返回应用信息: Object {"description": String("1. 修复已知问题\n2. 优化用户体验\n3. 提升性能"), "downloadUrl": String("/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-26 21:11:25.454  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 21:11:25.456  INFO 数据库连接管理器创建成功
2025-07-26 21:11:25.485  INFO 数据库连接池创建成功
2025-07-26 21:11:25.491  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 21:29:17.076  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 21:29:17.077  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 21:29:17.078  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-26 21:30:50.935  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 21:30:50.937  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 21:30:50.938  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-26 21:30:53.555  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 21:30:53.556  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 21:30:53.557  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-26 21:32:58.867  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 21:32:58.867  INFO 数据库连接管理器创建成功
2025-07-26 21:32:58.896  INFO 数据库连接池创建成功
2025-07-26 21:32:58.900  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 21:34:07.080  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 21:34:07.081  INFO 数据库连接管理器创建成功
2025-07-26 21:34:07.109  INFO 数据库连接池创建成功
2025-07-26 21:34:07.114  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 21:35:07.346  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 21:35:07.347  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 21:35:07.350  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-26 21:35:43.177  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 21:35:43.178  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 21:35:43.179  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-26 21:43:38.614  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 21:43:38.615  INFO 数据库连接管理器创建成功
2025-07-26 21:43:38.649  INFO 数据库连接池创建成功
2025-07-26 21:43:38.655  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 21:44:40.993  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 21:44:40.994  INFO 数据库连接管理器创建成功
2025-07-26 21:44:41.043  INFO 数据库连接池创建成功
2025-07-26 21:44:41.050  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 21:45:03.986  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 21:45:03.987  INFO 数据库连接管理器创建成功
2025-07-26 21:45:04.018  INFO 数据库连接池创建成功
2025-07-26 21:45:04.023  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 21:46:24.174  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 21:46:24.175  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 21:46:24.176  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-26 21:47:22.466  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 21:47:22.467  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 21:47:22.467  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-26 21:49:03.219  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 21:49:03.220  INFO 数据库连接管理器创建成功
2025-07-26 21:49:03.253  INFO 数据库连接池创建成功
2025-07-26 21:49:03.258  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 21:49:36.992  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 21:49:36.993  INFO 数据库连接管理器创建成功
2025-07-26 21:49:37.027  INFO 数据库连接池创建成功
2025-07-26 21:49:37.034  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 21:49:41.969  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 21:49:41.970  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 21:49:41.971  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(true)}
2025-07-26 21:49:45.079  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 21:49:45.080  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 21:49:45.081  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(true)}
2025-07-26 22:00:52.151  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 22:00:52.152  INFO 数据库连接管理器创建成功
2025-07-26 22:00:52.188  INFO 数据库连接池创建成功
2025-07-26 22:00:52.193  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 22:56:21.431  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 22:56:21.433  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 22:56:21.437  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(true)}
2025-07-26 22:57:16.536  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 22:57:16.537  INFO 数据库连接管理器创建成功
2025-07-26 22:57:16.568  INFO 数据库连接池创建成功
2025-07-26 22:57:16.574  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 22:57:29.322  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 22:57:29.322  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 22:57:29.324  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/api/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(true)}
2025-07-26 22:57:30.691  INFO [检查更新] 接收到更新请求: action=Some("download"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 22:57:30.692  INFO [检查更新] 请求下载 android 平台 com.huahai.pda 应用
2025-07-26 22:57:30.697  INFO [检查更新] 创建目录: storage/apk
2025-07-26 22:57:30.698 ERROR [检查更新] APK文件不存在: storage/apk/huahai-pda-latest.apk
2025-07-26 22:59:02.457  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 22:59:02.458  INFO 数据库连接管理器创建成功
2025-07-26 22:59:02.496  INFO 数据库连接池创建成功
2025-07-26 22:59:02.501  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 23:09:38.678  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 23:09:38.679  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 23:09:38.682  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/api/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(true)}
2025-07-26 23:09:39.703  INFO [检查更新] 接收到更新请求: action=Some("download"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 23:09:39.704  INFO [检查更新] 请求下载 android 平台 com.huahai.pda 应用
2025-07-26 23:09:39.706  INFO [检查更新] 开始下载APK文件: src/storage/apk/huahai-pda-latest.apk
2025-07-26 23:09:39.707  INFO [检查更新] APK文件流创建成功，准备下载
2025-07-26 23:19:50.430  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 23:19:50.431  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 23:19:50.433  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/api/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(true)}
2025-07-26 23:26:56.706  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 23:26:56.706  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 23:26:56.708  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/api/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(true)}
2025-07-26 23:26:58.221  INFO [检查更新] 接收到更新请求: action=Some("download"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 23:26:58.224  INFO [检查更新] 请求下载 android 平台 com.huahai.pda 应用
2025-07-26 23:26:58.227  INFO [检查更新] 开始下载APK文件: src/storage/apk/huahai-pda-latest.apk
2025-07-26 23:26:58.228  INFO [检查更新] APK文件流创建成功，准备下载
2025-07-26 23:27:34.577  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-26 23:27:34.578  INFO 数据库连接管理器创建成功
2025-07-26 23:27:34.608  INFO 数据库连接池创建成功
2025-07-26 23:27:34.613  INFO 服务器运行在 0.0.0.0:3000
2025-07-26 23:27:40.437  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 23:27:40.438  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 23:27:40.440  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/api/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-26 23:27:53.126  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 23:27:53.127  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 23:27:53.129  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/api/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-26 23:33:46.494  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 23:33:46.494  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 23:33:46.496  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/api/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-26 23:33:49.289  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-26 23:33:49.289  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-26 23:33:49.291  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/api/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-27 08:22:07.745  INFO [数据同步] 开始获取各表数据条数
2025-07-27 08:22:07.785  INFO [数据同步] 数据库连接获取成功
2025-07-27 08:22:07.797  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-27 08:26:30.632  INFO [数据同步] 开始获取各表数据条数
2025-07-27 08:26:30.645  INFO [数据同步] 数据库连接获取成功
2025-07-27 08:26:30.649  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-27 08:26:50.246  INFO [数据同步] 开始获取各表数据条数
2025-07-27 08:26:50.258  INFO [数据同步] 数据库连接获取成功
2025-07-27 08:26:50.260  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-27 08:27:00.915  INFO [数据同步] 开始获取各表数据条数
2025-07-27 08:27:00.926  INFO [数据同步] 数据库连接获取成功
2025-07-27 08:27:00.928  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-27 08:27:05.837  INFO [数据同步] 开始获取各表数据条数
2025-07-27 08:27:05.848  INFO [数据同步] 数据库连接获取成功
2025-07-27 08:27:05.850  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-27 08:27:12.821  INFO [数据同步] 开始获取各表数据条数
2025-07-27 08:27:12.830  INFO [数据同步] 数据库连接获取成功
2025-07-27 08:27:12.832  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-27 08:28:01.412  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-27 08:28:01.412  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-27 08:28:01.413  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/api/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-27 08:30:53.495  INFO [单据分页] 开始获取单据分页列表
2025-07-27 08:30:53.496  INFO [单据分页] 查询参数: page=1, per_page=5, id=None, ware_code=Some(""), status=None, ware_date=None, channel=None
2025-07-27 08:30:53.507  INFO [单据分页] 数据库连接获取成功
2025-07-27 08:30:53.518  INFO [单据分页] 查询成功，获取到5条记录
2025-07-27 08:31:12.804  INFO [单据分页] 开始获取单据分页列表
2025-07-27 08:31:12.805  INFO [单据分页] 查询参数: page=1, per_page=5, id=None, ware_code=Some(""), status=None, ware_date=None, channel=None
2025-07-27 08:31:12.817  INFO [单据分页] 数据库连接获取成功
2025-07-27 08:31:12.820  INFO [单据分页] 查询成功，获取到5条记录
2025-07-27 08:31:23.951  INFO [单据分页] 开始获取单据分页列表
2025-07-27 08:31:23.952  INFO [单据分页] 查询参数: page=1, per_page=5, id=None, ware_code=Some(""), status=None, ware_date=None, channel=None
2025-07-27 08:31:23.961  INFO [单据分页] 数据库连接获取成功
2025-07-27 08:31:23.964  INFO [单据分页] 查询成功，获取到5条记录
2025-07-27 08:31:27.907  INFO [单据分页] 开始获取单据分页列表
2025-07-27 08:31:27.907  INFO [单据分页] 查询参数: page=1, per_page=5, id=None, ware_code=Some(""), status=None, ware_date=None, channel=None
2025-07-27 08:31:27.916  INFO [单据分页] 数据库连接获取成功
2025-07-27 08:31:27.919  INFO [单据分页] 查询成功，获取到5条记录
2025-07-27 08:31:31.681  INFO [单据分页] 开始获取单据分页列表
2025-07-27 08:31:31.682  INFO [单据分页] 查询参数: page=1, per_page=5, id=None, ware_code=Some(""), status=None, ware_date=None, channel=None
2025-07-27 08:31:31.706  INFO [单据分页] 数据库连接获取成功
2025-07-27 08:31:31.709  INFO [单据分页] 查询成功，获取到5条记录
2025-07-27 08:31:46.127  INFO [单据分页] 开始获取单据分页列表
2025-07-27 08:31:46.128  INFO [单据分页] 查询参数: page=1, per_page=5, id=None, ware_code=Some(""), status=None, ware_date=None, channel=None
2025-07-27 08:31:46.138  INFO [单据分页] 数据库连接获取成功
2025-07-27 08:31:46.141  INFO [单据分页] 查询成功，获取到5条记录
2025-07-27 08:32:02.536  INFO [单据分页] 开始获取单据分页列表
2025-07-27 08:32:02.537  INFO [单据分页] 查询参数: page=1, per_page=5, id=None, ware_code=Some(""), status=None, ware_date=None, channel=None
2025-07-27 08:32:02.551  INFO [单据分页] 数据库连接获取成功
2025-07-27 08:32:02.554  INFO [单据分页] 查询成功，获取到5条记录
2025-07-27 08:32:08.978  INFO [单据分页] 开始获取单据分页列表
2025-07-27 08:32:08.978  INFO [单据分页] 查询参数: page=1, per_page=5, id=None, ware_code=Some(""), status=None, ware_date=None, channel=None
2025-07-27 08:32:08.990  INFO [单据分页] 数据库连接获取成功
2025-07-27 08:32:08.994  INFO [单据分页] 查询成功，获取到5条记录
2025-07-27 08:32:23.297  INFO [产品分页] 开始获取产品分页列表
2025-07-27 08:32:23.297  INFO [产品分页] 查询参数: page=1, per_page=5, product_name=Some(""), product_code=None, ndc=None
2025-07-27 08:32:23.306  INFO [产品分页] 数据库连接获取成功
2025-07-27 08:32:23.318  INFO [产品分页] 查询成功，获取到5条记录
2025-07-27 08:32:35.656  INFO [产品分页] 开始获取产品分页列表
2025-07-27 08:32:35.657  INFO [产品分页] 查询参数: page=1, per_page=5, product_name=Some(""), product_code=None, ndc=None
2025-07-27 08:32:35.666  INFO [产品分页] 数据库连接获取成功
2025-07-27 08:32:35.670  INFO [产品分页] 查询成功，获取到5条记录
2025-07-27 08:32:38.724  INFO [产品分页] 开始获取产品分页列表
2025-07-27 08:32:38.724  INFO [产品分页] 查询参数: page=1, per_page=5, product_name=Some(""), product_code=None, ndc=None
2025-07-27 08:32:38.737  INFO [产品分页] 数据库连接获取成功
2025-07-27 08:32:38.741  INFO [产品分页] 查询成功，获取到5条记录
2025-07-27 08:32:55.540  INFO [订单分页] 开始获取订单分页列表
2025-07-27 08:32:55.541  INFO [订单分页] 查询参数: page=1, per_page=5, batch_no=Some(""), po_number=None, estate=None
2025-07-27 08:32:55.550  INFO [订单分页] 数据库连接获取成功
2025-07-27 08:32:55.560  INFO [订单分页] 查询成功，获取到5条记录
2025-07-27 08:32:55.564  INFO [订单分页] 成功加载关联的订单请求数据
2025-07-27 08:33:08.360  INFO [订单分页] 开始获取订单分页列表
2025-07-27 08:33:08.360  INFO [订单分页] 查询参数: page=1, per_page=5, batch_no=Some(""), po_number=None, estate=None
2025-07-27 08:33:08.370  INFO [订单分页] 数据库连接获取成功
2025-07-27 08:33:08.375  INFO [订单分页] 查询成功，获取到5条记录
2025-07-27 08:33:08.378  INFO [订单分页] 成功加载关联的订单请求数据
2025-07-27 08:33:16.604  INFO [订单分页] 开始获取订单分页列表
2025-07-27 08:33:16.605  INFO [订单分页] 查询参数: page=1, per_page=5, batch_no=Some(""), po_number=None, estate=None
2025-07-27 08:33:16.617  INFO [订单分页] 数据库连接获取成功
2025-07-27 08:33:16.621  INFO [订单分页] 查询成功，获取到5条记录
2025-07-27 08:33:16.623  INFO [订单分页] 成功加载关联的订单请求数据
2025-07-27 09:08:37.759  INFO [单据分页] 开始获取单据分页列表
2025-07-27 09:08:37.760  INFO [单据分页] 查询参数: page=1, per_page=10, id=None, ware_code=Some(""), status=Some(10), ware_date=None, channel=None
2025-07-27 09:08:37.790  INFO [单据分页] 数据库连接获取成功
2025-07-27 09:08:37.795  INFO [单据分页] 总记录数为0，直接返回空结果
2025-07-27 09:09:45.658  INFO [单据列表] 开始获取单据列表
2025-07-27 09:09:45.659  INFO [单据列表] 查询参数: id=None, ware_code=Some("250224"), status=Some(3), ware_date=Some("2025-02-24"), channel=None
2025-07-27 09:09:45.672  INFO [单据列表] 数据库连接获取成功
2025-07-27 09:09:45.680  INFO [单据列表] 查询成功，获取到1条记录
2025-07-27 09:09:45.733  INFO [单据列表] 返回成功，返回1条记录
2025-07-27 09:11:02.696  INFO [单据统计] 开始获取单据统计信息
2025-07-27 09:11:02.697  INFO [单据统计] 本周日期范围: ["2025-07-21", "2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-26", "2025-07-27"]
2025-07-27 09:11:02.707  INFO [单据统计] 数据库连接获取成功
2025-07-27 09:11:02.711  INFO [单据统计] 执行单据统计查询SQL
2025-07-27 09:11:02.714  INFO [单据统计] 统计查询执行成功
2025-07-27 09:11:02.715  INFO [单据统计] 成功获取单据统计信息
