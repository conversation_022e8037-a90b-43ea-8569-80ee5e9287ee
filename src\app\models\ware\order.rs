use crate::app::traits::DateTime;
use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use serde_json::{self, Value};
use std::collections::{HashMap, HashSet};
use std::sync::OnceLock;
use tiberius::Row;

/// 定义DateTime字段访问特性，提供类似反射的功能
trait DateTimeFields {
    /// 返回结构体中的所有DateTime字段信息
    /// 包含三元组: (字段名, 字段引用, 格式化字符串)
    fn casts(&self) -> Vec<(&'static str, &DateTime, &'static str)>;
}

/// 出库单据模型
///
/// # 主要功能
/// - 智能字段检测：根据查询自动识别和处理字段
/// - 部分字段查询：只返回实际查询的字段，实现"查询多少，返回多少"
/// - 动态字段存储：处理非标准字段，适应不同业务需求
/// - 高性能数据处理：优化大批量数据加载和处理
/// - 类型安全：提供类型安全的动态字段访问
///
/// # 用法示例
///
/// ```rust
/// // 查询和加载数据
/// let row = db.query("SELECT id, wareCode FROM ware_order WHERE id = 1").await?.into_row()?;
/// let order = Order::renovation(&row);
///
/// // 访问标准属性
/// println!("单据编码: {}", order.ware_code);
///
/// // 使用动态字段
/// if let Some(value) = order.get_dynamic::<String>("customField") {
///     println!("自定义字段: {}", value);
/// }
///
/// // 序列化为JSON响应（只包含查询的字段）
/// let json = order.to_response();
/// ```
#[derive(Debug, Serialize, Deserialize, Default)]
pub struct Order {
    /// 主键ID
    pub id: i32,
    /// 工厂ID
    #[serde(rename = "plantId")]
    pub plant_id: i32,
    /// 单据编码
    #[serde(rename = "wareCode")]
    pub ware_code: String,
    /// 仓库编码
    #[serde(rename = "wareHouseCode")]
    pub ware_house_code: String,
    /// 生产编码
    #[serde(rename = "produceCode")]
    pub produce_code: Option<String>,
    /// 客户编码
    #[serde(rename = "cusCode")]
    pub cus_code: String,
    /// 收货人编码
    #[serde(rename = "receiverCode")]
    pub receiver_code: String,
    /// 出库日期
    #[serde(rename = "wareDate")]
    pub ware_date: DateTime,
    /// 采购订单号
    #[serde(rename = "poNumber")]
    pub po_number: String,
    /// 备注
    pub remark: Option<String>,
    /// 批次列表
    #[serde(rename = "batchList")]
    pub batch_list: String,
    /// 完成标志
    #[serde(rename = "completeFlag")]
    pub complete_flag: i8,
    /// 状态
    pub estate: i8,
    /// 创建时间
    #[serde(rename = "createTime")]
    pub create_time: DateTime,
    /// 更新时间
    #[serde(rename = "updateTime")]
    pub update_time: DateTime,
    /// 文件名
    #[serde(rename = "fileName")]
    pub file_name: Option<String>,
    /// 接收邮箱
    #[serde(rename = "receiveMailbox")]
    pub receive_mailbox: Option<String>,
    /// 订单类型
    #[serde(rename = "orderType")]
    pub order_type: i8,
    /// 出库类型
    #[serde(rename = "outboundType")]
    pub outbound_type: String,
    /// 渠道
    pub channel: Option<i32>,
    /// 发票号
    #[serde(rename = "invoiceNo")]
    pub invoice_no: Option<String>,
    /// 动态字段存储，用于存储非标准字段
    #[serde(flatten, skip_serializing_if = "Option::is_none")]
    pub dynamic_fields: Option<HashMap<String, Value>>,
    /// 记录实际查询的字段，用于序列化时过滤
    #[serde(skip)]
    pub queried_fields: Option<HashSet<String>>,
}

impl DateTimeFields for Order {
    fn casts(&self) -> Vec<(&'static str, &DateTime, &'static str)> {
        vec![
            ("wareDate", &self.ware_date, "%Y-%m-%d"),
            ("createTime", &self.create_time, "%Y-%m-%d %H:%M:%S"),
            ("updateTime", &self.update_time, "%Y-%m-%d %H:%M:%S"),
        ]
    }
}

impl Order {
    /// 获取标准字段集合
    ///
    /// 返回系统预定义的所有标准字段名称集合，用于区分标准字段和动态字段。
    /// 该方法使用OnceLock实现懒加载单例模式，确保字段集合只初始化一次。
    ///
    /// # 返回值
    /// 返回包含所有标准字段名称的静态HashSet引用
    #[inline]
    pub fn standard_fields() -> &'static HashSet<&'static str> {
        static FIELDS: OnceLock<HashSet<&'static str>> = OnceLock::new();

        FIELDS.get_or_init(|| {
            [
                "id",
                "plantId",
                "wareCode",
                "wareHouseCode",
                "produceCode",
                "cusCode",
                "receiverCode",
                "wareDate",
                "poNumber",
                "remark",
                "batchList",
                "completeFlag",
                "estate",
                "createTime",
                "updateTime",
                "fileName",
                "receiveMailbox",
                "orderType",
                "outboundType",
                "channel",
                "invoiceNo",
                "RowNum",
            ]
            .into_iter()
            .collect()
        })
    }

    /// 从数据库行创建出库单据实例
    ///
    /// 支持指定要加载的字段和动态字段，只加载需要的数据以提高性能。
    /// 记录查询的字段，用于实现"查询多少，返回多少"的原则。
    ///
    /// # 参数
    /// * `row` - 数据库查询结果行
    /// * `fields` - 可选参数，指定要转换的字段集合
    /// * `dynamic_fields` - 可选参数，指定要提取的动态字段
    ///
    /// # 返回值
    /// 返回出库单据实例
    pub fn from_row(
        row: &Row,
        fields: Option<&HashSet<String>>,
        dynamic_fields: Option<&HashSet<String>>,
    ) -> Self {
        // 创建字段读取函数
        let should_read = |field: &str| -> bool { fields.map_or(true, |f| f.contains(field)) };

        // 构建出库单据实例
        let mut order = Self::build_from_row(row, &should_read);

        // 处理动态字段
        if let Some(dynamic_field_set) = dynamic_fields {
            if !dynamic_field_set.is_empty() {
                order.dynamic_fields = Self::extract_dynamic_fields(row, dynamic_field_set);
            }
        }

        // 记录查询的字段
        if let Some(field_set) = fields {
            order.queried_fields = Some(field_set.clone());
        }

        order
    }

    /// 判断出库单据是否为部分加载，用于确定是否需要补充数据
    #[inline]
    pub fn is_partial(&self) -> bool {
        self.ware_code.is_empty() || self.ware_house_code.is_empty()
    }

    /// 合并出库单据数据，用于补充部分加载的出库单据
    ///
    /// # 参数
    /// * `other` - 包含完整数据的出库单据实例
    ///
    /// # 返回值
    /// 返回是否进行了合并操作
    pub fn merge(&mut self, other: &Self) -> bool {
        // 如果当前不是部分加载，则无需合并
        if !self.is_partial() {
            return false;
        }

        let mut merged = false;

        // 合并所有必要字段
        Self::merge_optional_string_field(&mut self.produce_code, &other.produce_code, &mut merged);
        Self::merge_optional_string_field(&mut self.remark, &other.remark, &mut merged);
        Self::merge_optional_string_field(&mut self.file_name, &other.file_name, &mut merged);
        Self::merge_optional_string_field(
            &mut self.receive_mailbox,
            &other.receive_mailbox,
            &mut merged,
        );
        Self::merge_optional_string_field(&mut self.invoice_no, &other.invoice_no, &mut merged);

        // 合并必填字段
        if self.ware_code.is_empty() && !other.ware_code.is_empty() {
            self.ware_code = other.ware_code.clone();
            merged = true;
        }

        if self.ware_house_code.is_empty() && !other.ware_house_code.is_empty() {
            self.ware_house_code = other.ware_house_code.clone();
            merged = true;
        }

        if self.cus_code.is_empty() && !other.cus_code.is_empty() {
            self.cus_code = other.cus_code.clone();
            merged = true;
        }

        if self.receiver_code.is_empty() && !other.receiver_code.is_empty() {
            self.receiver_code = other.receiver_code.clone();
            merged = true;
        }

        if self.po_number.is_empty() && !other.po_number.is_empty() {
            self.po_number = other.po_number.clone();
            merged = true;
        }

        if self.batch_list.is_empty() && !other.batch_list.is_empty() {
            self.batch_list = other.batch_list.clone();
            merged = true;
        }

        if self.outbound_type.is_empty() && !other.outbound_type.is_empty() {
            self.outbound_type = other.outbound_type.clone();
            merged = true;
        }

        // 合并数值字段
        if self.plant_id == 0 && other.plant_id != 0 {
            self.plant_id = other.plant_id;
            merged = true;
        }

        if self.complete_flag == 0 && other.complete_flag != 0 {
            self.complete_flag = other.complete_flag;
            merged = true;
        }

        if self.estate == 0 && other.estate != 0 {
            self.estate = other.estate;
            merged = true;
        }

        if self.order_type == 0 && other.order_type != 0 {
            self.order_type = other.order_type;
            merged = true;
        }

        if self.channel.is_none() && other.channel.is_some() {
            self.channel = other.channel;
            merged = true;
        }

        // 合并日期时间字段
        if self.ware_date.0 == NaiveDateTime::default()
            && other.ware_date.0 != NaiveDateTime::default()
        {
            self.ware_date = other.ware_date.clone();
            merged = true;
        }

        if self.create_time.0 == NaiveDateTime::default()
            && other.create_time.0 != NaiveDateTime::default()
        {
            self.create_time = other.create_time.clone();
            merged = true;
        }

        if self.update_time.0 == NaiveDateTime::default()
            && other.update_time.0 != NaiveDateTime::default()
        {
            self.update_time = other.update_time.clone();
            merged = true;
        }

        // 合并动态字段
        if let Some(other_fields) = &other.dynamic_fields {
            if self.dynamic_fields.is_none() {
                self.dynamic_fields = Some(other_fields.clone());
                merged = true;
            } else if let Some(self_fields) = &mut self.dynamic_fields {
                for (key, value) in other_fields {
                    if !self_fields.contains_key(key) {
                        self_fields.insert(key.clone(), value.clone());
                        merged = true;
                    }
                }
            }
        }

        merged
    }

    /// 获取动态字段值
    ///
    /// 将动态字段值转换为指定类型并返回。
    #[inline]
    pub fn get_dynamic<T>(&self, field: &str) -> Option<T>
    where
        T: serde::de::DeserializeOwned,
    {
        self.dynamic_fields
            .as_ref()
            .and_then(|fields| fields.get(field))
            .and_then(|v| serde_json::from_value(v.clone()).ok())
    }

    /// 设置动态字段值
    #[inline]
    pub fn set_dynamic<T>(&mut self, field: &str, value: T) -> Result<(), serde_json::Error>
    where
        T: Serialize,
    {
        let json_value = serde_json::to_value(value)?;

        if self.dynamic_fields.is_none() {
            self.dynamic_fields = Some(HashMap::new());
        }

        if let Some(ref mut fields) = self.dynamic_fields {
            fields.insert(field.to_string(), json_value);
        }

        Ok(())
    }

    /// 智能转换数据库结果为出库单据实例
    ///
    /// 自动分析查询结果包含的字段，只返回查询的字段。
    /// 是数据加载的主要入口点，实现"查询多少，返回多少"的需求。
    ///
    /// # 类型参数
    /// * `T` - 实现了DataConversion特征的数据类型
    ///
    /// # 参数
    /// * `data` - 数据库查询结果行或行的集合
    ///
    /// # 返回值
    /// * 出库单据结构体或结构体集合
    #[inline]
    pub fn renovation<T>(data: &T) -> T::Output
    where
        T: DataConversion,
    {
        data.convert()
    }

    /// 将出库单据对象序列化为JSON响应
    ///
    /// 根据查询的字段自动过滤，只返回实际查询的字段。
    /// 实现"查询多少，返回多少"的原则，避免返回未查询的字段。
    ///
    /// # 返回值
    /// 返回只包含实际查询字段的JSON对象
    pub fn to_response(&self) -> serde_json::Value {
        // 获取原始序列化结果
        let json = serde_json::to_value(self).unwrap_or(serde_json::Value::Null);

        if let serde_json::Value::Object(mut obj) = json {
            // 使用DateTimeFields特性处理日期字段
            let casts = self.casts();
            for (field_name, datetime, format) in casts {
                if obj.contains_key(field_name) {
                    // 使用指定格式格式化日期
                    let formatted = datetime.format(format);
                    obj.insert(field_name.to_string(), serde_json::Value::String(formatted));
                }
            }

            let filtered: serde_json::Map<String, serde_json::Value> = match &self.queried_fields {
                // 如果有记录查询字段，只返回这些字段
                Some(queried) => obj
                    .into_iter()
                    .filter(|(key, _value)| {
                        // 转换字段名以匹配查询字段名
                        let field_name = if key.contains('_') {
                            // 转换蛇形命名为驼峰命名，匹配SQL查询字段名
                            let parts: Vec<&str> = key.split('_').collect();
                            let mut camel_case = parts[0].to_string();
                            for part in &parts[1..] {
                                camel_case.push_str(&part[0..1].to_uppercase());
                                camel_case.push_str(&part[1..]);
                            }
                            camel_case
                        } else {
                            key.clone()
                        };

                        // 只保留查询的字段，不论值是什么
                        queried.contains(&field_name)
                    })
                    .collect(),
                // 如果没有记录查询字段，过滤掉null和默认值
                None => obj
                    .into_iter()
                    .filter(|(_key, value)| {
                        // 排除null值
                        if value.is_null() {
                            return false;
                        }

                        // 排除空字符串
                        if let serde_json::Value::String(s) = value {
                            if s.is_empty() {
                                return false;
                            }
                        }

                        true
                    })
                    .collect(),
            };

            serde_json::Value::Object(filtered)
        } else {
            json
        }
    }

    // 私有辅助方法

    /// 从行数据构建出库单据实例
    ///
    /// 根据指定的字段筛选条件，从数据库行中提取数据并构建出库单据实例。
    /// 此方法会处理各种类型转换和数据格式问题，确保安全地读取数据。
    ///
    /// # 参数
    /// * `row` - 数据库查询结果行
    /// * `should_read` - 函数指针，判断是否应该读取特定字段
    ///
    /// # 返回值
    /// 返回构建的出库单据实例
    #[inline]
    fn build_from_row(row: &Row, should_read: &impl Fn(&str) -> bool) -> Self {
        // 创建临时实例，获取日期格式映射
        let date_formats: std::collections::HashMap<&str, &str> = Self::default()
            .casts()
            .into_iter()
            .map(|(field, _, format)| (field, format))
            .collect();

        // 定义读取函数
        let get_i32 = |field: &str, default: i32| -> i32 {
            if !should_read(field) {
                return default;
            }

            // 安全地获取整数值
            match row.try_get::<i32, _>(field) {
                Ok(Some(value)) => value,
                _ => {
                    // 尝试作为字符串获取并解析
                    match row.try_get::<&str, _>(field) {
                        Ok(Some(str_val)) => {
                            // 只有当字符串看起来像数字时才尝试解析为数字
                            if str_val.chars().all(|c| c.is_digit(10) || c == '-') {
                                str_val.parse::<i32>().unwrap_or(default)
                            } else {
                                // 如果字符串不像数字，直接返回默认值
                                default
                            }
                        }
                        _ => default,
                    }
                }
            }
        };

        let get_i8 = |field: &str, default: i8| -> i8 {
            if !should_read(field) {
                return default;
            }

            // 安全地获取 u8 值并转换为 i8
            match row.try_get::<u8, _>(field) {
                Ok(Some(u8_val)) => u8_val as i8,
                _ => {
                    // 尝试作为字符串获取并解析
                    match row.try_get::<&str, _>(field) {
                        Ok(Some(str_val)) => {
                            // 只有当字符串看起来像数字时才尝试解析为i8
                            if str_val.chars().all(|c| c.is_digit(10) || c == '-') {
                                str_val.parse::<i8>().unwrap_or(default)
                            } else {
                                // 如果字符串不像数字，直接返回默认值
                                default
                            }
                        }
                        _ => default,
                    }
                }
            }
        };

        let get_string = |field: &str| -> String {
            if !should_read(field) {
                return String::new();
            }

            // 安全地获取字符串值
            match row.try_get::<&str, _>(field) {
                Ok(Some(s)) => s.to_string(),
                _ => String::new(),
            }
        };

        let get_optional_string = |field: &str| -> Option<String> {
            if !should_read(field) {
                return None;
            }

            // 安全地获取可选字符串值
            match row.try_get::<&str, _>(field) {
                Ok(Some(s)) => Some(s.to_string()),
                _ => None,
            }
        };

        let get_optional_i32 = |field: &str| -> Option<i32> {
            if !should_read(field) {
                return None;
            }

            // 安全地获取 i32 类型值
            match row.try_get::<i32, _>(field) {
                Ok(Some(value)) => Some(value),
                _ => {
                    // 尝试作为字符串获取并解析
                    match row.try_get::<&str, _>(field) {
                        Ok(Some(str_val)) => {
                            // 只有当字符串看起来像数字时才尝试解析为i32
                            if str_val.chars().all(|c| c.is_digit(10) || c == '-') {
                                str_val.parse::<i32>().ok()
                            } else {
                                None
                            }
                        }
                        _ => None,
                    }
                }
            }
        };

        let get_datetime = |field: &str| -> DateTime {
            if !should_read(field) {
                return DateTime(NaiveDateTime::default());
            }

            // 安全地获取日期时间值
            match row.try_get::<&str, _>(field) {
                Ok(Some(s)) => {
                    // 使用casts中定义的格式，或默认格式
                    let format = date_formats.get(field).unwrap_or(&"%Y-%m-%d %H:%M:%S");

                    // 尝试多种格式解析日期时间
                    let formats = &[*format, "%Y-%m-%d %H:%M:%S", "%Y-%m-%d"];
                    for fmt in formats {
                        if let Some(dt) = DateTime::parse(s, *fmt) {
                            return dt;
                        }
                    }

                    // 如果所有格式都解析失败，返回默认值
                    DateTime(NaiveDateTime::default())
                }
                _ => DateTime(NaiveDateTime::default()),
            }
        };

        // 构建出库单据实例
        Self {
            id: get_i32("id", 0),
            plant_id: get_i32("plantId", 0),
            ware_code: get_string("wareCode"),
            ware_house_code: get_string("wareHouseCode"),
            produce_code: get_optional_string("produceCode"),
            cus_code: get_string("cusCode"),
            receiver_code: get_string("receiverCode"),
            ware_date: get_datetime("wareDate"),
            po_number: get_string("poNumber"),
            remark: get_optional_string("remark"),
            batch_list: get_string("batchList"),
            complete_flag: get_i8("completeFlag", 0),
            estate: get_i8("estate", 0),
            create_time: get_datetime("createTime"),
            update_time: get_datetime("updateTime"),
            file_name: get_optional_string("fileName"),
            receive_mailbox: get_optional_string("receiveMailbox"),
            order_type: get_i8("orderType", 0),
            outbound_type: get_string("outboundType"),
            channel: get_optional_i32("channel"),
            invoice_no: get_optional_string("invoiceNo"),
            dynamic_fields: None,
            queried_fields: None,
        }
    }

    /// 提取动态字段
    #[inline]
    fn extract_dynamic_fields(
        row: &Row,
        fields: &HashSet<String>,
    ) -> Option<HashMap<String, Value>> {
        let mut result = HashMap::new();
        let standard_fields = Self::standard_fields();

        for field in fields {
            // 只处理非标准字段
            if !standard_fields.contains(field.as_str()) {
                // 直接尝试作为字符串获取，这是最安全的方式
                match row.try_get::<&str, _>(field.as_str()) {
                    Ok(Some(str_val)) => {
                        result.insert(field.clone(), Value::String(str_val.to_string()));
                        continue;
                    }
                    _ => {}
                }

                // 如果不是字符串，尝试其他类型
                if let Some(val) = Self::extract_field_value(row, field) {
                    result.insert(field.clone(), val);
                }
            }
        }

        if result.is_empty() {
            None
        } else {
            Some(result)
        }
    }

    /// 提取字段值
    #[inline]
    fn extract_field_value(row: &Row, field: &str) -> Option<Value> {
        // 检查字段是否存在
        if !row.columns().iter().any(|c| c.name() == field) {
            return None;
        }

        // 尝试不同类型的提取
        match row.try_get::<i32, _>(field) {
            Ok(Some(val)) => return Some(Value::from(val)),
            _ => {}
        }

        match row.try_get::<u8, _>(field) {
            Ok(Some(val)) => return Some(Value::from(val)),
            _ => {}
        }

        match row.try_get::<&str, _>(field) {
            Ok(Some(val)) => {
                // 只有当字符串看起来像数字时才尝试解析为数字
                // 检查字符串是否只包含数字
                if val.chars().all(|c| c.is_digit(10) || c == '-') {
                    // 尝试将字符串解析为数字
                    if let Ok(num) = val.parse::<i32>() {
                        return Some(Value::from(num));
                    }
                }

                // 只有当字符串看起来像数字时才尝试解析为u8
                if val.chars().all(|c| c.is_digit(10)) {
                    if let Ok(num) = val.parse::<u8>() {
                        return Some(Value::from(num));
                    }
                }

                // 尝试解析为JSON
                if val.starts_with('{') || val.starts_with('[') {
                    if let Ok(json) = serde_json::from_str::<Value>(val) {
                        return Some(json);
                    }
                }

                // 尝试解析为日期时间
                if val.contains('-') && (val.contains(':') || val.contains(' ')) {
                    if let Some(dt) = DateTime::parse(val, "%Y-%m-%d %H:%M:%S") {
                        return Some(Value::String(dt.0.to_string()));
                    }
                }

                // 作为普通字符串处理
                return Some(Value::String(val.to_string()));
            }
            _ => {}
        }

        // 如果字段为 NULL，返回 null 值
        Some(Value::Null)
    }

    /// 合并可选字符串字段，避免重复代码
    #[inline]
    fn merge_optional_string_field(
        target: &mut Option<String>,
        source: &Option<String>,
        merged: &mut bool,
    ) {
        if target.is_none() && source.is_some() {
            *target = source.clone();
            *merged = true;
        }
    }
}

/// 数据转换特征
///
/// 为出库单据模型提供统一的数据转换接口，使单行查询和批量查询
/// 可以使用相同的处理逻辑，简化代码并提高一致性。
pub trait DataConversion {
    /// 转换后的输出类型
    type Output;

    /// 执行数据转换
    fn convert(&self) -> Self::Output;

    /// 获取当前行对象的引用
    fn row(&self) -> Option<&Row>;
}

/// 单行数据转换实现
///
/// 将单个数据库行转换为出库单据实例，只加载查询到的字段。
/// 记录查询的字段，用于实现"查询多少，返回多少"的原则。
impl DataConversion for Row {
    type Output = Order;

    fn convert(&self) -> Self::Output {
        // 获取行中所有可用的列名
        let available_columns: HashSet<_> = self
            .columns()
            .iter()
            .map(|c| c.name().to_string())
            .collect();

        // 只读取存在于查询结果中的字段
        let fields = Some(&available_columns);

        // 获取所有可能的动态字段
        let mut dynamic_fields = HashSet::new();
        let standard_fields = Order::standard_fields();

        for field in &available_columns {
            if !standard_fields.contains(field.as_str()) {
                dynamic_fields.insert(field.clone());
            }
        }

        // 使用from_row方法创建出库单据实例，只填充查询到的字段
        let mut order = Order::from_row(
            self,
            fields,
            if dynamic_fields.is_empty() {
                None
            } else {
                Some(&dynamic_fields)
            },
        );

        // 确保queried_fields被设置为可用列名
        order.queried_fields = Some(available_columns);

        order
    }

    fn row(&self) -> Option<&Row> {
        Some(self)
    }
}

/// 批量数据转换实现
///
/// 将多个数据库行转换为出库单据实例集合，用于批量处理查询结果。
impl DataConversion for [Row] {
    type Output = Vec<Order>;

    fn convert(&self) -> Self::Output {
        self.iter().map(|row| row.convert()).collect()
    }

    fn row(&self) -> Option<&Row> {
        None
    }
}
