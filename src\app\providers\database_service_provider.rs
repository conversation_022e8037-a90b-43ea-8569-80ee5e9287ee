/// 数据库服务提供者
///
/// 负责初始化和配置数据库连接池。
/// 使用 bb8 作为连接池管理器，tiberius 作为 SQL Server 驱动。
use bb8::Pool;
use bb8_tiberius::ConnectionManager;
use tiberius::{Client, Config};
use tokio::net::TcpStream;
use tokio_util::compat::{Compat, TokioAsyncWriteCompatExt};
use tracing::{error, info, warn};

use crate::config::DatabaseConfig;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use tokio::time;

/// 数据库连接池
#[derive(Clone)]
pub struct Database {
    /// 数据库配置
    config: DatabaseConfig,
}

impl Database {
    /// 创建数据库连接池实例
    pub fn new(config: &DatabaseConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// 获取数据库连接
    pub async fn get_connection(
        &self,
    ) -> Result<Client<Compat<TcpStream>>, tiberius::error::Error> {
        let config = Config::from_jdbc_string(&self.config.connection_string())?;
        let tcp = TcpStream::connect(format!("{}:{}", self.config.host, self.config.port)).await?;
        let client = Client::connect(config, tcp.compat_write()).await?;
        Ok(client)
    }
}

/// 数据库服务提供者结构体
///
/// 该结构体负责创建和配置数据库连接池。
/// 连接池配置包括：
/// - 最大连接数
/// - 最小空闲连接数
/// - 连接超时时间
#[derive(Clone)]
pub struct DatabaseServiceProvider {
    /// 数据库配置
    config: DatabaseConfig,
    pool: Arc<Mutex<Option<Database>>>,
}

impl DatabaseServiceProvider {
    /// 初始化数据库连接池
    ///
    /// # 返回
    ///
    /// 返回一个数据库连接池实例
    ///
    /// # 错误
    ///
    /// - 如果无法创建数据库连接池，将会 panic
    pub async fn init() -> Pool<ConnectionManager> {
        // 创建数据库配置
        let db_config = DatabaseConfig::from_env();

        // 创建连接字符串
        let conn_str = format!(
            "server={};port={};database={};user id={};password={};Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword",
            db_config.host, db_config.port, db_config.database, db_config.username, db_config.password
        );

        info!("数据库连接字符串: {}", conn_str);

        // 创建连接管理器
        let manager = match ConnectionManager::build(conn_str.as_str()) {
            Ok(manager) => {
                info!("数据库连接管理器创建成功");
                manager
            }
            Err(e) => {
                error!("创建数据库连接管理器失败: {}", e);
                error!("请检查以下内容:");
                error!("1. SQL Server 服务是否正在运行");
                error!("2. 用户名和密码是否正确");
                error!("3. SQL Server 是否允许 SQL Server 身份验证");
                error!("4. 防火墙是否允许连接");
                error!("5. 端口是否正确");
                panic!("无法创建数据库连接管理器: {}", e);
            }
        };

        // 创建连接池
        match Pool::builder()
            .max_size(db_config.pool_max_size)
            .min_idle(Some(db_config.pool_min_idle))
            .connection_timeout(std::time::Duration::from_secs(db_config.pool_timeout))
            .build(manager)
            .await
        {
            Ok(pool) => {
                info!("数据库连接池创建成功");
                pool
            }
            Err(e) => {
                error!("创建数据库连接池失败: {}", e);
                error!("请检查以下内容:");
                error!("1. 连接池参数是否合理");
                error!("2. 数据库服务器负载是否过高");
                error!("3. 网络连接是否稳定");
                panic!("无法创建数据库连接池: {}", e);
            }
        }
    }

    /// 创建数据库服务提供者实例
    pub fn new(config: &DatabaseConfig) -> Self {
        Self {
            config: config.clone(),
            pool: Arc::new(Mutex::new(None)),
        }
    }

    /// 获取数据库连接
    pub async fn get_connection(
        &self,
    ) -> Result<Client<Compat<TcpStream>>, tiberius::error::Error> {
        let config = Config::from_jdbc_string(&self.config.connection_string())?;
        let tcp = TcpStream::connect(format!("{}:{}", self.config.host, self.config.port)).await?;
        let client = Client::connect(config, tcp.compat_write()).await?;
        Ok(client)
    }

    pub async fn boot(&self) {
        let pool: Arc<Mutex<Option<Database>>> = Arc::clone(&self.pool);
        let config = self.config.clone();

        // 初始化连接池
        let database = Database::new(&config);
        *pool.lock().await = Some(database);

        // 预热连接池
        self.warmup_pool().await;

        // 启动监控
        self.start_monitoring().await;
    }

    async fn warmup_pool(&self) {
        let pool = self.pool.lock().await;
        if let Some(database) = &*pool {
            let warmup_count = self.config.pool_warmup;
            info!("开始预热数据库连接池，预热数量: {}", warmup_count);

            let mut handles = Vec::new();
            for _ in 0..warmup_count {
                let db = database.clone();
                handles.push(tokio::spawn(async move {
                    let start = Instant::now();
                    match db.get_connection().await {
                        Ok(_) => {
                            info!("连接池预热成功，耗时: {:?}", start.elapsed());
                        }
                        Err(e) => {
                            error!("连接池预热失败: {}", e);
                        }
                    }
                }));
            }

            for handle in handles {
                let _ = handle.await;
            }
        }
    }

    async fn start_monitoring(&self) {
        let pool: Arc<Mutex<Option<Database>>> = Arc::clone(&self.pool);
        let config = self.config.clone();
        let interval = Duration::from_secs(config.pool_monitor_interval);
        let timeout = Duration::from_secs(config.pool_health_check_timeout);

        tokio::spawn(async move {
            let mut interval_timer = time::interval(interval);
            loop {
                interval_timer.tick().await;
                let pool = pool.lock().await;
                if let Some(database) = &*pool {
                    let start = Instant::now();
                    match database.get_connection().await {
                        Ok(_) => {
                            let elapsed = start.elapsed();
                            if elapsed > timeout {
                                warn!("数据库连接池健康检查超时: {:?}", elapsed);
                            } else {
                                info!("数据库连接池健康检查正常，耗时: {:?}", elapsed);
                            }
                        }
                        Err(e) => {
                            error!("数据库连接池健康检查失败: {}", e);
                        }
                    }
                }
            }
        });
    }

    pub async fn get_pool(&self) -> Option<Database> {
        let pool = self.pool.lock().await;
        pool.clone()
    }
}
