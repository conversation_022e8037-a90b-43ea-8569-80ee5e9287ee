use crate::app::providers::DatabaseServiceProvider;
use crate::routes::basic::routes as basic_routes;
use crate::routes::docking::routes as docking_routes;
use crate::routes::login::routes as login_routes;
use crate::routes::logistics::routes as logistics_routes;
use crate::routes::nucleus::routes as nucleus_routes;
use crate::routes::produce::routes as produce_routes;
use crate::routes::test::routes as test_routes;
use axum::{routing::get, Router};
use std::sync::Arc;

/// API 路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new()
        .route("/", get(|| async { "API Home" }))
        .merge(basic_routes())
        .merge(docking_routes())
        .merge(login_routes())
        .merge(logistics_routes())
        .merge(nucleus_routes())
        .merge(produce_routes())
        .merge(test_routes())
}
