/// Socket服务提供者
///
/// 负责初始化和配置Socket连接池。
/// 使用 bb8 作为连接池管理器。
use bb8::Pool;

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::net::{TcpSocket, TcpStream};
use tokio::sync::Mutex;
use tokio::time;
use tracing::{error, info, warn};

use crate::app::services::socket::client_service::{SocketClientConfig, SocketClientError};
use crate::config::SocketConfig;

/// Socket连接管理器
pub struct SocketConnectionManager {
    /// Socket配置
    config: SocketClientConfig,
}

impl SocketConnectionManager {
    /// 创建Socket连接管理器
    pub fn new(config: SocketClientConfig) -> Self {
        Self { config }
    }
}

#[async_trait::async_trait]
impl bb8::ManageConnection for SocketConnectionManager {
    type Connection = TcpStream;
    type Error = SocketClientError;

    async fn connect(&self) -> Result<Self::Connection, Self::Error> {
        let addr = format!("{}:{}", self.config.host, self.config.port);
        let timeout = Duration::from_millis(self.config.read_timeout_ms.unwrap_or(5000));

        // 使用超时机制连接
        match time::timeout(timeout, Self::configure_stream(&addr, &self.config)).await {
            Ok(result) => match result {
                Ok(stream) => Ok(stream),
                Err(e) => Err(SocketClientError::PoolError(e.to_string())),
            },
            Err(_) => Err(SocketClientError::PoolError("连接超时".to_string())),
        }
    }

    async fn is_valid(&self, conn: &mut Self::Connection) -> Result<(), Self::Error> {
        // 发送心跳包或简单检查连接是否有效
        if conn.peer_addr().is_err() {
            return Err(SocketClientError::PoolError("连接已断开".to_string()));
        }
        Ok(())
    }

    fn has_broken(&self, conn: &mut Self::Connection) -> bool {
        conn.peer_addr().is_err()
    }
}

impl SocketConnectionManager {
    /// 配置TCP流 - 在连接前设置socket选项
    async fn configure_stream(
        addr: &str,
        config: &SocketClientConfig,
    ) -> Result<TcpStream, std::io::Error> {
        // 创建TCP socket
        let socket = TcpSocket::new_v4()?;

        // 设置读写缓冲区大小
        socket.set_recv_buffer_size(config.read_buffer_size as u32)?;
        socket.set_send_buffer_size(config.read_buffer_size as u32)?;

        // 连接到服务器
        let addr = addr
            .parse()
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::InvalidInput, e))?;
        let stream = socket.connect(addr).await?;

        // 连接池模式下无需特殊配置，由连接池管理

        Ok(stream)
    }
}

/// Socket连接
#[derive(Clone)]
pub struct SocketConnection {
    /// Socket配置
    config: SocketClientConfig,
}

impl SocketConnection {
    /// 创建Socket连接实例
    pub fn new(config: &SocketClientConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// 获取Socket连接
    pub async fn get_connection(&self) -> Result<TcpStream, SocketClientError> {
        let addr = format!("{}:{}", self.config.host, self.config.port);
        let timeout = Duration::from_millis(self.config.read_timeout_ms.unwrap_or(5000));

        // 使用超时机制连接
        match time::timeout(
            timeout,
            SocketConnectionManager::configure_stream(&addr, &self.config),
        )
        .await
        {
            Ok(result) => match result {
                Ok(stream) => Ok(stream),
                Err(e) => Err(SocketClientError::PoolError(e.to_string())),
            },
            Err(_) => Err(SocketClientError::PoolError("连接超时".to_string())),
        }
    }
}

/// Socket服务提供者结构体
///
/// 该结构体负责创建和配置Socket连接池。
/// 连接池配置包括：
/// - 最大连接数
/// - 最小空闲连接数
/// - 连接超时时间
#[derive(Clone)]
pub struct SocketServiceProvider {
    /// Socket配置
    config: SocketConfig,
    pool: Arc<Mutex<Option<SocketConnection>>>,
}

impl SocketServiceProvider {
    /// 初始化Socket连接池
    ///
    /// # 返回
    ///
    /// 返回一个Socket连接池实例
    ///
    /// # 错误
    ///
    /// - 如果无法创建Socket连接池，将会 panic
    pub async fn init() -> Pool<SocketConnectionManager> {
        // 创建Socket配置
        let socket_config = SocketConfig::from_env();

        // 创建客户端配置
        let client_config = SocketClientConfig {
            host: socket_config.host.clone(),
            port: socket_config.port,
            connect_timeout_ms: 5000, // 5秒连接超时
            read_timeout_ms: Some(socket_config.timeout * 1000), // 读取超时
            write_timeout_ms: Some(5000), // 5秒写入超时
            operation_timeout_ms: Some(socket_config.timeout * 1000 + 5000), // 总操作超时
            read_buffer_size: socket_config.read_buffer_size,
        };

        info!(
            "Socket连接字符串: {}:{}",
            socket_config.host, socket_config.port
        );

        // 创建连接管理器
        let manager = SocketConnectionManager::new(client_config);

        // 创建连接池
        match Pool::builder()
            .max_size(10) // 最大连接数
            .min_idle(Some(2)) // 最小空闲连接数
            .connection_timeout(std::time::Duration::from_secs(socket_config.timeout))
            .build(manager)
            .await
        {
            Ok(pool) => {
                info!("Socket连接池创建成功");
                pool
            }
            Err(e) => {
                error!("创建Socket连接池失败: {}", e);
                error!("请检查以下内容:");
                error!("1. 连接池参数是否合理");
                error!("2. Socket服务器是否可达");
                error!("3. 网络连接是否稳定");
                panic!("无法创建Socket连接池: {}", e);
            }
        }
    }

    /// 创建Socket服务提供者实例
    pub fn new(config: &SocketConfig) -> Self {
        Self {
            config: config.clone(),
            pool: Arc::new(Mutex::new(None)),
        }
    }

    /// 获取Socket连接
    pub async fn get_connection(&self) -> Result<TcpStream, SocketClientError> {
        let client_config = SocketClientConfig {
            host: self.config.host.clone(),
            port: self.config.port,
            connect_timeout_ms: 5000,
            read_timeout_ms: Some(self.config.timeout * 1000), // 秒转毫秒
            write_timeout_ms: Some(5000),
            operation_timeout_ms: Some(self.config.timeout * 1000 + 5000),
            read_buffer_size: self.config.read_buffer_size,
        };

        let socket_conn = SocketConnection::new(&client_config);
        socket_conn.get_connection().await
    }

    pub async fn boot(&self) {
        let pool: Arc<Mutex<Option<SocketConnection>>> = Arc::clone(&self.pool);
        let config = self.config.clone();

        // 创建客户端配置
        let client_config = SocketClientConfig {
            host: config.host.clone(),
            port: config.port,
            connect_timeout_ms: 5000,
            read_timeout_ms: Some(config.timeout * 1000), // 秒转毫秒
            write_timeout_ms: Some(5000),
            operation_timeout_ms: Some(config.timeout * 1000 + 5000),
            read_buffer_size: config.read_buffer_size,
        };

        // 初始化连接池
        let socket_conn = SocketConnection::new(&client_config);
        *pool.lock().await = Some(socket_conn);

        // 预热连接池
        self.warmup_pool().await;

        // 启动监控
        self.start_monitoring().await;
    }

    async fn warmup_pool(&self) {
        let pool = self.pool.lock().await;
        if let Some(socket_conn) = &*pool {
            let warmup_count = 5; // 预热连接数
            info!("开始预热Socket连接池，预热数量: {}", warmup_count);

            let mut handles = Vec::new();
            for _ in 0..warmup_count {
                let conn = socket_conn.clone();
                handles.push(tokio::spawn(async move {
                    let start = Instant::now();
                    match conn.get_connection().await {
                        Ok(_) => {
                            info!("Socket连接池预热成功，耗时: {:?}", start.elapsed());
                        }
                        Err(e) => {
                            error!("Socket连接池预热失败: {}", e);
                        }
                    }
                }));
            }

            for handle in handles {
                let _ = handle.await;
            }
        }
    }

    async fn start_monitoring(&self) {
        let pool: Arc<Mutex<Option<SocketConnection>>> = Arc::clone(&self.pool);
        let config = self.config.clone();
        let interval = Duration::from_secs(config.heartbeat_interval);
        let timeout = Duration::from_secs(config.timeout);

        tokio::spawn(async move {
            let mut interval_timer = time::interval(interval);
            loop {
                interval_timer.tick().await;
                let pool = pool.lock().await;
                if let Some(socket_conn) = &*pool {
                    let start = Instant::now();
                    match socket_conn.get_connection().await {
                        Ok(_) => {
                            let elapsed = start.elapsed();
                            if elapsed > timeout {
                                warn!("Socket连接池健康检查超时: {:?}", elapsed);
                            } else {
                                info!("Socket连接池健康检查正常，耗时: {:?}", elapsed);
                            }
                        }
                        Err(e) => {
                            error!("Socket连接池健康检查失败: {}", e);
                        }
                    }
                }
            }
        });
    }

    pub async fn get_pool(&self) -> Option<SocketConnection> {
        let pool = self.pool.lock().await;
        pool.clone()
    }
}
