use crate::{
    app::facades::get_facade_manager,
    app::facades::manager::init_facade_manager,
    app::providers::{
        DatabaseServiceProvider, LoggingServiceProvider, RouteServiceProvider,
        SocketServiceProvider,
    },
    config::database::DatabaseConfig,
};
use std::{net::SocketAddr, sync::Arc};
use tokio::{net::TcpListener, signal};

/// 应用程序
#[derive(Clone)]
pub struct Application {
    /// 应用配置
    pub app_config: crate::config::app::AppConfig,
    /// 日志配置
    pub logging_config: crate::config::logging::LoggingConfig,
}

impl Application {
    /// 创建新的应用实例
    pub fn init() -> Self {
        Self {
            app_config: crate::config::app::AppConfig::from_env(),
            logging_config: crate::config::logging::LoggingConfig::from_env(),
        }
    }

    /// 启动应用
    pub async fn boot(&self) {
        // 初始化日志
        LoggingServiceProvider::init(
            &self.logging_config.level,
            &self.logging_config.path,
            self.logging_config.max_size,
            self.logging_config.max_age,
            self.logging_config.max_backups,
            self.logging_config.compress,
        );

        // 初始化 Facade 管理器
        init_facade_manager();

        // 创建数据库连接池
        let db_config = DatabaseConfig::from_env();
        let db = Arc::new(DatabaseServiceProvider::new(&db_config));

        // 初始化数据库连接池
        let db_pool = DatabaseServiceProvider::init().await;

        // 初始化Socket连接池
        let socket_pool = SocketServiceProvider::init().await;

        // 注册连接池到 Facade 管理器
        {
            let mut facade_manager = get_facade_manager()
                .lock()
                .expect("无法获取 Facade 管理器锁");

            // 注册数据库连接池
            facade_manager.register(db_pool);

            // 注册Socket连接池
            facade_manager.register(socket_pool);

            // 注册应用实例
            facade_manager.register(self.clone());

            // 注册日志配置
            facade_manager.register(self.logging_config.clone());
        }

        // 创建路由
        let mut route_provider = RouteServiceProvider::new();
        let router = route_provider.boot().with_state(db);

        // 解析地址
        let addr = format!("{}:{}", self.app_config.host, self.app_config.port)
            .parse::<SocketAddr>()
            .expect("解析服务器地址失败");

        // 启动服务器
        tracing::info!("服务器运行在 {}", addr);
        let listener = TcpListener::bind(addr).await.unwrap();

        // 启动服务器并等待关闭信号
        axum::serve(listener, router)
            .with_graceful_shutdown(shutdown_signal())
            .await
            .unwrap();
    }
}

/// 等待关闭信号
async fn shutdown_signal() {
    signal::ctrl_c().await.expect("安装 Ctrl+C 处理器失败");
    tracing::info!("正在优雅地关闭服务器...");
}
