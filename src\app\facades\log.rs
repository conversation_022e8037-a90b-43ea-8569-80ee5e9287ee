use super::manager::{get_facade_manager, Facade};
use crate::config::logging::LoggingConfig;
use std::sync::Arc;
use tracing::{debug, error, info, trace, warn};

/// Log Facade
pub struct Log;

impl Log {
    /// 获取日志配置
    pub fn config() -> Arc<LoggingConfig> {
        Self::get_instance()
            .downcast::<LoggingConfig>()
            .expect("无法获取日志配置")
    }

    /// 获取日志通道
    pub fn channel() -> String {
        Self::config().channel.clone()
    }

    /// 获取日志级别
    pub fn level() -> String {
        Self::config().level.clone()
    }

    /// 获取日志路径
    pub fn path() -> String {
        Self::config().path.clone()
    }

    /// 获取日志文件最大大小
    pub fn max_size() -> u64 {
        Self::config().max_size
    }

    /// 获取日志保留天数
    pub fn max_age() -> u64 {
        Self::config().max_age
    }

    /// 获取日志备份数量
    pub fn max_backups() -> u64 {
        Self::config().max_backups
    }

    /// 获取日志是否压缩
    pub fn compress() -> bool {
        Self::config().compress
    }

    /// 记录紧急日志
    pub fn emergency(message: &str) {
        error!("[EMERGENCY] {}", message);
    }

    /// 记录警告日志
    pub fn alert(message: &str) {
        error!("[ALERT] {}", message);
    }

    /// 记录严重错误日志
    pub fn critical(message: &str) {
        error!("[CRITICAL] {}", message);
    }

    /// 记录错误日志
    pub fn error(message: &str) {
        error!("{}", message);
    }

    /// 记录警告日志
    pub fn warning(message: &str) {
        warn!("{}", message);
    }

    /// 记录通知日志
    pub fn notice(message: &str) {
        info!("[NOTICE] {}", message);
    }

    /// 记录信息日志
    pub fn info(message: &str) {
        info!("{}", message);
    }

    /// 记录调试日志
    pub fn debug(message: &str) {
        debug!("{}", message);
    }

    /// 记录追踪日志
    pub fn trace(message: &str) {
        trace!("{}", message);
    }
}

impl Facade for Log {
    fn get_instance() -> Arc<dyn std::any::Any + Send + Sync> {
        get_facade_manager()
            .lock()
            .expect("无法获取 Facade 管理器锁")
            .get::<LoggingConfig>()
            .expect("日志配置未注册")
    }
}
