# Rustavel

基于 Rust + Axum 框架开发的高性能 Web 应用框架。

## 项目特点

- 使用 Rust 语言开发，提供卓越的性能和内存安全性
- 基于 Axum 框架，支持异步处理和高并发
- 模块化架构设计，易于扩展和维护
- 支持 SQL Server 数据库和 Redis 缓存
- 完整的日志系统和错误处理机制
- 支持邮件发送功能
- 多种路由类型：API、Web、渠道和控制台

## 环境要求

- Rust 1.75.0 或更高版本
- SQL Server 2019 或更高版本
- Redis 6.0 或更高版本
- Windows 操作系统（开发环境）

## 安装步骤

1. 克隆项目

```bash
git clone https://github.com/yourusername/rustravel.git
cd rustravel
```

2. 安装依赖

```bash
cargo build
```

3. 配置环境变量
   复制 `.env.example` 文件为 `.env`，并修改相关配置：

```bash
cp .env.example .env
```

4. 配置数据库

- 确保 SQL Server 已安装并运行
- 创建数据库
- 在 `.env` 文件中配置数据库连接信息

## 运行项目

开发环境运行：

```bash
cargo run
```

或使用提供的批处理脚本：

```bash
server.bat
```

生产环境运行：

```bash
cargo run --release
```

## 目录结构

```
src/
├── app/                    # 应用核心目录
│   ├── console/           # 控制台命令目录
│   ├── enums/             # 枚举定义目录
│   ├── facades/           # 门面模式实现
│   ├── grpc/              # GRPC 相关
│   │   ├── controllers/   # GRPC 控制器
│   │   └── middleware/    # GRPC 中间件
│   │
│   ├── http/              # HTTP 相关
│   │   ├── controllers/   # 控制器
│   │   │   ├── basic/     # 基础控制器
│   │   │   ├── logistics/ # 物流相关控制器
│   │   │   ├── nucleus/   # 核心控制器
│   │   │   ├── produce/   # 生产相关控制器
│   │   │   └── test/      # 测试控制器
│   │   ├── middleware/    # HTTP 中间件
│   │   └── mod.rs         # HTTP 模块导出
│   │
│   ├── models/            # 数据模型目录
│   ├── providers/         # 服务提供者目录
│   ├── services/          # 服务层
│   ├── traits/            # 特征定义目录
│   ├── utils/             # 工具类
│   └── mod.rs             # 应用模块导出
│
├── bootstrap/             # 启动引导目录
│   ├── app.rs             # 应用启动类
│   └── mod.rs             # 引导模块导出
│
├── config/                # 配置文件目录
│   ├── app.rs             # 应用配置
│   ├── database.rs        # 数据库配置
│   ├── logging.rs         # 日志配置
│   └── mod.rs             # 配置模块导出
│
├── framework/             # 框架核心目录
│   ├── validation/        # 验证相关功能
│   └── mod.rs             # 框架模块导出
│
├── resources/             # 资源文件目录
│
├── routes/                # 路由目录
│   ├── api.rs             # API 路由入口
│   ├── web.rs             # Web 路由入口
│   ├── channel.rs         # 渠道路由
│   ├── console.rs         # 控制台路由
│   ├── basic/             # 基础路由
│   ├── logistics/         # 物流相关路由
│   ├── nucleus/           # 核心路由
│   ├── produce/           # 生产相关路由
│   ├── test/              # 测试路由
│   └── mod.rs             # 路由模块导出
│
├── storage/               # 存储目录
│
├── main.rs                # 应用入口
└── lib.rs                 # 库入口
```

## 技术栈

- **Web 框架**: Axum 0.8.4
- **异步运行时**: Tokio 1.36.0
- **数据库**:
  - SQL Server (tiberius 0.12.3)
  - 连接池 (bb8 0.8.6)
- **缓存**: Redis (redis 0.24)
- **模板引擎**: Askama 0.14.0
- **日志系统**: tracing 0.1
- **序列化**: Serde 1.0
- **错误处理**: thiserror 1.0, anyhow 1.0
- **邮件发送**: lettre 0.11
- **环境变量**: dotenvy 0.15

## 主要功能模块

### 基础模块 (Basic)

- 基础数据管理
- 系统配置
- 用户认证与授权

### 核心模块 (Nucleus)

- 核心业务逻辑
- 系统中枢功能
- 数据处理中心

### 物流模块 (Logistics)

- 客户管理：客户信息的增删改查
- 仓库管理：仓库信息的增删改查
- 单据管理：物流单据的处理流程

### 生产模块 (Produce)

- 产品信息管理：产品基本信息的增删改查
- 生产计划：生产排期和资源调度
- 质量控制：产品质量监控和管理

### 路由结构

- `/api` - API 接口
  - `/api/basic` - 基础接口
  - `/api/nucleus` - 核心接口
  - `/api/logistics` - 物流相关接口
  - `/api/produce` - 生产相关接口
- `/web` - Web 页面
- `/channel` - 渠道接口
- `/console` - 控制台接口

## 应用启动流程

1. 加载环境变量 (.env 文件)
2. 初始化应用配置
3. 配置日志系统
4. 创建数据库连接池
5. 注册路由
6. 启动 HTTP 服务器
7. 等待关闭信号 (Ctrl+C)

## 日志系统

项目使用 `tracing` 实现日志系统，支持：

- 控制台输出
- 文件记录
- 日志轮转
- 日志压缩（可选）
- 可配置的日志级别

日志文件位置：`storage/logs/YYYY-MM-DD/app.log`

### 日志配置

在 `.env` 文件中配置日志：

```env
# 日志通道
LOG_CHANNEL=stack

# 日志级别 (debug, info, warn, error)
LOG_LEVEL=info

# 日志文件路径
LOG_PATH=storage/logs

# 日志文件最大大小（MB）
LOG_MAX_SIZE=100

# 日志保留天数
LOG_MAX_AGE=30

# 日志备份数量
LOG_MAX_BACKUPS=10

# 是否压缩日志文件（true/false）
LOG_COMPRESS=false
```

## 数据库配置

在 `.env` 文件中配置数据库连接：

```env
DB_CONNECTION=sqlsrv
DB_HOST=127.0.0.1
DB_PORT=1433
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

## 数据库索引命名规范

### 索引命名规则

1. 索引名称必须以 `IX_` 开头，表示这是一个索引（Index）
2. 索引名称应包含表名和索引字段名，用下划线连接
3. 索引名称长度不应超过 128 个字符
4. 索引名称应使用小写字母
5. 避免使用特殊字符，只使用字母、数字和下划线

### 命名示例

```sql
-- 正确的索引命名
CREATE INDEX ix_transit_code_pallet_code_batch_no_order_id_po_number_level_code_code_flag
ON transit_code(pallet_code, batch_no, order_id, po_number, level_code, code_flag);

-- 不推荐的索引命名
CREATE INDEX IX_transit_code_palletCode_batchNo_orderId_poNumber_levelCode_codeFlag
ON transit_code(palletCode, batchNo, orderId, poNumber, levelCode, codeFlag);
```

### 注意事项

1. 索引名称应具有描述性，能够清晰表达索引的用途
2. 对于复合索引，字段名应按重要性顺序排列
3. 避免使用过长的索引名称，如果字段过多，可以考虑使用缩写
4. 保持命名风格的一致性，建议使用下划线命名法（snake_case）

## 缓存配置

在 `.env` 文件中配置 Redis 连接：

```env
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=null
REDIS_DB=0
```

## 邮件配置

在 `.env` 文件中配置邮件服务：

```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
```

## 编码规范

- 使用 4 个空格缩进，不使用制表符
- 行长度最大限制为 100 个字符
- 函数和方法应有明确的文档注释
- 公共 API 必须有文档注释
- 使用 `rustfmt` 格式化代码
- 避免使用 `unwrap()` 和 `expect()`，除非确定不会 panic

### 命名约定

- 模块名使用蛇形命名法(snake_case)
- 结构体、枚举和特征使用驼峰命名法(CamelCase)
- 函数、方法和变量使用蛇形命名法(snake_case)
- 常量使用全大写蛇形命名法(SCREAMING_SNAKE_CASE)
- 类型参数使用驼峰命名法，通常单个大写字母(如 T, E)
- 宏使用蛇形命名法(snake_case)

## 产品模型使用指南

### 概述

产品模型(`Product`)是系统中核心的数据结构之一，用于表示和处理产品相关的信息。该模型支持标准字段和动态字段，可以灵活地处理各种产品数据需求。

### 基本用法

#### 从数据库行创建产品实例

```rust
use std::collections::HashSet;
use tiberius::Row;
use crate::app::models::basic::Product;

// 从单行数据创建产品实例（读取所有字段）
let product = Product::from_row(&row, None, None);

// 只读取指定字段
let mut fields = HashSet::new();
fields.insert("id".to_string());
fields.insert("productName".to_string());
fields.insert("productCode".to_string());
let product = Product::from_row(&row, Some(&fields), None);

// 使用From trait简化创建
let product: Product = (&row).into();
```

#### 批量处理产品数据

```rust
// 批量转换多行数据为产品列表
let products = Product::from_rows(&rows, None, None);

// 批量转换并只读取指定字段
let products = Product::from_rows(&rows, Some(&fields), None);
```

### 动态字段处理

产品模型支持处理标准字段之外的动态字段，这对于处理自定义属性和扩展字段非常有用。

#### 读取动态字段

```rust
// 定义要读取的动态字段
let mut dynamic_fields = HashSet::new();
dynamic_fields.insert("custom_field1".to_string());
dynamic_fields.insert("custom_field2".to_string());

// 创建包含动态字段的产品实例
let product = Product::from_row(&row, None, Some(&dynamic_fields));

// 读取动态字段值
if let Some(value) = product.get_dynamic::<String>("custom_field1") {
    println!("自定义字段值: {}", value);
}

// 读取不同类型的动态字段
let number_value: Option<i32> = product.get_dynamic("number_field");
let boolean_value: Option<bool> = product.get_dynamic("boolean_field");
let complex_value: Option<HashMap<String, String>> = product.get_dynamic("complex_field");
```

#### 设置动态字段

```rust
// 创建产品实例
let mut product = Product::from_row(&row, None, None);

// 设置各种类型的动态字段
product.set_dynamic("custom_string", "自定义值").unwrap();
product.set_dynamic("custom_number", 42).unwrap();
product.set_dynamic("custom_boolean", true).unwrap();

// 设置复杂类型
let mut complex_data = HashMap::new();
complex_data.insert("key1".to_string(), "value1".to_string());
complex_data.insert("key2".to_string(), "value2".to_string());
product.set_dynamic("complex_data", complex_data).unwrap();
```

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

[MIT License](LICENSE)
