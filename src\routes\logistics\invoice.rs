use crate::app::http::controllers::logistics::InvoiceController;
use crate::app::providers::DatabaseServiceProvider;
use axum::{
    routing::{get, post},
    Router,
};
use std::sync::Arc;

/// 单据路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().merge(
        Router::new()
            .route("/invoice/page", get(InvoiceController::page))
            .route("/invoice/list", get(InvoiceController::index))
            .route("/invoice/detail", get(InvoiceController::detail))
            .route("/invoice/statistics", get(InvoiceController::statistics))
            .route("/invoice/upload", post(InvoiceController::upload)),
    )
}
