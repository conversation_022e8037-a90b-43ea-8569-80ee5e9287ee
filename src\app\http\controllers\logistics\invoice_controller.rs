use crate::app::models::ware::order::Order;
use crate::app::providers::DatabaseServiceProvider;
use crate::app::services::logistics::invoice_service;
use crate::app::traits::response::track;
use axum::extract::{Query, State};
use axum::response::IntoResponse;
use chrono;
use chrono::Datelike;
use futures_util::TryStreamExt;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tiberius::ToSql;
use tracing::{error, info};

/// 查询参数
#[derive(Debug, Deserialize)]
pub struct QueryParams {
    /// 页码
    #[serde(default = "default_page")]
    pub page: i32,
    /// 每页数量
    #[serde(rename = "limit", default = "default_limit")]
    pub per_page: i32,
    /// 单据ID
    pub id: Option<i32>,
    /// 单据编号
    #[serde(rename = "wareCode")]
    pub ware_code: Option<String>,
    /// 单据状态
    pub status: Option<i32>,
    /// 出库日期
    #[serde(rename = "wareDate")]
    pub ware_date: Option<String>,
    /// 来源
    pub channel: Option<i32>,
}

/// 默认页码
fn default_page() -> i32 {
    1
}

/// 默认每页数量
fn default_limit() -> i32 {
    20
}

/// 查询结果
#[derive(Debug, Serialize)]
pub struct QueryResult {
    /// 数据列表
    pub list: Vec<Order>,
    /// 总记录数
    pub count: i32,
}

/// 单据控制器
pub struct InvoiceController;

impl InvoiceController {
    /// 查询单据列表（不分页）
    pub async fn index(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Query(params): Query<QueryParams>,
    ) -> impl IntoResponse {
        info!("[单据列表] 开始获取单据列表");
        info!(
            "[单据列表] 查询参数: id={:?}, ware_code={:?}, status={:?}, ware_date={:?}, channel={:?}",
            params.id, params.ware_code, params.status, params.ware_date, params.channel
        );

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[单据列表] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[单据列表] 获取数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 构建查询条件
        let mut where_conditions = Vec::new();
        let mut query_params: Vec<Box<dyn ToSql>> = Vec::new();
        let mut param_index = 1;

        // 添加查询条件
        if let Some(id) = params.id {
            if id > 0 {
                where_conditions.push(format!("wo.id = @P{}", param_index));
                query_params.push(Box::new(id));
                param_index += 1;
            }
        }

        if let Some(ware_code) = &params.ware_code {
            if !ware_code.trim().is_empty() {
                where_conditions.push(format!("wo.wareCode = @P{}", param_index));
                query_params.push(Box::new(ware_code.clone()));
                param_index += 1;
            }
        }

        if let Some(status) = params.status {
            if status >= 0 {
                where_conditions.push(format!("wo.estate = @P{}", param_index));
                query_params.push(Box::new(status));
                param_index += 1;
            }
        }

        if let Some(ware_date) = &params.ware_date {
            if !ware_date.trim().is_empty() {
                where_conditions.push(format!(
                    "CONVERT(varchar(10), wo.wareDate, 120) = @P{}",
                    param_index
                ));
                query_params.push(Box::new(ware_date.clone()));
                param_index += 1;
            }
        }

        if let Some(channel) = params.channel {
            where_conditions.push(format!("wo.channel = @P{}", param_index));
            query_params.push(Box::new(channel));
        }

        // 组合 WHERE 子句
        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 构建 SQL 查询
        let sql = format!(
            r#"
            SELECT 
                wo.id,
                wo.plantId,
                wo.wareCode,
                wo.wareHouseCode,
                wo.produceCode,
                wo.cusCode,
                wo.receiverCode,
                CONVERT(varchar(19), wo.wareDate, 120) as wareDate,
                wo.poNumber,
                wo.remark,
                wo.batchList,
                wo.completeFlag,
                wo.estate,
                CONVERT(varchar(19), wo.createTime, 120) as createTime,
                CONVERT(varchar(19), wo.updateTime, 120) as updateTime,
                wo.fileName,
                wo.receiveMailbox,
                wo.orderType,
                wo.outboundType,
                wo.channel,
                wo.invoiceNo,
                bp.plantName,
                wc.cusName as customerName,
                wr.cusName as receiverName,
                wh.cusName as wareHouseName
            FROM ware_order wo WITH (NOLOCK)
            LEFT JOIN basic_plant bp WITH (NOLOCK) ON wo.plantId = bp.id
            LEFT JOIN ware_customer wc WITH (NOLOCK) ON wo.cusCode = wc.cusCode
            LEFT JOIN ware_customer wr WITH (NOLOCK) ON wo.receiverCode = wr.cusCode
            LEFT JOIN ware_customer wh WITH (NOLOCK) ON wo.wareHouseCode = wh.cusCode
            {where_clause}
            ORDER BY wo.id DESC
            "#,
            where_clause = where_clause
        );

        // 构建参数引用
        let params_ref: Vec<&dyn ToSql> = query_params.iter().map(|p| p.as_ref()).collect();

        // 执行查询
        let mut invoices = Vec::new();

        // 执行查询并处理结果
        let result = match conn.query(&sql, &params_ref).await {
            Ok(mut stream) => {
                // 处理查询结果流
                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        // 使用Order的renovation方法，自动处理字段
                        let mut invoice = Order::renovation(&row);

                        // 添加关联表字段
                        if let Some(plant_name) = row.get::<&str, _>("plantName") {
                            invoice.set_dynamic("plantName", plant_name).ok();
                        }

                        if let Some(customer_name) = row.get::<&str, _>("customerName") {
                            invoice.set_dynamic("customerName", customer_name).ok();
                        }

                        if let Some(receiver_name) = row.get::<&str, _>("receiverName") {
                            invoice.set_dynamic("receiverName", receiver_name).ok();
                        }

                        if let Some(ware_house_name) = row.get::<&str, _>("wareHouseName") {
                            invoice.set_dynamic("wareHouseName", ware_house_name).ok();
                        }

                        invoices.push(invoice);
                    }
                }
                info!("[单据列表] 查询成功，获取到{}条记录", invoices.len());
                Ok(())
            }
            Err(e) => {
                error!("[单据列表] 查询失败: {}", e);
                Err(e.to_string())
            }
        };

        // 根据查询结果返回响应
        match result {
            Ok(_) => {
                // 使用to_response方法，直接返回数组
                let mut invoices_json = Vec::new();

                for o in &invoices {
                    // 创建一个包含所有字段的响应
                    let mut response = o.to_response();

                    // 确保关联表字段也被包含在响应中
                    if let Some(plant_name) = o.get_dynamic::<String>("plantName") {
                        response["plantName"] = serde_json::Value::String(plant_name);
                    }

                    if let Some(customer_name) = o.get_dynamic::<String>("customerName") {
                        response["customerName"] = serde_json::Value::String(customer_name);
                    }

                    if let Some(receiver_name) = o.get_dynamic::<String>("receiverName") {
                        response["receiverName"] = serde_json::Value::String(receiver_name);
                    }

                    if let Some(ware_house_name) = o.get_dynamic::<String>("wareHouseName") {
                        response["wareHouseName"] = serde_json::Value::String(ware_house_name);
                    }

                    // 处理batchList字段，如果是JSON字符串则解析
                    let mut batch_info = Vec::new();

                    // 处理 batchList，并准备异步处理的参数
                    let mut batch_process_params = Vec::new();

                    if let Some(batch_list) = response.get("batchList").and_then(|v| v.as_str()) {
                        if let Ok(batch_list_json) =
                            serde_json::from_str::<serde_json::Value>(batch_list)
                        {
                            response["batchList"] = batch_list_json.clone();

                            // 收集处理参数
                            if let Some(batch_array) = batch_list_json.as_array() {
                                for batch in batch_array {
                                    if let (Some(product_id), Some(order_id), Some(batch_no)) = (
                                        batch
                                            .get("productId")
                                            .and_then(|v| v.as_i64())
                                            .map(|v| v as i32),
                                        batch
                                            .get("orderId")
                                            .and_then(|v| v.as_i64())
                                            .map(|v| v as i32),
                                        batch
                                            .get("batchNo")
                                            .and_then(|v| v.as_str())
                                            .map(|s| s.to_string()),
                                    ) {
                                        batch_process_params.push((
                                            batch.clone(),
                                            product_id,
                                            order_id,
                                            batch_no,
                                        ));
                                    } else {
                                        // 如果没有必要的字段，只保留原始数据
                                        batch_info.push(batch.clone());
                                    }
                                }
                            }
                        }
                    }

                    // 顺序处理每个批次
                    for (batch, product_id, order_id, batch_no) in batch_process_params {
                        let batch_data = invoice_service::process_batch(
                            &db, &batch, product_id, order_id, &batch_no,
                        )
                        .await;
                        batch_info.push(batch_data);
                    }

                    // 添加增强的批次信息
                    if !batch_info.is_empty() {
                        response["batchInfo"] = serde_json::json!(batch_info);
                    }

                    invoices_json.push(response);
                }

                info!("[单据列表] 返回成功，返回{}条记录", invoices_json.len());
                track().victory().data(invoices_json).build()
            }
            Err(e) => {
                error!("[单据列表] 返回错误: {}", e);
                track().defeat().message(e).build()
            }
        }
    }

    /// 查询单据列表（分页）
    pub async fn page(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Query(params): Query<QueryParams>,
    ) -> impl IntoResponse {
        info!("[单据分页] 开始获取单据分页列表");
        info!(
            "[单据分页] 查询参数: page={}, per_page={}, id={:?}, ware_code={:?}, status={:?}, ware_date={:?}, channel={:?}",
            params.page, params.per_page, params.id, params.ware_code, params.status, params.ware_date, params.channel
        );

        // 验证分页参数
        let page = if params.page <= 0 {
            info!("[单据分页] 页码参数不合法({}), 使用默认值1", params.page);
            1
        } else {
            params.page
        };

        // 限制每页数量，防止请求过大导致性能问题
        let max_per_page = 100; // 设置最大每页数量为100
        let per_page = if params.per_page <= 0 {
            info!(
                "[单据分页] 每页数量参数不合法({}), 使用默认值20",
                params.per_page
            );
            20
        } else if params.per_page > max_per_page {
            info!(
                "[单据分页] 每页数量过大({}), 已限制为最大值{}",
                params.per_page, max_per_page
            );
            max_per_page
        } else {
            params.per_page
        };

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[单据分页] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[单据分页] 获取数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 构建查询条件
        let mut where_conditions = Vec::new();
        let mut query_params: Vec<Box<dyn ToSql>> = Vec::new();
        let mut param_index = 1;

        // 添加查询条件
        if let Some(id) = params.id {
            if id > 0 {
                where_conditions.push(format!("wo.id = @P{}", param_index));
                query_params.push(Box::new(id));
                param_index += 1;
            }
        }

        if let Some(ware_code) = &params.ware_code {
            if !ware_code.trim().is_empty() {
                where_conditions.push(format!("wo.wareCode = @P{}", param_index));
                query_params.push(Box::new(ware_code.clone()));
                param_index += 1;
            }
        }

        if let Some(status) = params.status {
            if status >= 0 {
                where_conditions.push(format!("wo.estate = @P{}", param_index));
                query_params.push(Box::new(status));
                param_index += 1;
            }
        }

        if let Some(ware_date) = &params.ware_date {
            if !ware_date.trim().is_empty() {
                where_conditions.push(format!(
                    "CONVERT(varchar(10), wo.wareDate, 120) = @P{}",
                    param_index
                ));
                query_params.push(Box::new(ware_date.clone()));
                param_index += 1;
            }
        }

        if let Some(channel) = params.channel {
            where_conditions.push(format!("wo.channel = @P{}", param_index));
            query_params.push(Box::new(channel));
        }

        // 组合 WHERE 子句
        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 计算分页参数
        let offset = (page - 1) * per_page;
        let limit = per_page;

        // 构建 SQL 查询
        let sql = format!(
            r#"
            WITH InvoiceData AS (
                SELECT 
                    wo.id,
                    wo.plantId,
                    wo.wareCode,
                    wo.wareHouseCode,
                    wo.produceCode,
                    wo.cusCode,
                    wo.receiverCode,
                    CONVERT(varchar(19), wo.wareDate, 120) as wareDate,
                    wo.poNumber,
                    wo.remark,
                    wo.batchList,
                    wo.completeFlag,
                    wo.estate,
                    CONVERT(varchar(19), wo.createTime, 120) as createTime,
                    CONVERT(varchar(19), wo.updateTime, 120) as updateTime,
                    wo.fileName,
                    wo.receiveMailbox,
                    wo.orderType,
                    wo.outboundType,
                    wo.channel,
                    wo.invoiceNo,
                    bp.plantName,
                    wc.cusName as customerName,
                    wr.cusName as receiverName,
                    wh.cusName as wareHouseName,
                    ROW_NUMBER() OVER (ORDER BY wo.id DESC) AS RowNum
                FROM ware_order wo WITH (NOLOCK)
                LEFT JOIN basic_plant bp WITH (NOLOCK) ON wo.plantId = bp.id
                LEFT JOIN ware_customer wc WITH (NOLOCK) ON wo.cusCode = wc.cusCode
                LEFT JOIN ware_customer wr WITH (NOLOCK) ON wo.receiverCode = wr.cusCode
                LEFT JOIN ware_customer wh WITH (NOLOCK) ON wo.wareHouseCode = wh.cusCode
                {where_clause}
            )
            SELECT * FROM InvoiceData
            WHERE RowNum BETWEEN @P{param_index} AND @P{next_param_index}
            "#,
            where_clause = where_clause,
            param_index = param_index,
            next_param_index = param_index + 1
        );

        // 构建计数 SQL
        let count_sql = format!(
            r#"
            SELECT COUNT(1) as total
            FROM ware_order wo WITH (NOLOCK)
            {where_clause}
            "#,
            where_clause = where_clause
        );

        // 构建参数引用 - 为计数查询创建一个独立的参数引用
        let count_params_ref: Vec<&dyn ToSql> = query_params.iter().map(|p| p.as_ref()).collect();

        // 执行计数查询
        let total = match conn.query(&count_sql, &count_params_ref).await {
            Ok(stream) => match stream.into_first_result().await {
                Ok(rows) => {
                    if !rows.is_empty() {
                        let count = rows[0].get::<i32, _>("total").unwrap_or(0);
                        count
                    } else {
                        0
                    }
                }
                Err(e) => {
                    error!("[单据分页] 解析计数查询结果失败: {}", e);
                    0
                }
            },
            Err(e) => {
                error!("[单据分页] 执行计数查询失败: {}", e);
                0
            }
        };

        // 如果总记录数为0，直接返回空结果
        if total == 0 {
            info!("[单据分页] 总记录数为0，直接返回空结果");
            return track()
                .victory()
                .message("成功返回单据分页列表")
                .data(serde_json::json!({
                    "list": Vec::<serde_json::Value>::new(),
                    "count": 0
                }))
                .build();
        }

        // 调整分页参数，确保不会超出范围
        let adjusted_page = if offset >= total {
            // 如果请求的页码超出范围，则返回第一页
            1
        } else {
            page
        };

        let adjusted_offset = (adjusted_page - 1) * per_page;

        // 添加分页参数
        query_params.push(Box::new(adjusted_offset + 1)); // SQL Server的ROW_NUMBER从1开始
        query_params.push(Box::new(adjusted_offset + limit));

        let params_ref: Vec<&dyn ToSql> = query_params.iter().map(|p| p.as_ref()).collect();

        // 执行分页查询
        let mut invoices = Vec::new();

        // 执行查询并处理结果
        let result = match conn.query(&sql, &params_ref).await {
            Ok(mut stream) => {
                // 处理查询结果流
                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        // 使用Order的renovation方法，自动处理字段
                        let mut invoice = Order::renovation(&row);

                        // 添加关联表字段
                        if let Some(plant_name) = row.get::<&str, _>("plantName") {
                            invoice.set_dynamic("plantName", plant_name).ok();
                        }

                        if let Some(customer_name) = row.get::<&str, _>("customerName") {
                            invoice.set_dynamic("customerName", customer_name).ok();
                        }

                        if let Some(receiver_name) = row.get::<&str, _>("receiverName") {
                            invoice.set_dynamic("receiverName", receiver_name).ok();
                        }

                        if let Some(ware_house_name) = row.get::<&str, _>("wareHouseName") {
                            invoice.set_dynamic("wareHouseName", ware_house_name).ok();
                        }

                        invoices.push(invoice);
                    }
                }
                info!("[单据分页] 查询成功，获取到{}条记录", invoices.len());

                if adjusted_page != page {
                    info!(
                        "[单据分页] 请求的页码({})超出范围，已调整为第{}页",
                        page, adjusted_page
                    );
                }

                Ok(())
            }
            Err(e) => {
                error!("[单据分页] 查询失败: {}", e);
                Err(e.to_string())
            }
        };

        // 根据查询结果返回响应
        match result {
            Ok(_) => {
                // 使用to_response方法，直接返回数组
                let mut invoices_json = Vec::new();

                for o in &invoices {
                    // 创建一个包含所有字段的响应
                    let mut response = o.to_response();

                    // 确保关联表字段也被包含在响应中
                    if let Some(plant_name) = o.get_dynamic::<String>("plantName") {
                        response["plantName"] = serde_json::Value::String(plant_name);
                    }

                    if let Some(customer_name) = o.get_dynamic::<String>("customerName") {
                        response["customerName"] = serde_json::Value::String(customer_name);
                    }

                    if let Some(receiver_name) = o.get_dynamic::<String>("receiverName") {
                        response["receiverName"] = serde_json::Value::String(receiver_name);
                    }

                    if let Some(ware_house_name) = o.get_dynamic::<String>("wareHouseName") {
                        response["wareHouseName"] = serde_json::Value::String(ware_house_name);
                    }

                    // 处理batchList字段，如果是JSON字符串则解析
                    let mut batch_info = Vec::new();

                    // 处理 batchList，并准备异步处理的参数
                    let mut batch_process_params = Vec::new();

                    if let Some(batch_list) = response.get("batchList").and_then(|v| v.as_str()) {
                        if let Ok(batch_list_json) =
                            serde_json::from_str::<serde_json::Value>(batch_list)
                        {
                            response["batchList"] = batch_list_json.clone();

                            // 收集处理参数
                            if let Some(batch_array) = batch_list_json.as_array() {
                                for batch in batch_array {
                                    if let (Some(product_id), Some(order_id), Some(batch_no)) = (
                                        batch
                                            .get("productId")
                                            .and_then(|v| v.as_i64())
                                            .map(|v| v as i32),
                                        batch
                                            .get("orderId")
                                            .and_then(|v| v.as_i64())
                                            .map(|v| v as i32),
                                        batch
                                            .get("batchNo")
                                            .and_then(|v| v.as_str())
                                            .map(|s| s.to_string()),
                                    ) {
                                        batch_process_params.push((
                                            batch.clone(),
                                            product_id,
                                            order_id,
                                            batch_no,
                                        ));
                                    } else {
                                        // 如果没有必要的字段，只保留原始数据
                                        batch_info.push(batch.clone());
                                    }
                                }
                            }
                        }
                    }

                    // 顺序处理每个批次
                    for (batch, product_id, order_id, batch_no) in batch_process_params {
                        let batch_data = invoice_service::process_batch(
                            &db, &batch, product_id, order_id, &batch_no,
                        )
                        .await;
                        batch_info.push(batch_data);
                    }

                    // 添加增强的批次信息
                    if !batch_info.is_empty() {
                        response["batchInfo"] = serde_json::json!(batch_info);
                    }

                    invoices_json.push(response);
                }

                track()
                    .victory()
                    .message("成功返回单据分页列表")
                    .data(serde_json::json!({
                                "list": invoices_json,
                        "count": total
                    }))
                    .build()
            }
            Err(e) => track().defeat().message(e).build(),
        }
    }

    /// 查询单据详情
    pub async fn detail(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Query(params): Query<QueryParams>,
    ) -> impl IntoResponse {
        info!(
            "[单据详情] 开始获取单据详情, 参数: id={:?}, ware_code={:?}",
            params.id, params.ware_code
        );

        // 验证参数
        if params.id.is_none() {
            error!("[单据详情] 参数错误: 单据ID不能为空");
            return track().defeat().message("参数错误: 单据ID不能为空").build();
        }

        if params.ware_code.is_none() {
            error!("[单据详情] 参数错误: 单据编号不能为空");
            return track()
                .defeat()
                .message("参数错误: 单据编号不能为空")
                .build();
        }

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[单据详情] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[单据详情] 获取数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 构建查询条件
        let mut where_conditions = Vec::new();
        let mut query_params: Vec<Box<dyn ToSql>> = Vec::new();
        let mut param_index = 1;

        // 添加查询条件
        if let Some(id) = params.id {
            if id > 0 {
                where_conditions.push(format!("wo.id = @P{}", param_index));
                query_params.push(Box::new(id));
                param_index += 1;
            }
        }

        if let Some(ware_code) = &params.ware_code {
            if !ware_code.trim().is_empty() {
                where_conditions.push(format!("wo.wareCode = @P{}", param_index));
                query_params.push(Box::new(ware_code.clone()));
            }
        }

        // 组合 WHERE 子句
        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 构建 SQL 查询
        let sql = format!(
            r#"
            SELECT 
                wo.id,
                wo.plantId,
                wo.wareCode,
                wo.wareHouseCode,
                wo.produceCode,
                wo.cusCode,
                wo.receiverCode,
                CONVERT(varchar(19), wo.wareDate, 120) as wareDate,
                wo.poNumber,
                wo.remark,
                wo.batchList,
                wo.completeFlag,
                wo.estate,
                CONVERT(varchar(19), wo.createTime, 120) as createTime,
                CONVERT(varchar(19), wo.updateTime, 120) as updateTime,
                wo.fileName,
                wo.receiveMailbox,
                wo.orderType,
                wo.outboundType,
                wo.channel,
                wo.invoiceNo,
                bp.plantName,
                wc.cusName as customerName,
                wr.cusName as receiverName,
                wh.cusName as wareHouseName
            FROM ware_order wo WITH (NOLOCK)
            LEFT JOIN basic_plant bp WITH (NOLOCK) ON wo.plantId = bp.id
            LEFT JOIN ware_customer wc WITH (NOLOCK) ON wo.cusCode = wc.cusCode
            LEFT JOIN ware_customer wr WITH (NOLOCK) ON wo.receiverCode = wr.cusCode
            LEFT JOIN ware_customer wh WITH (NOLOCK) ON wo.wareHouseCode = wh.cusCode
            {where_clause}
            "#,
            where_clause = where_clause
        );

        // 构建参数引用
        let params_ref: Vec<&dyn ToSql> = query_params.iter().map(|p| p.as_ref()).collect();

        // 执行查询
        let result = match conn.query(&sql, &params_ref).await {
            Ok(stream) => match stream.into_first_result().await {
                Ok(rows) => {
                    if rows.is_empty() {
                        error!(
                            "[单据详情] 未找到单据, 查询参数: id={:?}, ware_code={:?}",
                            params.id, params.ware_code
                        );
                        return track().defeat().message("未找到单据").build();
                    }

                    let row = &rows[0];
                    // 使用Order的renovation方法，自动处理字段
                    let mut invoice = Order::renovation(row);

                    // 添加关联表字段
                    if let Some(plant_name) = row.get::<&str, _>("plantName") {
                        invoice.set_dynamic("plantName", plant_name).ok();
                    }

                    if let Some(customer_name) = row.get::<&str, _>("customerName") {
                        invoice.set_dynamic("customerName", customer_name).ok();
                    }

                    if let Some(receiver_name) = row.get::<&str, _>("receiverName") {
                        invoice.set_dynamic("receiverName", receiver_name).ok();
                    }

                    if let Some(ware_house_name) = row.get::<&str, _>("wareHouseName") {
                        invoice.set_dynamic("wareHouseName", ware_house_name).ok();
                    }

                    // 创建响应
                    let mut response = invoice.to_response();

                    // 确保关联表字段也被包含在响应中
                    if let Some(plant_name) = invoice.get_dynamic::<String>("plantName") {
                        response["plantName"] = serde_json::Value::String(plant_name);
                    }

                    if let Some(customer_name) = invoice.get_dynamic::<String>("customerName") {
                        response["customerName"] = serde_json::Value::String(customer_name);
                    }

                    if let Some(receiver_name) = invoice.get_dynamic::<String>("receiverName") {
                        response["receiverName"] = serde_json::Value::String(receiver_name);
                    }

                    if let Some(ware_house_name) = invoice.get_dynamic::<String>("wareHouseName") {
                        response["wareHouseName"] = serde_json::Value::String(ware_house_name);
                    }

                    // 处理batchList字段，如果是JSON字符串则解析
                    let mut batch_info = Vec::new();

                    // 处理 batchList，并准备异步处理的参数
                    let mut batch_process_params = Vec::new();

                    if let Some(batch_list) = response.get("batchList").and_then(|v| v.as_str()) {
                        if let Ok(batch_list_json) =
                            serde_json::from_str::<serde_json::Value>(batch_list)
                        {
                            response["batchList"] = batch_list_json.clone();

                            // 收集处理参数
                            if let Some(batch_array) = batch_list_json.as_array() {
                                for batch in batch_array {
                                    if let (Some(product_id), Some(order_id), Some(batch_no)) = (
                                        batch
                                            .get("productId")
                                            .and_then(|v| v.as_i64())
                                            .map(|v| v as i32),
                                        batch
                                            .get("orderId")
                                            .and_then(|v| v.as_i64())
                                            .map(|v| v as i32),
                                        batch
                                            .get("batchNo")
                                            .and_then(|v| v.as_str())
                                            .map(|s| s.to_string()),
                                    ) {
                                        batch_process_params.push((
                                            batch.clone(),
                                            product_id,
                                            order_id,
                                            batch_no,
                                        ));
                                    } else {
                                        // 如果没有必要的字段，只保留原始数据
                                        batch_info.push(batch.clone());
                                    }
                                }
                            }
                        }
                    }

                    // 顺序处理每个批次
                    for (batch, product_id, order_id, batch_no) in batch_process_params {
                        let batch_data = invoice_service::process_batch(
                            &db, &batch, product_id, order_id, &batch_no,
                        )
                        .await;
                        batch_info.push(batch_data);
                    }

                    // 添加增强的批次信息
                    if !batch_info.is_empty() {
                        response["batchInfo"] = serde_json::json!(batch_info);
                    }

                    info!(
                        "[单据详情] 成功获取单据详情, id={:?}, ware_code={:?}",
                        row.get::<i32, _>("id"),
                        row.get::<&str, _>("wareCode")
                    );
                    Ok(response)
                }
                Err(e) => {
                    error!(
                        "[单据详情] 查询失败: {}, 查询参数: id={:?}, ware_code={:?}",
                        e, params.id, params.ware_code
                    );
                    Err(e.to_string())
                }
            },
            Err(e) => {
                error!(
                    "[单据详情] 查询失败: {}, 查询参数: id={:?}, ware_code={:?}",
                    e, params.id, params.ware_code
                );
                Err(e.to_string())
            }
        };

        // 根据查询结果返回响应
        match result {
            Ok(data) => {
                info!(
                    "[单据详情] 返回成功, id={}, ware_code={}",
                    data.get("id").and_then(|v| v.as_i64()).unwrap_or(0),
                    data.get("wareCode").and_then(|v| v.as_str()).unwrap_or("")
                );
                track()
                    .victory()
                    .message("成功获取单据详情")
                    .data(data)
                    .build()
            }
            Err(e) => {
                error!(
                    "[单据详情] 返回错误: {}, 查询参数: id={:?}, ware_code={:?}",
                    e, params.id, params.ware_code
                );
                track().defeat().message(e).build()
            }
        }
    }

    /// 查询单据统计信息
    pub async fn statistics(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Query(_params): Query<QueryParams>,
    ) -> impl IntoResponse {
        info!("[单据统计] 开始获取单据统计信息");

        // 获取当前日期和当前周的日期范围
        let now = chrono::Local::now().naive_local().date();
        let days_from_monday = now.weekday().num_days_from_monday();
        let this_monday = now - chrono::Duration::days(days_from_monday as i64);

        // 生成本周七天的日期字符串和中文星期名
        let mut dates = Vec::new();
        let mut categories = Vec::new();
        let weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];

        for i in 0..7 {
            let date = this_monday + chrono::Duration::days(i);
            dates.push(date.format("%Y-%m-%d").to_string());
            categories.push(weekdays[i as usize].to_string());
        }

        info!("[单据统计] 本周日期范围: {:?}", dates);

        // 执行查询
        let mut client = match db.get_connection().await {
            Ok(client) => {
                info!("[单据统计] 数据库连接获取成功");
                client
            }
            Err(e) => {
                error!("[单据统计] 获取数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 1. 首先查询汇总统计数据
        let summary_sql = r#"
            SELECT 
                COUNT(1) as totalOrderCount,
                COUNT(CASE WHEN wo.estate = 3 THEN 1 ELSE NULL END) as actualOrderCount,
                SUM(CASE WHEN wo.estate = 3 THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(1), 0) as completionRate
            FROM ware_order wo WITH (NOLOCK)
            WHERE CONVERT(varchar(10), wo.wareDate, 120) BETWEEN @P1 AND @P2
        "#;

        // 准备查询参数，传入一周的第一天和最后一天
        let mut summary_params: Vec<&dyn ToSql> = Vec::new();
        summary_params.push(&dates[0] as &dyn ToSql); // 周一
        summary_params.push(&dates[6] as &dyn ToSql); // 周日

        let summary_result = match client.query(summary_sql, &summary_params).await {
            Ok(result) => result,
            Err(e) => {
                error!("[单据统计] 执行汇总统计查询失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 处理汇总结果
        let mut total_order_count = 0;
        let mut actual_order_count = 0;
        let mut completion_rate = 0.0;

        match summary_result.into_row().await {
            Ok(Some(row)) => {
                total_order_count = row.get::<i32, _>("totalOrderCount").unwrap_or(0);
                actual_order_count = row.get::<i32, _>("actualOrderCount").unwrap_or(0);
                // 安全处理浮点数转换
                completion_rate = match row.try_get::<f64, _>("completionRate") {
                    Ok(Some(rate)) => rate,
                    _ => {
                        // 手动计算完成率
                        if total_order_count > 0 {
                            (actual_order_count as f64 / total_order_count as f64) * 100.0
                        } else {
                            0.0
                        }
                    }
                };
            }
            Ok(None) => {
                info!("[单据统计] 未查询到汇总统计信息");
            }
            Err(e) => {
                error!("[单据统计] 查询汇总统计信息失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        }

        // 2. 查询日期分布统计
        // 构建统计查询SQL - 计划发运（所有状态）和实际发运（状态为3）
        let stats_sql = r#"
            WITH DateData AS (
                SELECT 
                    CONVERT(varchar(10), wo.wareDate, 120) as dateStr,
                    COUNT(CASE WHEN wo.estate = 3 THEN 1 ELSE NULL END) as actualShipment,
                    COUNT(1) as plannedShipment
                FROM ware_order wo WITH (NOLOCK)
                WHERE CONVERT(varchar(10), wo.wareDate, 120) BETWEEN @P1 AND @P7
                GROUP BY CONVERT(varchar(10), wo.wareDate, 120)
            )
            SELECT 
                dd.dateStr,
                dd.actualShipment,
                dd.plannedShipment
            FROM DateData dd
            ORDER BY dd.dateStr
        "#;

        info!("[单据统计] 执行单据统计查询SQL");

        // 准备查询参数，传入一周的每一天日期
        let mut query_params: Vec<&dyn ToSql> = Vec::new();
        for date in &dates {
            query_params.push(date as &dyn ToSql);
        }

        // 查询统计
        let result = match client.query(stats_sql, &query_params).await {
            Ok(result) => result,
            Err(e) => {
                error!("[单据统计] 执行统计查询失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        info!("[单据统计] 统计查询执行成功");

        // 处理结果数据
        let mut date_map: std::collections::HashMap<String, (i32, i32)> =
            std::collections::HashMap::new();

        // 将查询结果存入映射表
        match result.into_results().await {
            Ok(results) => {
                if let Some(rows) = results.first() {
                    for row in rows {
                        let date_str = row
                            .get::<&str, _>("dateStr")
                            .unwrap_or_default()
                            .to_string();
                        let actual = row.get::<i32, _>("actualShipment").unwrap_or(0);
                        let planned = row.get::<i32, _>("plannedShipment").unwrap_or(0);
                        date_map.insert(date_str, (planned, actual));
                    }
                }
            }
            Err(e) => {
                error!("[单据统计] 获取统计结果失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        }

        // 准备图表数据
        let mut planned_data = Vec::new();
        let mut actual_data = Vec::new();

        for date in &dates {
            let (planned, actual) = date_map.get(date).copied().unwrap_or((0, 0));
            planned_data.push(planned);
            actual_data.push(actual);
        }

        // 构建统计卡片数据
        let summary_cards = vec![
            serde_json::json!({
                "name": "本周发运",
                "orderCount": total_order_count,
                "showPercentage": true,
                "completionRate": completion_rate.round() as i32 // 四舍五入为整数
            }),
            serde_json::json!({
                "name": "实际发运",
                "orderCount": actual_order_count,
                "showPercentage": false
            }),
            serde_json::json!({
                "name": "计划发运",
                "orderCount": total_order_count,
                "showPercentage": false
            }),
        ];

        // 构建返回数据
        let response_data = serde_json::json!({
            "categories": categories,
            "series": [
                {
                    "name": "计划发运",
                    "data": planned_data
                },
                {
                    "name": "实际发运",
                    "data": actual_data
                }
            ],
            "summary": summary_cards
        });

        info!("[单据统计] 成功获取单据统计信息");
        track()
            .victory()
            .message("成功获取单据统计信息")
            .data(response_data)
            .build()
    }

    /// 在线上传单据
    pub async fn upload(
        State(db): State<Arc<DatabaseServiceProvider>>,
        axum::extract::Json(payload): axum::extract::Json<serde_json::Value>,
    ) -> impl IntoResponse {
        info!("[单据上传] 开始在线上传单据");

        // 获取请求参数
        let codes = match payload.get("codes") {
            Some(codes) => codes,
            None => {
                error!("[单据上传] 上传单据失败: codes参数不能为空");
                return track().defeat().message("codes参数不能为空").build();
            }
        };

        let ware_order_id = match payload.get("orderId").and_then(|id| id.as_i64()) {
            Some(id) => id as i32,
            None => {
                error!("[单据上传] 上传单据失败: orderId参数不能为空");
                return track().defeat().message("orderId参数不能为空").build();
            }
        };

        let channel = match payload.get("channel").and_then(|c| c.as_i64()) {
            Some(c) => c as i32,
            None => 1, // 默认值 1 表示 WMS 上传的单据
        };

        // 记录请求日志
        let request_log = serde_json::to_string(&payload).unwrap_or_default();
        let client_ip = "127.0.0.1".to_string(); // 实际项目中应该从请求中获取

        // 获取数据库连接 - 这个连接仅用于记录日志，不参与事务
        if let Ok(mut log_client) = db.get_connection().await {
            let _ = log_client
                .query(
                    "INSERT INTO wms_api_log (requestLog, responseLog, api, ip, result, type) VALUES (@P1, @P2, @P3, @P4, @P5, @P6)",
                    &[
                        &request_log as &dyn ToSql,
                        &"" as &dyn ToSql,
                        &"api/produce/order/ware" as &dyn ToSql,
                        &client_ip as &dyn ToSql,
                        &"成功" as &dyn ToSql,
                        &"1" as &dyn ToSql,
                    ],
                )
                .await;
        }

        // 获取一个新的数据库连接用于所有操作
        let conn = match db.get_connection().await {
            Ok(conn) => conn,
            Err(e) => {
                error!("[单据上传] 获取数据库连接获取失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("获取数据库连接获取失败: {}", e))
                    .build();
            }
        };

        // 查询单据状态 - 在事务外执行
        let mut check_conn = match db.get_connection().await {
            Ok(conn) => conn,
            Err(e) => {
                error!("[单据上传] 获取数据库连接获取失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("获取数据库连接获取失败: {}", e))
                    .build();
            }
        };

        let ware_order_sql = "SELECT id, estate, orderType FROM ware_order WITH (NOLOCK) WHERE id = @P1 AND estate = 10 AND orderType = 1";

        let ware_order_rows = match check_conn
            .query(ware_order_sql, &[&ware_order_id as &dyn ToSql])
            .await
        {
            Ok(result) => match result.into_first_result().await {
                Ok(rows) => rows,
                Err(e) => {
                    error!("[单据上传] 获取单据状态结果失败: {}", e);
                    return track()
                        .defeat()
                        .message(format!("获取单据状态结果失败: {}", e))
                        .build();
                }
            },
            Err(e) => {
                error!("[单据上传] 查询单据状态失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("查询单据状态失败: {}", e))
                    .build();
            }
        };

        if ware_order_rows.is_empty() {
            error!("[单据上传] 该订单状态错误，订单ID: {}", ware_order_id);
            return track().defeat().message("该订单状态错误").build();
        }

        // 处理结果追踪
        let mut error_messages: Vec<serde_json::Value> = Vec::new();

        // 处理码信息 - 预先验证所有码
        let codes_array = match codes.as_array() {
            Some(array) => array,
            None => {
                error!("[单据上传] codes参数格式错误，应为数组");
                return track()
                    .defeat()
                    .message("codes参数格式错误，应为数组")
                    .build();
            }
        };

        // 先获取单据编号，这在事务外执行
        let ware_code = match invoice_service::get_ware_code(&mut check_conn, ware_order_id).await {
            Ok(code) => code,
            Err(e) => {
                error!("[单据上传] 获取单据编号失败: {}", e);
                return track()
                    .defeat()
                    .message(format!("获取单据编号失败: {}", e))
                    .build();
            }
        };

        // 预处理所有码，验证是否有效
        let mut valid_codes = Vec::new();
        for code_item in codes_array {
            let code = match code_item.get("code").and_then(|c| c.as_str()) {
                Some(c) => c.to_string(),
                None => {
                    if let Some(c) = code_item.as_str() {
                        c.to_string()
                    } else {
                        error!("[单据上传] 码信息中缺少code字段");
                        error_messages.push(serde_json::json!({
                            "code": "",
                            "message": "码信息中缺少code字段"
                        }));
                        continue;
                    }
                }
            };

            info!("[单据上传] 预验证码: {}", code);

            // 检查是否重复码
            if let Err(e) =
                invoice_service::check_duplicate_code(&mut check_conn, ware_order_id, &code).await
            {
                error!("[单据上传] 检查重复码失败: {}, code: {}", e, code);
                error_messages.push(serde_json::json!({
                    "code": code,
                    "message": e
                }));
                continue;
            }

            // 查询码信息和验证码有效性
            match invoice_service::validate_code(&mut check_conn, &code).await {
                Ok(code_info) => {
                    let (level_code, order_id, batch_no, po_number, amount, level_name, product_id) =
                        code_info;

                    // 检查出库明细
                    match invoice_service::check_order_detail(&mut check_conn, order_id, &batch_no)
                        .await
                    {
                        Ok(detail_id) => {
                            // 收集有效的码信息
                            valid_codes.push((
                                code.clone(),
                                level_code,
                                order_id,
                                batch_no,
                                po_number,
                                amount,
                                level_name,
                                product_id,
                                detail_id,
                            ));
                        }
                        Err(e) => {
                            error!("[单据上传] 检查出库明细失败: {}, code: {}", e, code);
                            error_messages.push(serde_json::json!({
                                "code": code,
                                "message": e
                            }));
                        }
                    }
                }
                Err(e) => {
                    error!("[单据上传] 验证码失败: {}, code: {}", e, code);
                    error_messages.push(serde_json::json!({
                        "code": code,
                        "message": e
                    }));
                }
            }
        }

        // 如果预验证阶段有错误，记录错误并直接返回
        if !error_messages.is_empty() {
            info!("[单据上传] 预验证发现错误: {:?}", error_messages);
            error!("[单据上传] 预验证阶段发现错误，不执行事务");

            // 记录失败日志
            if let Ok(mut log_client) = db.get_connection().await {
                let error_response = serde_json::to_string(&serde_json::json!({
                    "success": false,
                    "message": "上传失败",
                    "errorMessages": error_messages
                }))
                .unwrap_or_default();

                let _ = log_client.query(
                    "INSERT INTO wms_api_log (requestLog, responseLog, api, ip, result, type) VALUES (@P1, @P2, @P3, @P4, @P5, @P6)",
                    &[
                        &request_log as &dyn ToSql,
                        &error_response as &dyn ToSql,
                        &"api/produce/order/ware" as &dyn ToSql,
                        &client_ip as &dyn ToSql,
                        &"失败" as &dyn ToSql,
                        &"2" as &dyn ToSql,
                    ],
                ).await;
            }

            return track()
                .defeat()
                .message("上传失败，码验证不通过")
                .data(serde_json::json!({
                    "errorMessages": error_messages
                }))
                .build();
        }

        // 如果没有有效的码，直接返回
        if valid_codes.is_empty() {
            error!("[单据上传] 没有有效的码可处理");
            return track().defeat().message("没有有效的码可处理").build();
        }

        // 开始事务处理
        let mut client = conn;
        info!("[单据上传] 开始处理事务");

        let mut insert_codes: Vec<String> = Vec::new();
        let mut transaction_success = true;
        let mut error_msg = String::new();

        // 开始事务
        if let Err(e) = client.simple_query("BEGIN TRANSACTION").await {
            error!("[单据上传] 开始事务失败: {}", e);
            transaction_success = false;
            error_msg = format!("开始事务失败: {}", e);
        }

        // 获取当前时间
        let current_time = chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string();

        // 如果事务开始成功，处理所有码
        if transaction_success {
            for code_data in &valid_codes {
                let (
                    code,
                    level_code,
                    order_id,
                    batch_no,
                    po_number,
                    amount,
                    level_name,
                    product_id,
                    detail_id,
                ) = code_data;

                // 插入扫码记录
                if let Err(e) = invoice_service::insert_ware_code(
                    &mut client,
                    &ware_code,
                    code,
                    *level_code,
                    level_name,
                    1, // typeFlag 默认为1
                    *product_id,
                    *order_id,
                    batch_no,
                    po_number,
                    &current_time,
                    ware_order_id,
                )
                .await
                {
                    error!("[单据上传] 插入扫码记录失败: {}, code: {}", e, code);
                    transaction_success = false;
                    error_msg = e;
                    break;
                }

                // 更新出库单明细
                if let Err(e) =
                    invoice_service::update_order_detail(&mut client, *amount, *detail_id).await
                {
                    error!(
                        "[单据上传] 更新出库单明细失败: {}, detail_id: {}",
                        e, detail_id
                    );
                    transaction_success = false;
                    error_msg = e;
                    break;
                }

                // 记录已处理的码
                insert_codes.push(code.clone());
            }

            // 如果所有码处理成功，更新订单状态
            if transaction_success && !insert_codes.is_empty() {
                if let Err(e) =
                    invoice_service::update_order_status(&mut client, channel, ware_order_id).await
                {
                    error!("[单据上传] 更新订单状态失败: {}", e);
                    transaction_success = false;
                    error_msg = e;
                }
            }

            // 根据处理结果，提交或回滚事务
            if transaction_success {
                if let Err(e) = client.simple_query("COMMIT TRANSACTION").await {
                    error!("[单据上传] 提交事务失败: {}", e);
                    transaction_success = false;
                    error_msg = format!("提交事务失败: {}", e);
                    insert_codes.clear(); // 提交失败，清空已处理码
                } else {
                    info!("[单据上传] 事务提交成功");
                }
            } else {
                // 回滚事务
                if let Err(e) = client.simple_query("ROLLBACK TRANSACTION").await {
                    error!("[单据上传] 回滚事务失败: {}", e);
                    error_msg = format!("{}, 回滚事务失败: {}", error_msg, e);
                } else {
                    info!("[单据上传] 事务回滚成功");
                }
                insert_codes.clear(); // 回滚事务，清空已处理码
            }
        }

        // 记录响应日志
        let response_log = serde_json::to_string(&serde_json::json!({
            "success": transaction_success,
            "message": error_msg,
            "insertCodes": insert_codes,
            "errorMessages": error_messages
        }))
        .unwrap_or_default();

        // 使用新的连接记录响应日志
        if let Ok(mut log_client) = db.get_connection().await {
            let _ = log_client.query(
                "INSERT INTO wms_api_log (requestLog, responseLog, api, ip, result, type) VALUES (@P1, @P2, @P3, @P4, @P5, @P6)",
                &[
                    &request_log as &dyn ToSql,
                    &response_log as &dyn ToSql,
                    &"api/produce/order/ware" as &dyn ToSql,
                    &client_ip as &dyn ToSql,
                    &if transaction_success { "成功" } else { "失败" } as &dyn ToSql,
                    &"1" as &dyn ToSql,
                ],
            ).await;
        }

        info!(
            "[单据上传] 单据在线上传完成，成功状态: {}",
            transaction_success
        );

        if transaction_success {
            track().victory().message("上传成功").build()
        } else {
            track().defeat().message(error_msg).build()
        }
    }
}
