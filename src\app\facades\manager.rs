use once_cell::sync::OnceCell;
use std::any::Any;
use std::sync::{Arc, Mutex};

/// Facade 特征
pub trait Facade: Send + Sync {
    /// 获取 Facade 实例
    fn get_instance() -> Arc<dyn Any + Send + Sync>;
}

/// Facade 管理器
pub struct FacadeManager {
    instances: std::collections::HashMap<std::any::TypeId, Arc<dyn Any + Send + Sync>>,
}

impl FacadeManager {
    /// 创建新的 Facade 管理器
    pub fn new() -> Self {
        Self {
            instances: std::collections::HashMap::new(),
        }
    }

    /// 注册 Facade 实例
    pub fn register<T: 'static + Send + Sync>(&mut self, instance: T) {
        let type_id = std::any::TypeId::of::<T>();
        self.instances.insert(type_id, Arc::new(instance));
    }

    /// 获取 Facade 实例
    pub fn get<T: 'static + Send + Sync>(&self) -> Option<Arc<T>> {
        let type_id = std::any::TypeId::of::<T>();
        self.instances
            .get(&type_id)
            .and_then(|instance| instance.clone().downcast::<T>().ok())
    }
}

/// 全局 Facade 管理器
static FACADE_MANAGER: OnceCell<Mutex<FacadeManager>> = OnceCell::new();

/// 初始化 Facade 管理器
pub fn init_facade_manager() {
    FACADE_MANAGER.get_or_init(|| Mutex::new(FacadeManager::new()));
}

/// 获取 Facade 管理器
pub fn get_facade_manager() -> &'static Mutex<FacadeManager> {
    FACADE_MANAGER.get().expect("Facade 管理器未初始化")
}
