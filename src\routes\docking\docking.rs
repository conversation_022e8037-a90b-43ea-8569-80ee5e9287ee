use crate::app::http::controllers::docking::DockingController;
use crate::app::providers::DatabaseServiceProvider;
use axum::{
    routing::{get, post},
    Router,
};
use std::sync::Arc;

/// 对接管理路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().merge(
        Router::new()
            .route("/network", get(DockingController::network))
            .route("/sync", get(DockingController::sync))
            .route("/rise", get(DockingController::rise))
            .route("/print", post(DockingController::print)),
    )
}
