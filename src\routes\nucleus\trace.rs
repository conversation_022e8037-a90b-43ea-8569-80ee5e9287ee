use crate::app::http::controllers::nucleus::TraceController;
use crate::app::providers::DatabaseServiceProvider;
use axum::{routing::get, Router};
use std::sync::Arc;

/// 追溯管理路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().merge(
        Router::new()
            .route("/trace/relation", get(TraceController::relation))
            .route("/trace/lazy", get(TraceController::children))
            .route("/trace/independent", get(TraceController::independent)),
    )
}
