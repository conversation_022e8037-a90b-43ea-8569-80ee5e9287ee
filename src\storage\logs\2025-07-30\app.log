2025-07-30 06:55:28.721  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-30 06:55:28.723  INFO 数据库连接管理器创建成功
2025-07-30 06:55:28.759  INFO 数据库连接池创建成功
2025-07-30 06:55:28.768  INFO 服务器运行在 0.0.0.0:3000
2025-07-30 06:55:31.366  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-30 06:55:31.369  INFO 数据库连接管理器创建成功
2025-07-30 06:55:31.398  INFO 数据库连接池创建成功
2025-07-30 06:55:31.405  INFO 服务器运行在 0.0.0.0:3000
2025-07-30 06:55:41.921  INFO [网络检测] 网络连接完成，耗时: 38ms
2025-07-30 07:20:12.809  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:20:12.821  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:20:12.838  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:20:12.845  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:20:12.846  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-30 07:20:12.963  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-30 07:20:12.964  INFO [查询序列码] 查询序列码信息完成: code=015034354734703621100000045721, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:20:19.181  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:20:19.194  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:20:19.199  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:20:19.202  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:20:19.202  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-30 07:20:19.203  INFO [查询序列码] 瓶码不需要查询数量
2025-07-30 07:20:19.204  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356750, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:20:22.161  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:20:22.172  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:20:22.177  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:20:22.179  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:20:22.180  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-30 07:20:22.180  INFO [查询序列码] 瓶码不需要查询数量
2025-07-30 07:20:22.181  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356825, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:20:24.180  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:20:24.191  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:20:24.194  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:20:24.197  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:20:24.197  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-30 07:20:24.198  INFO [查询序列码] 瓶码不需要查询数量
2025-07-30 07:20:24.199  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356858, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:28:11.948  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:28:11.967  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:28:11.970  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:28:11.972  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:28:11.975  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-30 07:28:12.098  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-30 07:28:12.099  INFO [查询序列码] 查询序列码信息完成: code=015034354734703621100000045721, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:29:29.007  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:29:29.018  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:29:29.021  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:29:29.022  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:29:29.024  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-30 07:29:29.156  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-30 07:29:29.157  INFO [查询序列码] 查询序列码信息完成: code=015034354734703621100000045721, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:29:52.903  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:29:52.915  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:29:52.918  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:29:52.920  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:29:52.920  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-30 07:29:53.038  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-30 07:29:53.039  INFO [查询序列码] 查询序列码信息完成: code=015034354734703621100000045721, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:30:25.114  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:30:25.126  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:30:25.129  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:30:25.131  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:30:25.131  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-30 07:30:25.248  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-30 07:30:25.249  INFO [查询序列码] 查询序列码信息完成: code=015034354734703621100000045721, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:30:28.142  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:30:28.154  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:30:28.156  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:30:28.159  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:30:28.160  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-30 07:30:28.161  INFO [查询序列码] 瓶码不需要查询数量
2025-07-30 07:30:28.162  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356750, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:34:19.583  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:34:19.597  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:34:19.600  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:34:19.602  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:34:19.603  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-30 07:34:19.719  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-30 07:34:19.720  INFO [查询序列码] 查询序列码信息完成: code=015034354734703621100000045721, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:34:25.269  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:34:25.284  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:34:25.287  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:34:25.290  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:34:25.290  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-30 07:34:25.292  INFO [查询序列码] 瓶码不需要查询数量
2025-07-30 07:34:25.293  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356750, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:37:12.623  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:37:12.639  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:37:12.643  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:37:12.645  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:37:12.645  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-30 07:37:12.815  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-30 07:37:12.816  INFO [查询序列码] 查询序列码信息完成: code=015034354734703621100000045721, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:38:38.382  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:38:38.393  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:38:38.396  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:38:38.398  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:38:38.398  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-30 07:38:38.399  INFO [查询序列码] 瓶码不需要查询数量
2025-07-30 07:38:38.400  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356750, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:38:41.268  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:38:41.281  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:38:41.284  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:38:41.286  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:38:41.286  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-30 07:38:41.410  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-30 07:38:41.411  INFO [查询序列码] 查询序列码信息完成: code=015034354734703621100000045721, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:38:45.623  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:38:45.636  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:38:45.639  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:38:45.641  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:38:45.641  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-30 07:38:45.642  INFO [查询序列码] 瓶码不需要查询数量
2025-07-30 07:38:45.643  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356750, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:38:51.155  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:38:51.167  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:38:51.170  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:38:51.172  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:38:51.173  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-30 07:38:51.173  INFO [查询序列码] 瓶码不需要查询数量
2025-07-30 07:38:51.174  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356825, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:38:56.662  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:38:56.673  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:38:56.676  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:38:56.678  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:38:56.678  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-30 07:38:56.679  INFO [查询序列码] 瓶码不需要查询数量
2025-07-30 07:38:56.680  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356858, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:39:16.299  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:39:16.314  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:39:16.317  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:39:16.319  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:39:16.319  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-30 07:39:16.435  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-30 07:39:16.436  INFO [查询序列码] 查询序列码信息完成: code=015034354734703621100000045721, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:39:19.428  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:39:19.440  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:39:19.444  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:39:19.446  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:39:19.446  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-30 07:39:19.447  INFO [查询序列码] 瓶码不需要查询数量
2025-07-30 07:39:19.448  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356750, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:39:30.154  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:39:30.166  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:39:30.169  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:39:30.171  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:39:30.171  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-30 07:39:30.172  INFO [查询序列码] 瓶码不需要查询数量
2025-07-30 07:39:30.173  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356825, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:39:33.764  INFO [查询序列码] 开始查询序列码信息
2025-07-30 07:39:33.774  INFO [查询序列码] 数据库连接获取成功
2025-07-30 07:39:33.777  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-30 07:39:33.779  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-30 07:39:33.780  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-30 07:39:33.780  INFO [查询序列码] 瓶码不需要查询数量
2025-07-30 07:39:33.781  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356858, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-30 07:39:39.714  INFO [拆箱] 开始拆箱操作流程
2025-07-30 07:39:39.715  INFO [拆箱] 接收到请求参数: case=015034354734703621100000045721, codes=["010034354734703121100002356750", "010034354734703121100002356825", "010034354734703121100002356858"]
2025-07-30 07:39:39.718  INFO [拆箱] 发送HTTP请求到拆箱接口: {"case":"015034354734703621100000045721","codes":["010034354734703121100002356750","010034354734703121100002356825","010034354734703121100002356858"]}
2025-07-30 07:39:39.719 DEBUG starting new connection: http://localhost:8000/    
2025-07-30 07:39:39.719 DEBUG proxy(http://127.0.0.1:7890) intercepts 'http://localhost:8000/'    
2025-07-30 07:39:39.720 DEBUG connecting to 127.0.0.1:7890
2025-07-30 07:39:39.721 DEBUG connected to 127.0.0.1:7890
2025-07-30 07:39:39.723 DEBUG flushed 305 bytes
2025-07-30 07:39:39.980 DEBUG parsed 2 headers
2025-07-30 07:39:39.980 DEBUG incoming body is empty
2025-07-30 07:39:39.981 ERROR [拆箱] 解析响应数据失败: error decoding response body: EOF while parsing a value at line 1 column 0
2025-07-30 07:39:43.727  INFO [拆箱] 开始拆箱操作流程
2025-07-30 07:39:43.728  INFO [拆箱] 接收到请求参数: case=015034354734703621100000045721, codes=["010034354734703121100002356750", "010034354734703121100002356825", "010034354734703121100002356858"]
2025-07-30 07:39:43.729  INFO [拆箱] 发送HTTP请求到拆箱接口: {"case":"015034354734703621100000045721","codes":["010034354734703121100002356750","010034354734703121100002356825","010034354734703121100002356858"]}
2025-07-30 07:39:43.730 DEBUG starting new connection: http://localhost:8000/    
2025-07-30 07:39:43.730 DEBUG proxy(http://127.0.0.1:7890) intercepts 'http://localhost:8000/'    
2025-07-30 07:39:43.731 DEBUG connecting to 127.0.0.1:7890
2025-07-30 07:39:43.732 DEBUG connected to 127.0.0.1:7890
2025-07-30 07:39:43.733 DEBUG flushed 305 bytes
2025-07-30 07:39:43.827 DEBUG parsed 2 headers
2025-07-30 07:39:43.829 DEBUG incoming body is empty
2025-07-30 07:39:43.831 ERROR [拆箱] 解析响应数据失败: error decoding response body: EOF while parsing a value at line 1 column 0
