use serde::Deserialize;
use serde::Serialize;
use std::collections::HashMap;

/// 验证规则特征
///
/// 用于定义具体的验证规则，如必填、长度限制、格式验证等。
///
/// # 用法示例
/// ```
/// struct Required;
///
/// impl ValidationRule for Required {
///     fn name(&self) -> &'static str {
///         "required"
///     }
///     
///     fn validate(&self, value: &str, _params: &[&str]) -> bool {
///         !value.is_empty()
///     }
///     
///     fn message(&self) -> &str {
///         "字段不能为空"
///     }
/// }
/// ```
pub trait ValidationRule {
    /// 验证规则名称
    ///
    /// 返回规则的唯一标识符，如"required"、"email"、"max"等
    fn name(&self) -> &'static str;

    /// 验证规则实现
    ///
    /// # 参数
    /// * `value` - 要验证的值
    /// * `params` - 规则参数，如长度限制中的最大值、最小值等
    ///
    /// # 返回
    /// * `true` - 验证通过
    /// * `false` - 验证失败
    fn validate(&self, value: &str, params: &[&str]) -> bool;

    /// 错误消息
    ///
    /// 返回验证失败时的错误提示信息
    fn message(&self) -> &str;
}

/// 验证器特征
///
/// 用于组织和执行一组验证规则，支持场景管理和自定义错误信息。
///
/// # 用法示例
/// ```
/// struct UserValidator {
///     scene: Option<String>,
/// }
///
/// impl Validator for UserValidator {
///     fn scene(&self) -> Option<String> {
///         self.scene.clone()
///     }
///     
///     fn set_scene(&mut self, scene: &str) {
///         self.scene = Some(scene.to_string());
///     }
///     
///     fn rules(&self) -> HashMap<String, Vec<Box<dyn ValidationRule>>> {
///         let mut rules = HashMap::new();
///         rules.insert("username".to_string(), vec![Box::new(Required), Box::new(Length::new(3, 20))]);
///         rules.insert("email".to_string(), vec![Box::new(Required), Box::new(Email)]);
///         rules
///     }
///     
///     fn scenarios(&self) -> HashMap<String, Vec<String>> {
///         let mut scenes = HashMap::new();
///         scenes.insert("register".to_string(), vec!["username".to_string(), "email".to_string()]);
///         scenes.insert("login".to_string(), vec!["username".to_string()]);
///         scenes
///     }
///     
///     fn validate<T: 'static + std::any::Any>(&self, target: &T) -> Result<(), Vec<String>> {
///         // 实现验证逻辑
///     }
/// }
/// ```
pub trait Validator {
    /// 获取当前验证场景
    ///
    /// 返回当前设置的验证场景，如"register"、"login"等
    fn scene(&self) -> Option<String>;

    /// 设置验证场景
    ///
    /// 设置当前验证器的场景，用于控制哪些字段需要验证
    ///
    /// # 参数
    /// * `scene` - 场景名称
    fn set_scene(&mut self, scene: &str);

    /// 获取验证规则
    ///
    /// 返回字段名到验证规则列表的映射关系
    fn rules(&self) -> HashMap<String, Vec<Box<dyn ValidationRule>>>;

    /// 获取场景定义
    ///
    /// 返回场景名到字段列表的映射关系，用于控制不同场景下验证哪些字段
    /// 默认返回空映射，表示验证所有字段
    fn scenarios(&self) -> HashMap<String, Vec<String>> {
        HashMap::new()
    }

    /// 获取自定义错误消息
    ///
    /// 返回字段规则到自定义错误消息的映射关系
    /// 格式为: {"field.rule": "自定义错误消息"}
    /// 例如: {"username.required": "用户名不能为空"}
    fn messages(&self) -> HashMap<String, String> {
        HashMap::new()
    }

    /// 获取自定义属性名称
    ///
    /// 返回字段到自定义显示名称的映射关系
    /// 例如: {"email": "电子邮箱地址"}
    fn attributes(&self) -> HashMap<String, String> {
        HashMap::new()
    }

    /// 验证数据
    ///
    /// 对目标数据进行验证，根据当前场景和规则执行验证
    ///
    /// # 参数
    /// * `target` - 要验证的数据对象
    ///
    /// # 返回
    /// * `Ok(())` - 验证通过
    /// * `Err(Vec<String>)` - 验证失败，包含错误信息列表
    fn validate<T: 'static + std::any::Any>(&self, target: &T) -> Result<(), Vec<String>>;
}

/// 验证错误
///
/// 用于收集和管理验证过程中产生的错误信息
///
/// # 用法示例
/// ```
/// let mut errors = ValidationError::new();
/// errors.add("username", "用户名不能为空");
/// errors.add("email", "邮箱格式不正确");
///
/// if errors.has_errors() {
///     println!("验证失败: {:?}", errors.all());
/// }
/// ```
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationError {
    /// 错误信息，格式为 {字段名: [错误消息列表]}
    pub errors: HashMap<String, Vec<String>>,
}

impl ValidationError {
    /// 创建新的验证错误
    ///
    /// 返回一个空的验证错误对象
    pub fn new() -> Self {
        Self {
            errors: HashMap::new(),
        }
    }

    /// 添加错误信息
    ///
    /// # 参数
    /// * `field` - 字段名
    /// * `message` - 错误消息
    ///
    /// # 返回
    /// 返回自身，支持链式调用
    pub fn add(&mut self, field: &str, message: &str) -> Self {
        self.errors
            .entry(field.to_string())
            .or_insert_with(Vec::new)
            .push(message.to_string());
        self.clone()
    }

    /// 检查是否有错误
    ///
    /// # 返回
    /// * `true` - 存在错误
    /// * `false` - 无错误
    pub fn has_errors(&self) -> bool {
        !self.errors.is_empty()
    }

    /// 获取所有错误
    ///
    /// # 返回
    /// 所有字段的错误信息映射
    pub fn all(&self) -> &HashMap<String, Vec<String>> {
        &self.errors
    }

    /// 获取指定字段的错误
    ///
    /// # 参数
    /// * `field` - 字段名
    ///
    /// # 返回
    /// 指定字段的错误消息列表，如果字段没有错误则返回 None
    pub fn get(&self, field: &str) -> Option<&Vec<String>> {
        self.errors.get(field)
    }

    /// 获取第一个错误
    ///
    /// # 返回
    /// 第一个字段的第一个错误消息，如果没有错误则返回 None
    pub fn first(&self) -> Option<&String> {
        self.errors.values().next().and_then(|v| v.first())
    }
}
