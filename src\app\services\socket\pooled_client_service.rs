use crate::{
    app::facades::Socket as SocketFacade,
    app::services::socket::client_service::{SocketClientConfig, SocketClientError},
    config::SocketConfig,
};
use std::time::Duration;
use tokio::{
    io::{AsyncReadExt, AsyncWriteExt},
    net::TcpStream,
    time,
};
use tracing::{debug, error, info, instrument};

/// 基于连接池的Socket客户端服务
///
/// 这个服务专门设计用于连接池模式，提供更高效的连接管理和更好的并发性能。
/// 与传统的单连接模式相比，连接池模式具有以下优势：
///
/// 1. **连接复用**: 避免频繁建立和关闭连接的开销
/// 2. **并发支持**: 支持多个并发请求同时使用不同的连接
/// 3. **自动管理**: 连接的创建、维护和销毁由连接池自动处理
/// 4. **故障恢复**: 连接池会自动检测和替换失效的连接
/// 5. **资源控制**: 通过连接池大小限制资源使用
#[derive(Debug, Clone)]
pub struct PooledSocketClientService {
    /// 客户端配置
    config: SocketClientConfig,
}

impl PooledSocketClientService {
    /// 创建新的连接池Socket客户端实例
    pub fn new(config: SocketClientConfig) -> Self {
        Self { config }
    }

    /// 使用默认配置创建连接池Socket客户端实例
    pub fn default() -> Self {
        Self::new(SocketClientConfig::default())
    }

    /// 从环境配置创建连接池Socket客户端实例
    pub fn from_env() -> Self {
        Self::default()
    }

    /// 发送数据并接收响应
    ///
    /// 这是主要的通信方法，使用连接池获取连接并执行完整的发送-接收操作。
    /// 连接会在操作完成后自动归还给连接池。
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    pub async fn send_and_receive(&self, data: &[u8]) -> Result<Vec<u8>, SocketClientError> {
        info!("开始发送数据并接收响应，数据长度: {} 字节", data.len());

        let config = self.config.clone();
        let data = data.to_vec(); // 克隆数据以避免生命周期问题

        // 获取连接池
        let pool = SocketFacade::get_pool().await;
        let mut conn = pool.get().await.map_err(|e| {
            error!("获取连接失败: {:?}", e);
            SocketClientError::ConnectionError(format!("获取连接失败: {:?}", e))
        })?;

        // 发送数据
        debug!("发送数据到服务器");
        conn.write_all(&data).await.map_err(|e| {
            error!("发送数据失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        // 确保数据发送完成
        conn.flush().await.map_err(|e| {
            error!("刷新发送缓冲区失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        debug!("数据发送完成，开始接收响应");

        // 接收响应
        Self::read_response_static(&mut conn, &config).await
    }

    /// 只发送数据，不等待响应
    ///
    /// 适用于单向通信场景，如发送通知、日志等。
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    pub async fn send_only(&self, data: &[u8]) -> Result<(), SocketClientError> {
        info!("发送数据，数据长度: {} 字节", data.len());

        let data = data.to_vec(); // 克隆数据以避免生命周期问题

        // 获取连接池
        let pool = SocketFacade::get_pool().await;
        let mut conn = pool.get().await.map_err(|e| {
            error!("获取连接失败: {:?}", e);
            SocketClientError::ConnectionError(format!("获取连接失败: {:?}", e))
        })?;

        // 发送数据
        conn.write_all(&data).await.map_err(|e| {
            error!("发送数据失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        // 确保数据发送完成
        conn.flush().await.map_err(|e| {
            error!("刷新发送缓冲区失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        debug!("数据发送完成");
        Ok(())
    }

    /// 批量发送数据
    ///
    /// 使用单个连接发送多个数据包，提高效率。
    /// 适用于需要发送大量小数据包的场景。
    #[instrument(skip(self, data_list), fields(batch_size = data_list.len()))]
    pub async fn send_batch(&self, data_list: &[&[u8]]) -> Result<Vec<Vec<u8>>, SocketClientError> {
        info!("批量发送数据，批次大小: {}", data_list.len());

        if data_list.is_empty() {
            return Ok(Vec::new());
        }

        let config = self.config.clone();
        let data_list: Vec<Vec<u8>> = data_list.iter().map(|data| data.to_vec()).collect(); // 克隆数据以避免生命周期问题

        // 获取连接池
        let pool = SocketFacade::get_pool().await;
        let mut conn = pool.get().await.map_err(|e| {
            error!("获取连接失败: {:?}", e);
            SocketClientError::ConnectionError(format!("获取连接失败: {:?}", e))
        })?;

        let mut responses = Vec::with_capacity(data_list.len());

        for (index, data) in data_list.iter().enumerate() {
            debug!("发送第 {} 个数据包，长度: {} 字节", index + 1, data.len());

            // 发送数据
            conn.write_all(data).await.map_err(|e| {
                error!("发送第 {} 个数据包失败: {}", index + 1, e);
                SocketClientError::SendError(e.to_string())
            })?;

            // 确保数据发送完成
            conn.flush().await.map_err(|e| {
                error!("刷新发送缓冲区失败: {}", e);
                SocketClientError::SendError(e.to_string())
            })?;

            // 接收响应
            let response = Self::read_response_static(&mut conn, &config).await?;
            responses.push(response);

            debug!("第 {} 个数据包处理完成", index + 1);
        }

        Ok(responses)
    }

    /// 从TCP流中读取响应数据
    #[instrument(skip(self, stream))]
    async fn read_response(&self, stream: &mut TcpStream) -> Result<Vec<u8>, SocketClientError> {
        // 预分配合理大小的缓冲区，避免频繁扩容
        let mut buffer = Vec::with_capacity(self.config.read_buffer_size);
        let mut temp_buffer = vec![0u8; self.config.read_buffer_size];

        // 如果设置了读取超时
        if let Some(timeout_ms) = self.config.read_timeout_ms {
            let timeout = Duration::from_millis(timeout_ms);
            return self
                .read_with_timeout(stream, &mut buffer, &mut temp_buffer, timeout)
                .await;
        }

        // 无超时读取
        loop {
            match stream.read(&mut temp_buffer).await {
                Ok(0) => break, // 连接关闭
                Ok(n) => {
                    buffer.extend_from_slice(&temp_buffer[..n]);
                    // 检查是否接收完成（根据协议确定）
                    if n < temp_buffer.len() {
                        break;
                    }
                }
                Err(e) => {
                    error!("接收数据失败: {}", e);
                    return Err(SocketClientError::from(e));
                }
            }
        }

        debug!("接收到响应数据，长度: {} 字节", buffer.len());
        Ok(buffer)
    }

    /// 带超时的读取操作
    #[instrument(skip(self, stream, buffer, temp_buffer))]
    async fn read_with_timeout(
        &self,
        stream: &mut TcpStream,
        buffer: &mut Vec<u8>,
        temp_buffer: &mut [u8],
        timeout: Duration,
    ) -> Result<Vec<u8>, SocketClientError> {
        loop {
            match time::timeout(timeout, stream.read(temp_buffer)).await {
                Ok(Ok(0)) => break, // 连接关闭
                Ok(Ok(n)) => {
                    buffer.extend_from_slice(&temp_buffer[..n]);
                    // 检查是否接收完成
                    if n < temp_buffer.len() {
                        break;
                    }
                }
                Ok(Err(e)) => {
                    error!("接收数据失败: {}", e);
                    return Err(SocketClientError::from(e));
                }
                Err(_) => {
                    error!("读取超时");
                    return Err(SocketClientError::ReceiveError("读取超时".to_string()));
                }
            }
        }

        debug!("接收到响应数据，长度: {} 字节", buffer.len());
        // 避免不必要的克隆，直接返回buffer
        Ok(std::mem::take(buffer))
    }

    /// 静态方法：从TCP流中读取响应数据
    #[instrument(skip(stream, config))]
    async fn read_response_static(
        stream: &mut TcpStream,
        config: &SocketClientConfig,
    ) -> Result<Vec<u8>, SocketClientError> {
        // 预分配合理大小的缓冲区，避免频繁扩容
        let mut buffer = Vec::with_capacity(config.read_buffer_size);
        let mut temp_buffer = vec![0u8; config.read_buffer_size];

        // 如果设置了读取超时
        if let Some(timeout_ms) = config.read_timeout_ms {
            let timeout = Duration::from_millis(timeout_ms);
            return Self::read_with_timeout_static(stream, &mut buffer, &mut temp_buffer, timeout)
                .await;
        }

        // 无超时读取
        loop {
            match stream.read(&mut temp_buffer).await {
                Ok(0) => break, // 连接关闭
                Ok(n) => {
                    buffer.extend_from_slice(&temp_buffer[..n]);
                    // 检查是否接收完成（根据协议确定）
                    if n < temp_buffer.len() {
                        break;
                    }
                }
                Err(e) => {
                    error!("接收数据失败: {}", e);
                    return Err(SocketClientError::from(e));
                }
            }
        }

        debug!("接收到响应数据，长度: {} 字节", buffer.len());
        Ok(buffer)
    }

    /// 静态方法：带超时的读取操作
    #[instrument(skip(stream, buffer, temp_buffer))]
    async fn read_with_timeout_static(
        stream: &mut TcpStream,
        buffer: &mut Vec<u8>,
        temp_buffer: &mut [u8],
        timeout: Duration,
    ) -> Result<Vec<u8>, SocketClientError> {
        loop {
            match time::timeout(timeout, stream.read(temp_buffer)).await {
                Ok(Ok(0)) => break, // 连接关闭
                Ok(Ok(n)) => {
                    buffer.extend_from_slice(&temp_buffer[..n]);
                    // 检查是否接收完成
                    if n < temp_buffer.len() {
                        break;
                    }
                }
                Ok(Err(e)) => {
                    error!("接收数据失败: {}", e);
                    return Err(SocketClientError::from(e));
                }
                Err(_) => {
                    error!("读取超时");
                    return Err(SocketClientError::ReceiveError("读取超时".to_string()));
                }
            }
        }

        debug!("接收到响应数据，长度: {} 字节", buffer.len());
        // 避免不必要的克隆，直接返回buffer
        Ok(std::mem::take(buffer))
    }

    /// 检查连接池是否可用
    ///
    /// 在连接池模式下，这个方法总是返回true，
    /// 因为连接的可用性由连接池管理。
    pub fn is_available(&self) -> bool {
        true
    }

    /// 获取客户端配置
    pub fn config(&self) -> &SocketClientConfig {
        &self.config
    }

    /// 测试连接池连接
    ///
    /// 发送一个简单的测试消息来验证连接池是否正常工作。
    #[instrument(skip(self))]
    pub async fn test_connection(&self) -> Result<(), SocketClientError> {
        info!("测试连接池连接");

        let test_data = b"PING";
        match self.send_and_receive(test_data).await {
            Ok(response) => {
                info!("连接测试成功，响应长度: {} 字节", response.len());
                Ok(())
            }
            Err(e) => {
                error!("连接测试失败: {:?}", e);
                Err(e)
            }
        }
    }
}

/// 连接池客户端的便捷函数
impl PooledSocketClientService {
    /// 创建一个全局可用的连接池客户端实例
    ///
    /// 这个方法返回一个配置好的客户端实例，可以在整个应用中使用。
    pub fn global() -> Self {
        Self::from_env()
    }

    /// 使用自定义配置创建连接池客户端
    pub fn with_config(host: &str, port: u16) -> Self {
        let socket_config = SocketConfig::from_env();
        let config = SocketClientConfig {
            host: host.to_string(),
            port,
            timeout_ms: socket_config.timeout * 1000,
            keep_alive: true,
            keep_alive_interval: socket_config.heartbeat_interval,
            read_timeout_ms: Some(socket_config.timeout * 1000),
            read_buffer_size: socket_config.read_buffer_size,
            write_buffer_size: socket_config.write_buffer_size,
            connection_mode:
                crate::app::services::socket::client_service::SocketConnectionMode::Pool,
        };
        Self::new(config)
    }
}
