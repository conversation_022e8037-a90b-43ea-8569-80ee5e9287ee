use axum::{http::StatusCode, response::IntoResponse, <PERSON><PERSON>};

/// 仓库管理控制器
pub struct WarehouseController;

impl WarehouseController {
    /// 仓库列表
    pub async fn index() -> impl IntoResponse {
        (StatusCode::OK, <PERSON><PERSON>("仓库列表"))
    }

    /// 仓库分页
    pub async fn page() -> impl IntoResponse {
        (StatusCode::OK, <PERSON><PERSON>("仓库分页"))
    }

    /// 添加仓库
    pub async fn save_add() -> impl IntoResponse {
        (StatusCode::OK, <PERSON><PERSON>("添加仓库"))
    }

    /// 编辑仓库
    pub async fn save_edit() -> impl IntoResponse {
        (StatusCode::OK, <PERSON><PERSON>("编辑仓库"))
    }

    /// 删除仓库
    pub async fn delete() -> impl IntoResponse {
        (StatusCode::OK, <PERSON><PERSON>("删除仓库"))
    }

    /// 获取GLN类型
    pub async fn get_gln_type() -> impl IntoResponse {
        (StatusCode::OK, <PERSON><PERSON>("获取GLN类型"))
    }
}
