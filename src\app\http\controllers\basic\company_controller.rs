use axum::response::IntoResponse;

/// 公司管理控制器
pub struct CompanyController;

impl CompanyController {
    /// 分页列表
    pub async fn page() -> impl IntoResponse {
        "page"
    }

    /// 列表
    pub async fn index() -> impl IntoResponse {
        "index"
    }

    /// 详情
    pub async fn show() -> impl IntoResponse {
        "show"
    }

    /// 新增
    pub async fn store() -> impl IntoResponse {
        "store"
    }

    /// 修改
    pub async fn update() -> impl IntoResponse {
        "update"
    }

    /// 删除
    pub async fn destroy() -> impl IntoResponse {
        "destroy"
    }
}
