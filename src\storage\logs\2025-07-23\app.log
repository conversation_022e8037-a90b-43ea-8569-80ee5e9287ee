2025-07-23 09:23:33.476  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 09:23:33.477  INFO 数据库连接管理器创建成功
2025-07-23 09:23:33.517  INFO 数据库连接池创建成功
2025-07-23 09:23:33.522  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 09:23:36.236  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 09:23:36.239  INFO 数据库连接管理器创建成功
2025-07-23 09:23:36.268  INFO 数据库连接池创建成功
2025-07-23 09:23:36.272  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 09:24:18.806  INFO 网络连接完成，耗时: 41ms
2025-07-23 09:39:53.473  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 09:39:53.475  INFO 数据库连接管理器创建成功
2025-07-23 09:39:53.533  INFO 数据库连接池创建成功
2025-07-23 09:39:53.547  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 10:09:47.932  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 10:09:47.935  INFO 数据库连接管理器创建成功
2025-07-23 10:09:48.040  INFO 数据库连接池创建成功
2025-07-23 10:09:48.056  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 10:42:46.874  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 10:42:46.875  INFO 数据库连接管理器创建成功
2025-07-23 10:42:46.913  INFO 数据库连接池创建成功
2025-07-23 10:42:46.920  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 10:45:58.857  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 10:45:58.860  INFO 数据库连接管理器创建成功
2025-07-23 10:45:58.940  INFO 数据库连接池创建成功
2025-07-23 10:45:58.954  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 10:53:07.724  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 10:53:07.725  INFO 数据库连接管理器创建成功
2025-07-23 10:53:07.760  INFO 数据库连接池创建成功
2025-07-23 10:53:07.766  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 10:53:59.679  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 10:53:59.684  INFO 数据库连接管理器创建成功
2025-07-23 10:53:59.743  INFO 数据库连接池创建成功
2025-07-23 10:53:59.750  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 16:29:16.374  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 16:29:16.375  INFO 数据库连接管理器创建成功
2025-07-23 16:29:16.415  INFO 数据库连接池创建成功
2025-07-23 16:29:16.421  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 16:29:23.146  INFO 开始测试Socket: SocketParams { host: None, port: None, message: None }
2025-07-23 16:29:23.147  INFO Socket测试参数: host=127.0.0.1, port=8080
2025-07-23 16:29:23.151  INFO [Socket客户端] 开始连接到服务器: 127.0.0.1:8080
2025-07-23 16:29:25.185 ERROR [Socket客户端] 连接失败: 由于目标计算机积极拒绝，无法连接。 (os error 10061)
2025-07-23 16:29:25.187 ERROR Socket连接失败: ConnectionError("由于目标计算机积极拒绝，无法连接。 (os error 10061)")
2025-07-23 16:29:46.860  INFO 开始测试Socket: SocketParams { host: None, port: None, message: None }
2025-07-23 16:29:46.861  INFO Socket测试参数: host=127.0.0.1, port=8080
2025-07-23 16:29:46.864  INFO [Socket客户端] 开始连接到服务器: 127.0.0.1:8080
2025-07-23 16:29:48.884 ERROR [Socket客户端] 连接失败: 由于目标计算机积极拒绝，无法连接。 (os error 10061)
2025-07-23 16:29:48.884 ERROR Socket连接失败: ConnectionError("由于目标计算机积极拒绝，无法连接。 (os error 10061)")
2025-07-23 16:30:37.245  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 16:30:37.246  INFO 数据库连接管理器创建成功
2025-07-23 16:30:37.289  INFO 数据库连接池创建成功
2025-07-23 16:30:37.295  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 16:30:39.226  INFO 开始测试Socket: SocketParams { host: None, port: None, message: None }
2025-07-23 16:30:39.227  INFO Socket测试参数: host=127.0.0.1, port=7000
2025-07-23 16:30:39.229  INFO [Socket客户端] 开始连接到服务器: 127.0.0.1:7000
2025-07-23 16:30:39.230 DEBUG [Socket客户端] 连接成功: 127.0.0.1:7000
2025-07-23 16:30:39.231  INFO Socket连接成功
2025-07-23 16:30:49.241 ERROR [Socket客户端] 读取超时
2025-07-23 16:30:49.242 ERROR Socket通信失败: ReceiveError("读取超时")
2025-07-23 16:30:49.246  INFO [Socket客户端] 关闭连接
2025-07-23 16:33:42.429  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 16:33:42.430  INFO 数据库连接管理器创建成功
2025-07-23 16:33:42.501  INFO 数据库连接池创建成功
2025-07-23 16:33:42.513  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 16:35:45.639  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 16:35:45.641  INFO 数据库连接管理器创建成功
2025-07-23 16:35:45.683  INFO 数据库连接池创建成功
2025-07-23 16:35:45.690  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 16:35:57.480  INFO 开始测试Socket: SocketParams { host: None, port: None, message: None, print_type: None, print_count: None, use_default: None }
2025-07-23 16:35:57.480  INFO Socket测试参数: host=127.0.0.1, port=7000
2025-07-23 16:35:57.481  INFO 发送数据: {"Message":[{"Node:":"Barcode","Value":"111111"},{"Node:":"Product","Value":"测试产品"}],"PrintCount":"3","PrintType":"1"}
2025-07-23 16:35:57.481  INFO [Socket客户端] 开始连接到服务器: 127.0.0.1:7000
2025-07-23 16:35:57.481 DEBUG [Socket客户端] 连接成功: 127.0.0.1:7000
2025-07-23 16:35:57.482  INFO Socket连接成功
2025-07-23 16:36:07.484 ERROR [Socket客户端] 读取超时
2025-07-23 16:36:07.484 ERROR Socket通信失败: ReceiveError("读取超时")
2025-07-23 16:36:07.485  INFO [Socket客户端] 关闭连接
2025-07-23 16:40:25.052  INFO 开始测试Socket: SocketParams { host: None, port: None, message: None, print_type: None, print_count: None, use_default: None }
2025-07-23 16:40:25.053  INFO Socket测试参数: host=127.0.0.1, port=7000
2025-07-23 16:40:25.055  INFO 发送数据: {"Message":[{"Node:":"Barcode","Value":"111111"},{"Node:":"Product","Value":"测试产品"}],"PrintCount":"3","PrintType":"1"}
2025-07-23 16:40:25.055  INFO [Socket客户端] 开始连接到服务器: 127.0.0.1:7000
2025-07-23 16:40:25.057 DEBUG [Socket客户端] 连接成功: 127.0.0.1:7000
2025-07-23 16:40:25.058  INFO Socket连接成功
2025-07-23 16:40:35.067 ERROR [Socket客户端] 读取超时
2025-07-23 16:40:35.069 ERROR Socket通信失败: ReceiveError("读取超时")
2025-07-23 16:40:35.071  INFO [Socket客户端] 关闭连接
2025-07-23 16:45:36.076  INFO 开始测试Socket: SocketParams { host: None, port: None, message: None, print_type: None, print_count: None, use_default: None }
2025-07-23 16:45:36.077  INFO Socket测试参数: host=127.0.0.1, port=7000
2025-07-23 16:45:36.078  INFO 发送数据: {"Message":[{"Node:":"Barcode","Value":"111111"},{"Node:":"Product","Value":"测试产品"}],"PrintCount":"3","PrintType":"1"}
2025-07-23 16:45:36.078  INFO [Socket客户端] 开始连接到服务器: 127.0.0.1:7000
2025-07-23 16:45:36.080 DEBUG [Socket客户端] 连接成功: 127.0.0.1:7000
2025-07-23 16:45:36.080  INFO Socket连接成功
2025-07-23 16:45:46.094 ERROR [Socket客户端] 读取超时
2025-07-23 16:45:46.094 ERROR Socket通信失败: ReceiveError("读取超时")
2025-07-23 16:45:46.098  INFO [Socket客户端] 关闭连接
2025-07-23 16:51:04.326  INFO [单据统计] 开始获取单据统计信息
2025-07-23 16:51:10.824  INFO [单据统计] 本周日期范围: ["2025-07-21", "2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-26", "2025-07-27"]
2025-07-23 16:51:15.249  INFO [单据统计] 开始获取单据统计信息
2025-07-23 16:51:15.250  INFO [单据统计] 本周日期范围: ["2025-07-21", "2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-26", "2025-07-27"]
2025-07-23 16:51:15.311  INFO [单据统计] 数据库连接获取成功
2025-07-23 16:51:15.330  INFO [单据统计] 执行单据统计查询SQL
2025-07-23 16:51:15.335  INFO [单据统计] 统计查询执行成功
2025-07-23 16:51:15.336  INFO [单据统计] 成功获取单据统计信息
2025-07-23 16:55:29.932  INFO [单据分页] 开始获取单据分页列表
2025-07-23 16:55:29.933  INFO [单据分页] 查询参数: page=1, per_page=10, id=None, ware_code=Some(""), status=Some(10), ware_date=None, channel=None
2025-07-23 16:55:29.944  INFO [单据分页] 数据库连接获取成功
2025-07-23 16:55:29.953  INFO [单据分页] 总记录数为0，直接返回空结果
2025-07-23 16:55:52.947  INFO [产品分页] 开始获取产品分页列表
2025-07-23 16:55:52.948  INFO [产品分页] 查询参数: page=1, per_page=5, product_name=Some(""), product_code=None, ndc=None
2025-07-23 16:55:52.965  INFO [产品分页] 数据库连接获取成功
2025-07-23 16:55:53.012  INFO [产品分页] 查询成功，获取到5条记录
2025-07-23 16:56:00.765  INFO [产品分页] 开始获取产品分页列表
2025-07-23 16:56:00.766  INFO [产品分页] 查询参数: page=2, per_page=5, product_name=Some(""), product_code=None, ndc=None
2025-07-23 16:56:00.779  INFO [产品分页] 数据库连接获取成功
2025-07-23 16:56:00.783  INFO [产品分页] 查询成功，获取到5条记录
2025-07-23 16:56:02.797  INFO [产品分页] 开始获取产品分页列表
2025-07-23 16:56:02.798  INFO [产品分页] 查询参数: page=3, per_page=5, product_name=Some(""), product_code=None, ndc=None
2025-07-23 16:56:02.811  INFO [产品分页] 数据库连接获取成功
2025-07-23 16:56:02.817  INFO [产品分页] 查询成功，获取到5条记录
2025-07-23 16:56:05.272  INFO [产品分页] 开始获取产品分页列表
2025-07-23 16:56:05.272  INFO [产品分页] 查询参数: page=4, per_page=5, product_name=Some(""), product_code=None, ndc=None
2025-07-23 16:56:05.284  INFO [产品分页] 数据库连接获取成功
2025-07-23 16:56:05.302  INFO [产品分页] 查询成功，获取到5条记录
2025-07-23 16:56:06.586  INFO [产品分页] 开始获取产品分页列表
2025-07-23 16:56:06.587  INFO [产品分页] 查询参数: page=5, per_page=5, product_name=Some(""), product_code=None, ndc=None
2025-07-23 16:56:06.606  INFO [产品分页] 数据库连接获取成功
2025-07-23 16:56:06.629  INFO [产品分页] 查询成功，获取到4条记录
2025-07-23 16:56:12.549  INFO [单据分页] 开始获取单据分页列表
2025-07-23 16:56:12.550  INFO [单据分页] 查询参数: page=1, per_page=5, id=None, ware_code=Some(""), status=None, ware_date=None, channel=None
2025-07-23 16:56:12.564  INFO [单据分页] 数据库连接获取成功
2025-07-23 16:56:12.595  INFO [单据分页] 查询成功，获取到5条记录
2025-07-23 16:56:19.749  INFO [订单分页] 开始获取订单分页列表
2025-07-23 16:56:19.750  INFO [订单分页] 查询参数: page=1, per_page=5, batch_no=Some(""), po_number=None, estate=None
2025-07-23 16:56:19.766  INFO [订单分页] 数据库连接获取成功
2025-07-23 16:56:19.799  INFO [订单分页] 查询成功，获取到5条记录
2025-07-23 16:56:19.806  INFO [订单分页] 成功加载关联的订单请求数据
2025-07-23 17:21:33.611  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 17:21:33.613  INFO 数据库连接管理器创建成功
2025-07-23 17:22:13.962  INFO 数据库连接池创建成功
2025-07-23 17:22:13.973  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 21:45:50.817  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 21:45:50.819  INFO 数据库连接管理器创建成功
2025-07-23 21:45:50.858  INFO 数据库连接池创建成功
2025-07-23 21:45:50.863  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 21:50:52.170  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 21:50:52.171  INFO 数据库连接管理器创建成功
2025-07-23 21:50:52.202  INFO 数据库连接池创建成功
2025-07-23 21:50:52.207  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 21:52:22.635  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 21:52:22.637  INFO 数据库连接管理器创建成功
2025-07-23 21:52:22.668  INFO 数据库连接池创建成功
2025-07-23 21:52:22.673  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 22:15:03.885  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 22:15:03.886  INFO 数据库连接管理器创建成功
2025-07-23 22:15:03.936  INFO 数据库连接池创建成功
2025-07-23 22:15:03.943  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 23:00:01.643  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 23:00:01.645  INFO 数据库连接管理器创建成功
2025-07-23 23:00:01.682  INFO 数据库连接池创建成功
2025-07-23 23:00:01.690  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 23:07:03.498  INFO 正在优雅地关闭服务器...
2025-07-23 23:23:07.717  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 23:23:07.719  INFO 数据库连接管理器创建成功
2025-07-23 23:23:07.757  INFO 数据库连接池创建成功
2025-07-23 23:23:07.761  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 23:29:51.033  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-23 23:29:51.034  INFO 数据库连接管理器创建成功
2025-07-23 23:29:51.065  INFO 数据库连接池创建成功
2025-07-23 23:29:51.071  INFO 服务器运行在 0.0.0.0:3000
2025-07-23 23:30:03.358  INFO [拆箱] 开始拆箱操作流程
2025-07-23 23:30:03.358  INFO [拆箱] 接收到请求参数: case=013034354728950921100000007221, codes=["010034354728950821100000008004", "010034354728950821100000008003"]
2025-07-23 23:30:03.362  INFO [拆箱] 发送HTTP请求到拆箱接口: {"case":"013034354728950921100000007221","codes":["010034354728950821100000008004","010034354728950821100000008003"]}
2025-07-23 23:30:03.363 DEBUG starting new connection: http://localhost:8000/    
2025-07-23 23:30:03.363 DEBUG proxy(http://127.0.0.1:7897) intercepts 'http://localhost:8000/'    
2025-07-23 23:30:03.363 DEBUG connecting to 127.0.0.1:7897
2025-07-23 23:30:03.364 DEBUG connected to 127.0.0.1:7897
2025-07-23 23:30:03.365 DEBUG flushed 272 bytes
2025-07-23 23:30:04.200 DEBUG parsed 10 headers
2025-07-23 23:30:04.201 DEBUG incoming body is chunked encoding
2025-07-23 23:30:04.202 DEBUG incoming chunked header: 0x3B (59 bytes)
2025-07-23 23:30:04.202 DEBUG incoming body completed
2025-07-23 23:30:04.203 ERROR [拆箱] 拆箱失败: 大箱码不存在
