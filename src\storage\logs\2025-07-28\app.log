2025-07-28 09:23:07.454  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-28 09:23:07.455  INFO 数据库连接管理器创建成功
2025-07-28 09:23:07.492  INFO 数据库连接池创建成功
2025-07-28 09:23:07.497  INFO 服务器运行在 0.0.0.0:3000
2025-07-28 09:23:10.074  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-28 09:23:10.077  INFO 数据库连接管理器创建成功
2025-07-28 09:23:10.106  INFO 数据库连接池创建成功
2025-07-28 09:23:10.112  INFO 服务器运行在 0.0.0.0:3000
2025-07-28 09:54:05.499  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-28 09:54:05.500  INFO 数据库连接管理器创建成功
2025-07-28 09:54:05.529  INFO 数据库连接池创建成功
2025-07-28 09:54:05.533  INFO 服务器运行在 0.0.0.0:3000
2025-07-28 09:54:53.953  INFO [网络检测] 网络连接完成，耗时: 58ms
2025-07-28 09:55:00.319  INFO [网络检测] 网络连接完成，耗时: 26ms
2025-07-28 09:55:06.816  INFO [网络检测] 网络连接完成，耗时: 30ms
2025-07-28 09:55:24.481  INFO [网络检测] 网络连接完成，耗时: 8ms
2025-07-28 09:55:33.453  INFO [网络检测] 网络连接完成，耗时: 33ms
2025-07-28 09:55:34.749  INFO [网络检测] 网络连接完成，耗时: 23ms
2025-07-28 09:55:42.934  INFO [数据同步] 开始获取各表数据条数
2025-07-28 09:55:42.943  INFO [数据同步] 数据库连接获取成功
2025-07-28 09:55:42.956  INFO [数据同步] 查询成功，产品:24, 批次:81, 订单:30, 返工:2
2025-07-28 09:55:49.553  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-28 09:55:49.553  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-28 09:55:49.554  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/api/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-28 09:55:53.815  INFO [网络检测] 网络连接完成，耗时: 9ms
2025-07-28 09:56:01.921  INFO [检查更新] 接收到更新请求: action=Some("check"), platform=Some("android"), appid=Some("com.huahai.pda")
2025-07-28 09:56:01.921  INFO [检查更新] 检查 android 平台 com.huahai.pda 应用的更新
2025-07-28 09:56:01.922  INFO [检查更新] 返回应用信息: Object {"description": Array [String("修复已知问题"), String("优化用户体验"), String("提升性能")], "downloadUrl": String("/api/docking/rise?action=download&platform=android&appid=com.huahai.pda"), "upgrade": Bool(false)}
2025-07-28 10:00:22.832  INFO [查询序列码] 开始查询序列码信息
2025-07-28 10:00:22.844  INFO [查询序列码] 数据库连接获取成功
2025-07-28 10:00:22.858  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 10:00:22.863  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 10:00:22.864  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 10:00:23.057  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-28 10:00:23.058  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216888, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 10:02:18.623  INFO [查询序列码] 开始查询序列码信息
2025-07-28 10:02:18.635  INFO [查询序列码] 数据库连接获取成功
2025-07-28 10:02:18.637  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 10:02:18.638  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 10:02:18.639  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 10:02:18.782  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-28 10:02:18.784  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216888, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 10:05:06.930  INFO [查询序列码] 开始查询序列码信息
2025-07-28 10:05:06.939  INFO [查询序列码] 数据库连接获取成功
2025-07-28 10:05:06.941  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 10:05:06.943  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 10:05:06.943  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 10:05:07.069  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-28 10:05:07.070  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216888, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 10:11:44.929  INFO [查询序列码] 开始查询序列码信息
2025-07-28 10:11:44.938  INFO [查询序列码] 数据库连接获取成功
2025-07-28 10:11:44.941  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 10:11:44.942  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 10:11:44.942  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 10:11:45.056  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-28 10:11:45.057  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216888, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 10:59:18.143  INFO [查询序列码] 开始查询序列码信息
2025-07-28 10:59:18.153  INFO [查询序列码] 数据库连接获取成功
2025-07-28 10:59:18.160  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 10:59:18.164  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 10:59:18.164  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 10:59:18.295  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-28 10:59:18.296  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216888, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 11:15:48.019  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-28 11:15:48.021  INFO 数据库连接管理器创建成功
2025-07-28 11:15:48.056  INFO 数据库连接池创建成功
2025-07-28 11:15:48.063  INFO 服务器运行在 0.0.0.0:3000
2025-07-28 14:24:50.606  INFO [查询序列码] 开始查询序列码信息
2025-07-28 14:24:50.641  INFO [查询序列码] 数据库连接获取成功
2025-07-28 14:24:50.652  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 14:24:50.658  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 14:24:50.658  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 14:24:50.850  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-28 14:24:50.853  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216888, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 14:25:29.371  INFO [查询序列码] 开始查询序列码信息
2025-07-28 14:25:29.382  INFO [查询序列码] 数据库连接获取成功
2025-07-28 14:25:29.385  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 14:25:29.387  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 14:25:29.387  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-28 14:25:29.388  INFO [查询序列码] 瓶码不需要查询数量
2025-07-28 14:25:29.389  INFO [查询序列码] 查询序列码信息完成: code=010034354728010921100005730418, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 14:25:54.049  INFO [查询序列码] 开始查询序列码信息
2025-07-28 14:25:54.057  INFO [查询序列码] 数据库连接获取成功
2025-07-28 14:25:54.060  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 14:25:54.061  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 14:25:54.062  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-28 14:25:54.062  INFO [查询序列码] 瓶码不需要查询数量
2025-07-28 14:25:54.063  INFO [查询序列码] 查询序列码信息完成: code=010034354728010921100005730464, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 14:37:45.843  INFO [查询序列码] 开始查询序列码信息
2025-07-28 14:37:45.872  INFO [查询序列码] 数据库连接获取成功
2025-07-28 14:37:45.875  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 14:37:45.877  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 14:37:45.877  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 14:37:45.990  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-28 14:37:45.991  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216888, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 14:37:51.435  INFO [查询序列码] 开始查询序列码信息
2025-07-28 14:37:51.447  INFO [查询序列码] 数据库连接获取成功
2025-07-28 14:37:51.449  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 14:37:51.451  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 14:37:51.451  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-28 14:37:51.451  INFO [查询序列码] 瓶码不需要查询数量
2025-07-28 14:37:51.452  INFO [查询序列码] 查询序列码信息完成: code=010034354728010921100005730418, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 14:44:11.915  INFO [查询序列码] 开始查询序列码信息
2025-07-28 14:44:11.927  INFO [查询序列码] 数据库连接获取成功
2025-07-28 14:44:11.929  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 14:44:11.931  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 14:44:11.931  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 14:44:12.050  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-28 14:44:12.051  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216888, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 14:44:17.392  INFO [查询序列码] 开始查询序列码信息
2025-07-28 14:44:17.418  INFO [查询序列码] 数据库连接获取成功
2025-07-28 14:44:17.420  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 14:44:17.421  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 14:44:17.421  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-28 14:44:17.421  INFO [查询序列码] 瓶码不需要查询数量
2025-07-28 14:44:17.422  INFO [查询序列码] 查询序列码信息完成: code=010034354728010921100005730464, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 14:44:25.796  INFO [查询序列码] 开始查询序列码信息
2025-07-28 14:44:25.806  INFO [查询序列码] 数据库连接获取成功
2025-07-28 14:44:25.809  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 14:44:25.810  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 14:44:25.811  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-28 14:44:25.811  INFO [查询序列码] 瓶码不需要查询数量
2025-07-28 14:44:25.812  INFO [查询序列码] 查询序列码信息完成: code=010034354728010921100005730418, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 14:46:24.878  INFO [查询序列码] 开始查询序列码信息
2025-07-28 14:46:24.888  INFO [查询序列码] 数据库连接获取成功
2025-07-28 14:46:24.891  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 14:46:24.893  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 14:46:24.893  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 14:46:25.010  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-28 14:46:25.011  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216888, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 14:46:29.781  INFO [查询序列码] 开始查询序列码信息
2025-07-28 14:46:29.792  INFO [查询序列码] 数据库连接获取成功
2025-07-28 14:46:29.795  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 14:46:29.797  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 14:46:29.797  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-28 14:46:29.798  INFO [查询序列码] 瓶码不需要查询数量
2025-07-28 14:46:29.799  INFO [查询序列码] 查询序列码信息完成: code=010034354728010921100005730464, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 14:46:39.677  INFO [查询序列码] 开始查询序列码信息
2025-07-28 14:46:39.688  INFO [查询序列码] 数据库连接获取成功
2025-07-28 14:46:39.690  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 14:46:39.692  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 14:46:39.692  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-28 14:46:39.692  INFO [查询序列码] 瓶码不需要查询数量
2025-07-28 14:46:39.693  INFO [查询序列码] 查询序列码信息完成: code=010034354728010921100005730418, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 14:48:12.078  INFO [查询序列码] 开始查询序列码信息
2025-07-28 14:48:12.090  INFO [查询序列码] 数据库连接获取成功
2025-07-28 14:48:12.092  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 14:48:12.094  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 14:48:12.095  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 14:48:12.225  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-28 14:48:12.226  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216888, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 14:48:14.609  INFO [查询序列码] 开始查询序列码信息
2025-07-28 14:48:14.634  INFO [查询序列码] 数据库连接获取成功
2025-07-28 14:48:14.636  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 14:48:14.638  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 14:48:14.638  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-28 14:48:14.639  INFO [查询序列码] 瓶码不需要查询数量
2025-07-28 14:48:14.644  INFO [查询序列码] 查询序列码信息完成: code=010034354728010921100005730418, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 15:30:58.734  INFO [查询序列码] 开始查询序列码信息
2025-07-28 15:30:58.765  INFO [查询序列码] 数据库连接获取成功
2025-07-28 15:30:58.791  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-28 15:30:58.797  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-28 15:30:58.797  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-28 15:30:58.798  INFO [查询序列码] 瓶码不需要查询数量
2025-07-28 15:30:58.798  INFO [查询序列码] 查询序列码信息完成: code=010034354728010921100005730418, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-28 15:31:07.726  INFO [拆箱] 开始拆箱操作流程
2025-07-28 15:31:07.727  INFO [拆箱] 接收到请求参数: case=015034354728010421100000216888, codes=["010034354728010921100005730418"]
2025-07-28 15:31:07.740  INFO [拆箱] 发送HTTP请求到拆箱接口: {"case":"015034354728010421100000216888","codes":["010034354728010921100005730418"]}
2025-07-28 15:31:07.746 DEBUG starting new connection: http://localhost:8000/    
2025-07-28 15:31:07.752 DEBUG resolving host="localhost"
2025-07-28 15:31:07.758 DEBUG connecting to [::1]:8000
2025-07-28 15:31:07.760 DEBUG connected to [::1]:8000
2025-07-28 15:31:07.763 DEBUG flushed 217 bytes
2025-07-28 15:31:07.764 DEBUG parsed 4 headers
2025-07-28 15:31:07.764 DEBUG incoming body is chunked encoding
2025-07-28 15:31:07.765 DEBUG incoming chunked header: 0x3 (3 bytes)
2025-07-28 15:31:07.765 DEBUG incoming body completed
2025-07-28 15:31:07.766 DEBUG pooling idle connection for ("http", localhost:8000)
2025-07-28 15:31:07.766 ERROR [拆箱] 响应格式错误: 缺少状态码
2025-07-28 15:31:58.771  INFO [拆箱] 开始拆箱操作流程
2025-07-28 15:31:58.772  INFO [拆箱] 接收到请求参数: case=015034354728010421100000216888, codes=["010034354728010921100005730418"]
2025-07-28 15:31:58.774  INFO [拆箱] 发送HTTP请求到拆箱接口: {"case":"015034354728010421100000216888","codes":["010034354728010921100005730418"]}
2025-07-28 15:31:58.775 DEBUG starting new connection: http://localhost:8000/    
2025-07-28 15:31:58.776 DEBUG resolving host="localhost"
2025-07-28 15:31:58.776 DEBUG connecting to [::1]:8000
2025-07-28 15:31:58.777 DEBUG connected to [::1]:8000
2025-07-28 15:31:58.777 DEBUG flushed 217 bytes
2025-07-28 15:32:00.575 DEBUG parsed 9 headers
2025-07-28 15:32:00.575 DEBUG incoming body is chunked encoding
2025-07-28 15:32:00.576 DEBUG incoming chunked header: 0x66 (102 bytes)
2025-07-28 15:32:00.577 DEBUG incoming body completed
2025-07-28 15:32:00.577 DEBUG pooling idle connection for ("http", localhost:8000)
2025-07-28 15:32:00.578 ERROR [拆箱] 拆箱失败: 层级编码为5的规则信息不存在
2025-07-28 15:36:39.670  INFO [查询序列码] 开始查询序列码信息
2025-07-28 15:36:41.954  INFO [查询序列码] 数据库连接获取成功
2025-07-28 15:36:42.086  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-28 15:36:42.300  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-28 15:36:43.135  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 15:36:49.606  INFO [查询序列码] 开始查询序列码信息
2025-07-28 15:36:52.273  INFO [查询序列码] 数据库连接获取成功
2025-07-28 15:36:52.343  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-28 15:36:53.760  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-28 15:36:53.760  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 15:38:04.340  INFO [网络检测] 网络连接完成，耗时: 555ms
2025-07-28 15:38:18.918  INFO [查询序列码] 开始查询序列码信息
2025-07-28 15:38:24.088  INFO [查询序列码] 数据库连接获取成功
2025-07-28 15:38:24.112  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-28 15:38:25.088  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-28 15:38:25.088  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 15:38:35.323  INFO [查询序列码] 开始查询序列码信息
2025-07-28 15:38:37.884  INFO [查询序列码] 数据库连接获取成功
2025-07-28 15:38:37.915  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-28 15:38:37.934  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-28 15:38:37.935  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 15:38:39.226  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-28 15:38:39.228  INFO [查询序列码] 查询序列码信息完成: code=015034354734703621100000045719, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-28 15:38:51.337  INFO [查询序列码] 开始查询序列码信息
2025-07-28 15:38:51.358  INFO [查询序列码] 数据库连接获取成功
2025-07-28 15:38:51.367  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-28 15:38:51.369  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-28 15:38:51.369  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 15:38:51.991  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-28 15:38:51.992  INFO [查询序列码] 查询序列码信息完成: code=015034354734703621100000045719, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-28 15:39:07.889  INFO [查询序列码] 开始查询序列码信息
2025-07-28 15:39:07.893 ERROR [查询序列码] 数据库连接获取失败: An error occured during the attempt of performing I/O: 由于系统缓冲区空间不足或队列已满，不能执行套接字上的操作。 (os error 10055)
2025-07-28 15:39:10.545  INFO [查询序列码] 开始查询序列码信息
2025-07-28 15:39:10.545 ERROR [查询序列码] 数据库连接获取失败: An error occured during the attempt of performing I/O: 由于系统缓冲区空间不足或队列已满，不能执行套接字上的操作。 (os error 10055)
2025-07-28 15:39:13.418  INFO [查询序列码] 开始查询序列码信息
2025-07-28 15:39:13.419 ERROR [查询序列码] 数据库连接获取失败: An error occured during the attempt of performing I/O: 由于系统缓冲区空间不足或队列已满，不能执行套接字上的操作。 (os error 10055)
2025-07-28 16:11:58.045  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-28 16:11:58.046  INFO 数据库连接管理器创建成功
2025-07-28 16:11:58.075  INFO 数据库连接池创建成功
2025-07-28 16:11:58.079  INFO 服务器运行在 0.0.0.0:3000
2025-07-28 16:12:21.219  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-28 16:12:21.220  INFO 数据库连接管理器创建成功
2025-07-28 16:12:21.249  INFO 数据库连接池创建成功
2025-07-28 16:12:21.254  INFO 服务器运行在 0.0.0.0:3000
2025-07-28 16:12:28.574  INFO [查询序列码] 开始查询序列码信息
2025-07-28 16:12:28.614  INFO [查询序列码] 数据库连接获取成功
2025-07-28 16:12:28.631  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-28 16:12:28.635  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-28 16:12:28.635  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-28 16:12:28.820  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-28 16:12:28.820  INFO [查询序列码] 查询序列码信息完成: code=015034354734703621100000045719, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-28 16:12:56.841  INFO [查询序列码] 开始查询序列码信息
2025-07-28 16:12:56.859  INFO [查询序列码] 数据库连接获取成功
2025-07-28 16:12:56.866  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-28 16:12:56.869  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-28 16:12:56.870  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-28 16:12:56.870  INFO [查询序列码] 瓶码不需要查询数量
2025-07-28 16:12:56.871  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356651, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-28 16:13:02.147  INFO [查询序列码] 开始查询序列码信息
2025-07-28 16:13:02.159  INFO [查询序列码] 数据库连接获取成功
2025-07-28 16:13:02.161  INFO [查询序列码] 开始查询生产订单信息: order_id=56
2025-07-28 16:13:02.163  INFO [查询序列码] 开始查询产品信息: product_id=1
2025-07-28 16:13:02.164  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-28 16:13:02.164  INFO [查询序列码] 瓶码不需要查询数量
2025-07-28 16:13:02.165  INFO [查询序列码] 查询序列码信息完成: code=010034354734703121100002356680, batch=2025060901, product=PAROXETINE TABLETS, USP
2025-07-28 16:13:06.917  INFO [拆箱] 开始拆箱操作流程
2025-07-28 16:13:06.918  INFO [拆箱] 接收到请求参数: case=015034354734703621100000045719, codes=["010034354734703121100002356651", "010034354734703121100002356680"]
2025-07-28 16:13:06.921  INFO [拆箱] 发送HTTP请求到拆箱接口: {"case":"015034354734703621100000045719","codes":["010034354734703121100002356651","010034354734703121100002356680"]}
2025-07-28 16:13:06.922 DEBUG starting new connection: http://localhost:8000/    
2025-07-28 16:13:06.923 DEBUG proxy(http://127.0.0.1:7897) intercepts 'http://localhost:8000/'    
2025-07-28 16:13:06.924 DEBUG connecting to 127.0.0.1:7897
2025-07-28 16:13:06.925 DEBUG connected to 127.0.0.1:7897
2025-07-28 16:13:06.926 DEBUG flushed 272 bytes
2025-07-28 16:13:07.020 DEBUG parsed 3 headers
2025-07-28 16:13:07.020 DEBUG incoming body is chunked encoding
2025-07-28 16:13:07.022 DEBUG incoming chunked header: 0x3 (3 bytes)
2025-07-28 16:13:07.023 DEBUG incoming body completed
2025-07-28 16:13:07.024 ERROR [拆箱] 响应格式错误: 缺少状态码
2025-07-28 16:18:47.187  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-28 16:18:47.187  INFO 数据库连接管理器创建成功
2025-07-28 16:18:47.219  INFO 数据库连接池创建成功
2025-07-28 16:18:47.226  INFO 服务器运行在 0.0.0.0:3000
2025-07-28 16:18:50.663  INFO [拆箱] 开始拆箱操作流程
2025-07-28 16:18:50.664  INFO [拆箱] 接收到请求参数: case=015034354734703621100000045719, codes=["010034354734703121100002356651", "010034354734703121100002356680"]
2025-07-28 16:18:50.668  INFO [拆箱] 发送HTTP请求到拆箱接口: {"case":"015034354734703621100000045719","codes":["010034354734703121100002356651","010034354734703121100002356680"]}
2025-07-28 16:18:50.669 DEBUG starting new connection: http://localhost:8000/    
2025-07-28 16:18:50.669 DEBUG proxy(http://127.0.0.1:7897) intercepts 'http://localhost:8000/'    
2025-07-28 16:18:50.670 DEBUG connecting to 127.0.0.1:7897
2025-07-28 16:18:50.671 DEBUG connected to 127.0.0.1:7897
2025-07-28 16:18:50.672 DEBUG flushed 272 bytes
2025-07-28 16:18:50.674 DEBUG parsed 3 headers
2025-07-28 16:18:50.675 DEBUG incoming body is chunked encoding
2025-07-28 16:18:50.675 DEBUG incoming chunked header: 0x3 (3 bytes)
2025-07-28 16:18:50.675 DEBUG incoming body completed
2025-07-28 16:18:50.676  INFO [拆箱] 响应: 404
2025-07-28 16:18:50.676 ERROR [拆箱] 响应格式错误: 缺少状态码
2025-07-28 16:20:00.405  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-28 16:20:00.406  INFO 数据库连接管理器创建成功
2025-07-28 16:20:00.440  INFO 数据库连接池创建成功
2025-07-28 16:20:00.446  INFO 服务器运行在 0.0.0.0:3000
2025-07-28 16:21:04.628  INFO [拆箱] 开始拆箱操作流程
2025-07-28 16:21:04.629  INFO [拆箱] 接收到请求参数: case=015034354734703621100000045719, codes=["010034354734703121100002356651", "010034354734703121100002356680"]
2025-07-28 16:21:04.634  INFO [拆箱] 发送HTTP请求到拆箱接口: {"case":"015034354734703621100000045719","codes":["010034354734703121100002356651","010034354734703121100002356680"]}
2025-07-28 16:21:04.635 DEBUG starting new connection: http://localhost:8000/    
2025-07-28 16:21:04.635 DEBUG proxy(http://127.0.0.1:7897) intercepts 'http://localhost:8000/'    
2025-07-28 16:21:04.636 DEBUG connecting to 127.0.0.1:7897
2025-07-28 16:21:04.638 DEBUG connected to 127.0.0.1:7897
2025-07-28 16:21:04.639 DEBUG flushed 272 bytes
2025-07-28 16:21:05.260 DEBUG parsed 10 headers
2025-07-28 16:21:05.261 DEBUG incoming body is chunked encoding
2025-07-28 16:21:05.262 DEBUG incoming chunked header: 0x359 (857 bytes)
2025-07-28 16:21:05.263 DEBUG incoming body completed
2025-07-28 16:21:05.264  INFO [拆箱] 拆箱成功
2025-07-28 16:26:36.127  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-28 16:26:36.130  INFO 数据库连接管理器创建成功
2025-07-28 16:26:48.663  INFO 数据库连接池创建成功
2025-07-28 16:26:48.676  INFO 服务器运行在 0.0.0.0:3000
2025-07-28 16:28:59.397  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-28 16:28:59.399  INFO 数据库连接管理器创建成功
2025-07-28 16:29:39.685 ERROR 创建数据库连接池失败: 由于系统缓冲区空间不足或队列已满，不能执行套接字上的操作。 (os error 10055)
2025-07-28 16:29:39.686 ERROR 请检查以下内容:
2025-07-28 16:29:39.689 ERROR 1. 连接池参数是否合理
2025-07-28 16:29:39.691 ERROR 2. 数据库服务器负载是否过高
2025-07-28 16:29:39.693 ERROR 3. 网络连接是否稳定
