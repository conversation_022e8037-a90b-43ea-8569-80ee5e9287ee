2025-07-25 11:08:52.410  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 11:08:52.411  INFO 数据库连接管理器创建成功
2025-07-25 11:08:52.449  INFO 数据库连接池创建成功
2025-07-25 11:08:52.455  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 11:08:55.273  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 11:08:55.274  INFO 数据库连接管理器创建成功
2025-07-25 11:08:55.304  INFO 数据库连接池创建成功
2025-07-25 11:08:55.309  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 11:29:13.137  INFO [重打印] 开始重打印操作流程
2025-07-25 11:29:13.138  INFO [重打印] 接收到参数: code=015034354728010421100000216888
2025-07-25 11:29:13.185  INFO [重打印] 数据库连接获取成功
2025-07-25 11:29:13.185  INFO [打印服务] 开始获取序列码信息: 015034354728010421100000216888
2025-07-25 11:29:13.200  INFO [打印服务] 查询到序列码信息: code_flag=2, level_code=3, zero_box=0
2025-07-25 11:29:13.205  INFO [打印服务] 查询到产品信息: product_name=ESCITALOPRAM TABLETS, USP
2025-07-25 11:29:13.211  INFO [打印服务] 查询到订单信息: batch_no=**********, estate=4
2025-07-25 11:29:13.212  INFO [打印服务] 获取整箱模板数据
2025-07-25 11:29:13.250  INFO [打印服务] 箱内瓶码数量: 24
2025-07-25 11:29:13.251 ERROR [重打印] 获取打印数据失败: 未找到层级码3的控制码
2025-07-25 11:33:36.636  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 11:33:36.637  INFO 数据库连接管理器创建成功
2025-07-25 11:33:36.671  INFO 数据库连接池创建成功
2025-07-25 11:33:36.676  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 11:36:04.480  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 11:36:04.481  INFO 数据库连接管理器创建成功
2025-07-25 11:36:04.513  INFO 数据库连接池创建成功
2025-07-25 11:36:04.521  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 13:32:56.959  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 13:32:56.960  INFO 数据库连接管理器创建成功
2025-07-25 13:32:56.988  INFO 数据库连接池创建成功
2025-07-25 13:32:56.993  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 13:52:51.175  INFO [重打印] 开始重打印操作流程
2025-07-25 13:52:51.175  INFO [重打印] 接收到参数: code=015034354728010421100000216888
2025-07-25 13:52:51.215  INFO [重打印] 数据库连接获取成功
2025-07-25 13:52:51.216  INFO [打印服务] 开始获取序列码信息: 015034354728010421100000216888
2025-07-25 13:52:51.232  INFO [打印服务] 查询到序列码信息: code_flag=2, level_code=3, zero_box=0
2025-07-25 13:52:51.238  INFO [打印服务] 查询到产品信息: product_name=ESCITALOPRAM TABLETS, USP
2025-07-25 13:52:51.244  INFO [打印服务] 查询到订单信息: batch_no=**********, estate=4
2025-07-25 13:52:51.244  INFO [打印服务] 获取整箱模板数据
2025-07-25 13:52:51.272  INFO [打印服务] 箱内瓶码数量: 24
2025-07-25 13:52:51.273  INFO [重打印] 获取打印数据成功
2025-07-25 13:52:51.273  INFO [打印服务] 开始执行打印操作: host=127.0.0.1, port=7000
2025-07-25 13:52:51.274  INFO [打印服务] 打印数据: {"Message":[{"Node":"distributor1","Value":"Solco Healthcare US, LLC"},{"Node":"ProductName","Value":"ESCITALOPRAM TABLETS, USP"},{"Node":"PackageDose","Value":"5mg"},{"Node":"PackageUnit","Value":"100'S"},{"Node":"VariableCount","Value":"24"},{"Node":"exp","Value":"20280304"},{"Node":"lot","Value":"**********"},{"Node":"ndc","Value":"43547-280-10"},{"Node":"sn","Value":"100000216888"},{"Node":"gtin","Value":"50343547280104"},{"Node":"manufacturer2","Value":"Zhejiang Huahai Pharmaceutical Technology Co.,LTD."},{"Node":"manufacturerBy3","Value":"Jiangnan, Linhai, Zhejiang 317000, China"},{"Node":"distributorBy4","Value":"Somerset, NJ 08873, USA"},{"Node":"remark","Value":"Store at 20-25℃, excursions permitted to 15-30℃[see USP Controlled Room Temp.]."},{"Node":"controlCode","Value":"205244-02"}],"PrintCount":"1","PrintType":"1"}
2025-07-25 13:52:51.275  INFO [Socket客户端] 开始连接到服务器: 127.0.0.1:7000
2025-07-25 13:52:53.306 ERROR [Socket客户端] 连接失败: 由于目标计算机积极拒绝，无法连接。 (os error 10061)
2025-07-25 13:52:53.307 ERROR [打印服务] Socket连接失败: ConnectionError("由于目标计算机积极拒绝，无法连接。 (os error 10061)")
2025-07-25 13:52:53.308 ERROR [重打印] 打印失败: Socket连接失败: 连接错误: 由于目标计算机积极拒绝，无法连接。 (os error 10061)
2025-07-25 13:53:59.871  INFO [重打印] 开始重打印操作流程
2025-07-25 13:53:59.872  INFO [重打印] 接收到参数: code=015034354728010421100000216888
2025-07-25 13:53:59.884  INFO [重打印] 数据库连接获取成功
2025-07-25 13:53:59.884  INFO [打印服务] 开始获取序列码信息: 015034354728010421100000216888
2025-07-25 13:53:59.890  INFO [打印服务] 查询到序列码信息: code_flag=2, level_code=3, zero_box=0
2025-07-25 13:53:59.894  INFO [打印服务] 查询到产品信息: product_name=ESCITALOPRAM TABLETS, USP
2025-07-25 13:53:59.901  INFO [打印服务] 查询到订单信息: batch_no=**********, estate=4
2025-07-25 13:53:59.901  INFO [打印服务] 获取整箱模板数据
2025-07-25 13:53:59.929  INFO [打印服务] 箱内瓶码数量: 24
2025-07-25 13:53:59.929  INFO [重打印] 获取打印数据成功
2025-07-25 13:53:59.930  INFO [打印服务] 开始执行打印操作: host=127.0.0.1, port=7000
2025-07-25 13:53:59.930  INFO [打印服务] 打印数据: {"Message":[{"Node":"distributor1","Value":"Solco Healthcare US, LLC"},{"Node":"ProductName","Value":"ESCITALOPRAM TABLETS, USP"},{"Node":"PackageDose","Value":"5mg"},{"Node":"PackageUnit","Value":"100'S"},{"Node":"VariableCount","Value":"24"},{"Node":"exp","Value":"20280304"},{"Node":"lot","Value":"**********"},{"Node":"ndc","Value":"43547-280-10"},{"Node":"sn","Value":"100000216888"},{"Node":"gtin","Value":"50343547280104"},{"Node":"manufacturer2","Value":"Zhejiang Huahai Pharmaceutical Technology Co.,LTD."},{"Node":"manufacturerBy3","Value":"Jiangnan, Linhai, Zhejiang 317000, China"},{"Node":"distributorBy4","Value":"Somerset, NJ 08873, USA"},{"Node":"remark","Value":"Store at 20-25℃, excursions permitted to 15-30℃[see USP Controlled Room Temp.]."},{"Node":"controlCode","Value":"205244-02"}],"PrintCount":"1","PrintType":"1"}
2025-07-25 13:53:59.931  INFO [Socket客户端] 开始连接到服务器: 127.0.0.1:7000
2025-07-25 13:53:59.932 DEBUG [Socket客户端] 连接成功: 127.0.0.1:7000
2025-07-25 13:53:59.933  INFO [打印服务] Socket连接成功
2025-07-25 13:53:59.980  INFO [打印服务] 打印成功，服务器响应: {
  "status": "ok",
  "message": ""
}

2025-07-25 13:53:59.980  INFO [Socket客户端] 关闭连接
2025-07-25 13:53:59.981  INFO [重打印] 打印成功: {
  "status": "ok",
  "message": ""
}

2025-07-25 13:55:00.789  INFO [重打印] 开始重打印操作流程
2025-07-25 13:55:00.789  INFO [重打印] 接收到参数: code=015034354728010421100000216888
2025-07-25 13:55:00.804  INFO [重打印] 数据库连接获取成功
2025-07-25 13:55:00.804  INFO [打印服务] 开始获取序列码信息: 015034354728010421100000216888
2025-07-25 13:55:00.810  INFO [打印服务] 查询到序列码信息: code_flag=2, level_code=3, zero_box=0
2025-07-25 13:55:00.818  INFO [打印服务] 查询到产品信息: product_name=ESCITALOPRAM TABLETS, USP
2025-07-25 13:55:00.822  INFO [打印服务] 查询到订单信息: batch_no=**********, estate=4
2025-07-25 13:55:00.823  INFO [打印服务] 获取整箱模板数据
2025-07-25 13:55:00.853  INFO [打印服务] 箱内瓶码数量: 24
2025-07-25 13:55:00.853  INFO [重打印] 获取打印数据成功
2025-07-25 13:55:00.854  INFO [打印服务] 开始执行打印操作: host=127.0.0.1, port=7000
2025-07-25 13:55:00.854  INFO [打印服务] 打印数据: {"Message":[{"Node":"distributor1","Value":"Solco Healthcare US, LLC"},{"Node":"ProductName","Value":"ESCITALOPRAM TABLETS, USP"},{"Node":"PackageDose","Value":"5mg"},{"Node":"PackageUnit","Value":"100'S"},{"Node":"VariableCount","Value":"24"},{"Node":"exp","Value":"20280304"},{"Node":"lot","Value":"**********"},{"Node":"ndc","Value":"43547-280-10"},{"Node":"sn","Value":"100000216888"},{"Node":"gtin","Value":"50343547280104"},{"Node":"manufacturer2","Value":"Zhejiang Huahai Pharmaceutical Technology Co.,LTD."},{"Node":"manufacturerBy3","Value":"Jiangnan, Linhai, Zhejiang 317000, China"},{"Node":"distributorBy4","Value":"Somerset, NJ 08873, USA"},{"Node":"remark","Value":"Store at 20-25℃, excursions permitted to 15-30℃[see USP Controlled Room Temp.]."},{"Node":"controlCode","Value":"205244-02"}],"PrintCount":"1","PrintType":"1"}
2025-07-25 13:55:00.855  INFO [Socket客户端] 开始连接到服务器: 127.0.0.1:7000
2025-07-25 13:55:00.856 DEBUG [Socket客户端] 连接成功: 127.0.0.1:7000
2025-07-25 13:55:00.856  INFO [打印服务] Socket连接成功
2025-07-25 13:55:00.860  INFO [打印服务] 打印成功，服务器响应: {
  "status": "ok",
  "message": ""
}

2025-07-25 13:55:00.860  INFO [Socket客户端] 关闭连接
2025-07-25 13:55:00.860  INFO [重打印] 打印成功: {
  "status": "ok",
  "message": ""
}

2025-07-25 14:00:30.393  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 14:00:30.394  INFO 数据库连接管理器创建成功
2025-07-25 14:00:30.431  INFO 数据库连接池创建成功
2025-07-25 14:00:30.436  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 14:00:32.254  INFO [重打印] 开始重打印操作流程
2025-07-25 14:00:32.255  INFO [重打印] 接收到参数: code=015034354728010421100000216888
2025-07-25 14:00:32.311  INFO [重打印] 数据库连接获取成功
2025-07-25 14:00:32.311  INFO [打印服务] 开始获取序列码信息: 015034354728010421100000216888
2025-07-25 14:00:32.319  INFO [打印服务] 查询到序列码信息: code_flag=2, level_code=3, zero_box=0
2025-07-25 14:00:32.323  INFO [打印服务] 查询到产品信息: product_name=ESCITALOPRAM TABLETS, USP
2025-07-25 14:00:32.327  INFO [打印服务] 查询到订单信息: batch_no=**********, estate=4
2025-07-25 14:00:32.327  INFO [打印服务] 获取整箱模板数据
2025-07-25 14:00:32.351  INFO [打印服务] 箱内瓶码数量: 24
2025-07-25 14:00:32.352  INFO [重打印] 获取打印数据成功
2025-07-25 14:00:32.353  INFO [打印服务] 开始执行打印操作: host=127.0.0.1, port=7000
2025-07-25 14:00:32.353  INFO [打印服务] 打印数据: {"Message":[{"Node":"distributor1","Value":"Solco Healthcare US, LLC"},{"Node":"ProductName","Value":"ESCITALOPRAM TABLETS, USP"},{"Node":"PackageDose","Value":"5mg"},{"Node":"PackageUnit","Value":"100'S"},{"Node":"VariableCount","Value":"24"},{"Node":"exp","Value":"20280304"},{"Node":"lot","Value":"**********"},{"Node":"ndc","Value":"43547-280-10"},{"Node":"sn","Value":"100000216888"},{"Node":"gtin","Value":"50343547280104"},{"Node":"manufacturer2","Value":"Zhejiang Huahai Pharmaceutical Technology Co.,LTD."},{"Node":"manufacturerBy3","Value":"Jiangnan, Linhai, Zhejiang 317000, China"},{"Node":"distributorBy4","Value":"Somerset, NJ 08873, USA"},{"Node":"remark","Value":"Store at 20-25℃, excursions permitted to 15-30℃[see USP Controlled Room Temp.]."},{"Node":"controlCode","Value":"205244-02"}],"PrintCount":"1","PrintType":"1"}
2025-07-25 14:00:32.354  INFO [Socket客户端] 开始连接到服务器: 127.0.0.1:7000
2025-07-25 14:00:32.355 DEBUG [Socket客户端] 连接成功: 127.0.0.1:7000
2025-07-25 14:00:32.355  INFO [打印服务] Socket连接成功
2025-07-25 14:00:32.360  INFO [打印服务] 打印成功，服务器响应: {
  "status": "ok",
  "message": ""
}

2025-07-25 14:00:32.360  INFO [Socket客户端] 关闭连接
2025-07-25 14:00:32.360  INFO [重打印] 打印成功: {
  "status": "ok",
  "message": ""
}

2025-07-25 16:14:17.764  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 16:14:17.765  INFO 数据库连接管理器创建成功
2025-07-25 16:14:17.805  INFO 数据库连接池创建成功
2025-07-25 16:14:17.811  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 16:15:36.664  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 16:15:36.665  INFO 数据库连接管理器创建成功
2025-07-25 16:15:36.697  INFO 数据库连接池创建成功
2025-07-25 16:15:36.703  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 16:17:23.734  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 16:17:23.735  INFO 数据库连接管理器创建成功
2025-07-25 16:17:23.776  INFO 数据库连接池创建成功
2025-07-25 16:17:23.783  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 16:17:59.264  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 16:17:59.265  INFO 数据库连接管理器创建成功
2025-07-25 16:17:59.296  INFO 数据库连接池创建成功
2025-07-25 16:17:59.302  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 16:26:43.954  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 16:26:43.955  INFO 数据库连接管理器创建成功
2025-07-25 16:26:44.006  INFO 数据库连接池创建成功
2025-07-25 16:26:44.017  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 17:07:11.394  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 17:07:11.395  INFO 数据库连接管理器创建成功
2025-07-25 17:07:11.434  INFO 数据库连接池创建成功
2025-07-25 17:07:11.443  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 17:09:08.770  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 17:09:08.771  INFO 数据库连接管理器创建成功
2025-07-25 17:09:08.802  INFO 数据库连接池创建成功
2025-07-25 17:09:08.807  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 17:44:32.293  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 17:44:32.294  INFO 数据库连接管理器创建成功
2025-07-25 17:44:32.334  INFO 数据库连接池创建成功
2025-07-25 17:44:32.340  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 17:47:21.966  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 17:47:21.967  INFO 数据库连接管理器创建成功
2025-07-25 17:47:22.022  INFO 数据库连接池创建成功
2025-07-25 17:47:22.034  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 17:49:23.903  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 17:49:23.904  INFO 数据库连接管理器创建成功
2025-07-25 17:49:23.951  INFO 数据库连接池创建成功
2025-07-25 17:49:23.959  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 17:49:54.233  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 17:49:54.235  INFO 数据库连接管理器创建成功
2025-07-25 17:49:54.266  INFO 数据库连接池创建成功
2025-07-25 17:49:54.271  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 17:52:05.353  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 17:52:05.354  INFO 数据库连接管理器创建成功
2025-07-25 17:52:05.393  INFO 数据库连接池创建成功
2025-07-25 17:52:05.402  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 17:52:30.502  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 17:52:30.503  INFO 数据库连接管理器创建成功
2025-07-25 17:52:30.547  INFO 数据库连接池创建成功
2025-07-25 17:52:30.556  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 17:52:46.971  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 17:52:46.972  INFO 数据库连接管理器创建成功
2025-07-25 17:52:47.001  INFO 数据库连接池创建成功
2025-07-25 17:52:47.006  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 17:54:56.077  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 17:54:56.079  INFO 数据库连接管理器创建成功
2025-07-25 17:54:56.113  INFO 数据库连接池创建成功
2025-07-25 17:54:56.120  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 17:55:15.667  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 17:55:15.669  INFO 数据库连接管理器创建成功
2025-07-25 17:55:15.706  INFO 数据库连接池创建成功
2025-07-25 17:55:15.716  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 17:56:34.495  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 17:56:34.497  INFO 数据库连接管理器创建成功
2025-07-25 17:56:34.529  INFO 数据库连接池创建成功
2025-07-25 17:56:34.535  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 18:28:25.314  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 18:28:25.315  INFO 数据库连接管理器创建成功
2025-07-25 18:28:25.347  INFO 数据库连接池创建成功
2025-07-25 18:28:25.352  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 18:36:36.454  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 18:36:36.455  INFO 数据库连接管理器创建成功
2025-07-25 18:36:36.483  INFO 数据库连接池创建成功
2025-07-25 18:36:36.488  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 18:37:43.046  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 18:37:43.047  INFO 数据库连接管理器创建成功
2025-07-25 18:37:43.080  INFO 数据库连接池创建成功
2025-07-25 18:37:43.086  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 18:38:30.586  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 18:38:30.587  INFO 数据库连接管理器创建成功
2025-07-25 18:38:30.617  INFO 数据库连接池创建成功
2025-07-25 18:38:30.622  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 21:44:23.744  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 21:44:23.745  INFO 数据库连接管理器创建成功
2025-07-25 21:44:23.780  INFO 数据库连接池创建成功
2025-07-25 21:44:23.787  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:01:11.197  INFO 网络连接完成，耗时: 39ms
2025-07-25 22:22:28.216  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:22:28.216  INFO 数据库连接管理器创建成功
2025-07-25 22:22:28.249  INFO 数据库连接池创建成功
2025-07-25 22:22:28.254  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:22:40.283  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:22:40.284  INFO 数据库连接管理器创建成功
2025-07-25 22:22:40.323  INFO 数据库连接池创建成功
2025-07-25 22:22:40.327  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:22:41.859  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:22:41.860  INFO 数据库连接管理器创建成功
2025-07-25 22:22:41.913  INFO 数据库连接池创建成功
2025-07-25 22:22:41.920  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:23:31.520  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:23:31.521  INFO 数据库连接管理器创建成功
2025-07-25 22:23:31.563  INFO 数据库连接池创建成功
2025-07-25 22:23:31.572  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:25:06.683  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:25:06.685  INFO 数据库连接管理器创建成功
2025-07-25 22:25:06.754  INFO 数据库连接池创建成功
2025-07-25 22:25:06.760  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:25:16.493  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:25:16.494  INFO 数据库连接管理器创建成功
2025-07-25 22:25:16.526  INFO 数据库连接池创建成功
2025-07-25 22:25:16.531  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:25:40.556  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:25:40.558  INFO 数据库连接管理器创建成功
2025-07-25 22:25:40.642  INFO 数据库连接池创建成功
2025-07-25 22:25:40.655  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:25:44.346  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:25:44.347  INFO 数据库连接管理器创建成功
2025-07-25 22:25:44.389  INFO 数据库连接池创建成功
2025-07-25 22:25:44.394  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:26:03.493  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:26:03.494  INFO 数据库连接管理器创建成功
2025-07-25 22:26:03.537  INFO 数据库连接池创建成功
2025-07-25 22:26:03.543  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:26:37.903  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:26:37.904  INFO 数据库连接管理器创建成功
2025-07-25 22:26:37.960  INFO 数据库连接池创建成功
2025-07-25 22:26:37.972  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:28:44.964  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:28:44.966  INFO 数据库连接管理器创建成功
2025-07-25 22:28:45.001  INFO 数据库连接池创建成功
2025-07-25 22:28:45.006  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:29:31.111  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:29:31.114  INFO 数据库连接管理器创建成功
2025-07-25 22:29:31.157  INFO 数据库连接池创建成功
2025-07-25 22:29:31.163  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:29:40.273  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:29:40.274  INFO 数据库连接管理器创建成功
2025-07-25 22:29:40.326  INFO 数据库连接池创建成功
2025-07-25 22:29:40.335  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:29:42.955  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:29:42.956  INFO 数据库连接管理器创建成功
2025-07-25 22:29:43.000  INFO 数据库连接池创建成功
2025-07-25 22:29:43.006  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:30:08.392  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:30:08.393  INFO 数据库连接管理器创建成功
2025-07-25 22:30:08.444  INFO 数据库连接池创建成功
2025-07-25 22:30:08.451  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:30:15.464  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:30:15.466  INFO 数据库连接管理器创建成功
2025-07-25 22:30:15.494  INFO 数据库连接池创建成功
2025-07-25 22:30:15.501  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:30:40.374  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:30:40.375  INFO 数据库连接管理器创建成功
2025-07-25 22:30:40.401  INFO 数据库连接池创建成功
2025-07-25 22:30:40.405  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:30:45.216  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:30:45.217  INFO 数据库连接管理器创建成功
2025-07-25 22:30:45.258  INFO 数据库连接池创建成功
2025-07-25 22:30:45.265  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:30:52.239  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:30:52.240  INFO 数据库连接管理器创建成功
2025-07-25 22:30:52.266  INFO 数据库连接池创建成功
2025-07-25 22:30:52.271  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:30:58.404  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:30:58.405  INFO 数据库连接管理器创建成功
2025-07-25 22:30:58.440  INFO 数据库连接池创建成功
2025-07-25 22:30:58.446  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:31:04.682  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:31:04.682  INFO 数据库连接管理器创建成功
2025-07-25 22:31:04.721  INFO 数据库连接池创建成功
2025-07-25 22:31:04.726  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:31:08.367  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:31:08.368  INFO 数据库连接管理器创建成功
2025-07-25 22:31:08.396  INFO 数据库连接池创建成功
2025-07-25 22:31:08.401  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:31:15.739  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:31:15.740  INFO 数据库连接管理器创建成功
2025-07-25 22:31:15.772  INFO 数据库连接池创建成功
2025-07-25 22:31:15.776  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:35:02.401  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:35:02.402  INFO 数据库连接管理器创建成功
2025-07-25 22:35:02.435  INFO 数据库连接池创建成功
2025-07-25 22:35:02.440  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:37:03.251  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:37:03.252  INFO 数据库连接管理器创建成功
2025-07-25 22:37:03.280  INFO 数据库连接池创建成功
2025-07-25 22:37:03.285  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 22:38:41.946  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 22:38:41.947  INFO 数据库连接管理器创建成功
2025-07-25 22:38:41.974  INFO 数据库连接池创建成功
2025-07-25 22:38:41.979  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 23:33:01.707  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 23:33:01.708  INFO 数据库连接管理器创建成功
2025-07-25 23:33:01.752  INFO 数据库连接池创建成功
2025-07-25 23:33:01.757  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 23:37:15.302  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 23:37:15.305  INFO 数据库连接管理器创建成功
2025-07-25 23:37:15.348  INFO 数据库连接池创建成功
2025-07-25 23:37:15.354  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 23:37:20.095  INFO 网络连接完成，耗时: 42ms
2025-07-25 23:39:55.939  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 23:39:55.940  INFO 数据库连接管理器创建成功
2025-07-25 23:39:55.973  INFO 数据库连接池创建成功
2025-07-25 23:39:55.978  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 23:41:04.641  INFO 网络连接完成，耗时: 32ms
2025-07-25 23:47:51.052  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 23:47:51.053  INFO 数据库连接管理器创建成功
2025-07-25 23:47:51.085  INFO 数据库连接池创建成功
2025-07-25 23:47:51.090  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 23:49:45.332  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 23:49:45.333  INFO 数据库连接管理器创建成功
2025-07-25 23:49:45.365  INFO 数据库连接池创建成功
2025-07-25 23:49:45.370  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 23:49:48.123  INFO [网络检测] 网络连接完成，耗时: 49ms
2025-07-25 23:49:53.772  INFO [网络检测] 网络连接完成，耗时: 7ms
2025-07-25 23:49:56.339  INFO [网络检测] 网络连接完成，耗时: 8ms
2025-07-25 23:51:28.488  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 23:51:28.490  INFO 数据库连接管理器创建成功
2025-07-25 23:51:28.524  INFO 数据库连接池创建成功
2025-07-25 23:51:28.529  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 23:52:59.476  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 23:52:59.478  INFO 数据库连接管理器创建成功
2025-07-25 23:52:59.539  INFO 数据库连接池创建成功
2025-07-25 23:52:59.547  INFO 服务器运行在 0.0.0.0:3000
2025-07-25 23:54:55.041  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-25 23:54:55.042  INFO 数据库连接管理器创建成功
2025-07-25 23:54:55.090  INFO 数据库连接池创建成功
2025-07-25 23:54:55.095  INFO 服务器运行在 0.0.0.0:3000
