use crate::app::http::controllers::basic::PlantController;
use crate::app::providers::DatabaseServiceProvider;
use axum::{routing::any, Router};
use std::sync::Arc;

/// 工厂管理路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().merge(
        Router::new()
            .route("/plant/list", any(PlantController::index))
            .route("/plant/page", any(PlantController::page))
            .route("/plant/increase", any(PlantController::store))
            .route("/plant/modify", any(PlantController::update))
            .route("/plant/remove", any(PlantController::destroy)),
    )
}
