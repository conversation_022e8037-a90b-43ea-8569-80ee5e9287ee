use crate::framework::validation::traits::ValidationRule;

/// 必填验证规则
pub struct Required;

impl ValidationRule for Required {
    fn name(&self) -> &'static str {
        "required"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        !value.is_empty()
    }

    fn message(&self) -> &str {
        "字段不能为空"
    }
}

/// 数字验证规则
pub struct Numeric;

impl ValidationRule for Numeric {
    fn name(&self) -> &'static str {
        "numeric"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        value
            .chars()
            .all(|c| c.is_ascii_digit() || c == '.' || c == '-')
    }

    fn message(&self) -> &str {
        "必须是数字"
    }
}

/// 整数验证规则
pub struct Integer;

impl ValidationRule for Integer {
    fn name(&self) -> &'static str {
        "integer"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        value.parse::<i64>().is_ok()
    }

    fn message(&self) -> &str {
        "必须是整数"
    }
}

/// 范围验证规则
pub struct Range {
    min: f64,
    max: f64,
}

impl Range {
    pub fn new(min: f64, max: f64) -> Self {
        Self { min, max }
    }
}

impl ValidationRule for Range {
    fn name(&self) -> &'static str {
        "range"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        if let Ok(num) = value.parse::<f64>() {
            return num >= self.min && num <= self.max;
        }
        false
    }

    fn message(&self) -> &str {
        "数值必须在指定范围内"
    }
}

/// 枚举验证规则
pub struct Enum {
    values: Vec<String>,
}

impl Enum {
    pub fn new(values: Vec<String>) -> Self {
        Self { values }
    }
}

impl ValidationRule for Enum {
    fn name(&self) -> &'static str {
        "enum"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        self.values.contains(&value.to_string())
    }

    fn message(&self) -> &str {
        "值必须在允许的选项中"
    }
}
