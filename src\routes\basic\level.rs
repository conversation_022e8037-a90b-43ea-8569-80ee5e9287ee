use crate::app::http::controllers::basic::LevelController;
use crate::app::providers::DatabaseServiceProvider;
use axum::{routing::any, Router};
use std::sync::Arc;

/// 层级管理路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().merge(
        Router::new()
            .route("/level/list", any(LevelController::index))
            .route("/level/page", any(LevelController::page))
            .route("/level/increase", any(LevelController::store))
            .route("/level/modify", any(LevelController::update))
            .route("/level/remove", any(LevelController::destroy)),
    )
}
