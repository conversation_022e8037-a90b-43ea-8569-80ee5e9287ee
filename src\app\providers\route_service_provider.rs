use crate::app::providers::DatabaseServiceProvider;
use axum::Router;
use std::mem;
use std::sync::Arc;

/// 路由服务提供者
pub struct RouteServiceProvider {
    router: Router<Arc<DatabaseServiceProvider>>,
}

impl RouteServiceProvider {
    /// 创建新的路由服务提供者实例
    pub fn new() -> Self {
        Self {
            router: Router::new(),
        }
    }

    /// 启动路由服务
    pub fn boot(&mut self) -> Router<Arc<DatabaseServiceProvider>> {
        self.configure_rate_limiting();
        self.configure_routes();
        mem::replace(&mut self.router, Router::new())
    }

    /// 配置速率限制
    fn configure_rate_limiting(&self) {
        // TODO: 实现速率限制配置
    }

    /// 配置所有路由
    fn configure_routes(&mut self) {
        let router = mem::replace(&mut self.router, Router::new());
        self.router = router
            .merge(self.web_routes())
            .merge(Router::new().nest("/api", self.api_routes()))
            .merge(Router::new().nest("/channel", self.channel_routes()))
            .merge(Router::new().nest("/console", self.console_routes()));
    }

    /// 映射 API 路由
    fn api_routes(&self) -> Router<Arc<DatabaseServiceProvider>> {
        crate::routes::api::routes()
    }

    /// 映射 Web 路由
    fn web_routes(&self) -> Router<Arc<DatabaseServiceProvider>> {
        crate::routes::web::routes()
    }

    /// 映射 Channel 路由
    fn channel_routes(&self) -> Router<Arc<DatabaseServiceProvider>> {
        crate::routes::channel::routes()
    }

    /// 映射 Console 路由
    fn console_routes(&self) -> Router<Arc<DatabaseServiceProvider>> {
        crate::routes::console::routes()
    }
}
