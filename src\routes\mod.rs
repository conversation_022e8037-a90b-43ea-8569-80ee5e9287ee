use crate::app::providers::DatabaseServiceProvider;
use axum::Router;
use std::sync::Arc;

pub mod api;
pub mod channel;
pub mod console;
pub mod web;

pub mod basic;
pub mod docking;
pub mod login;
pub mod logistics;
pub mod nucleus;
pub mod produce;
pub mod test;

/// 注册所有路由
pub fn register_routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new()
        .nest("/", web::routes())
        .nest("/api", api::routes())
        .nest("/channel", channel::routes())
        .nest("/console", console::routes())
}
