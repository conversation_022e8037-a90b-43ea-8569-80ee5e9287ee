use crate::framework::validation::traits::ValidationRule;
use chrono::NaiveDate;
use chrono::NaiveDateTime;

/// 日期验证规则
pub struct Date {
    format: String,
}

impl Date {
    pub fn new(format: &str) -> Self {
        Self {
            format: format.to_string(),
        }
    }
}

impl ValidationRule for Date {
    fn name(&self) -> &'static str {
        "date"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        NaiveDate::parse_from_str(value, &self.format).is_ok()
    }

    fn message(&self) -> &str {
        "请输入有效的日期"
    }
}

/// 日期时间验证规则
pub struct DateTime {
    format: String,
}

impl DateTime {
    pub fn new(format: &str) -> Self {
        Self {
            format: format.to_string(),
        }
    }
}

impl ValidationRule for DateTime {
    fn name(&self) -> &'static str {
        "datetime"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        NaiveDateTime::parse_from_str(value, &self.format).is_ok()
    }

    fn message(&self) -> &str {
        "请输入有效的日期时间"
    }
}
