use crate::app::http::controllers::test::TestController;
use crate::app::providers::DatabaseServiceProvider;
use axum::{routing::get, Router};
use std::sync::Arc;

/// 测试路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new().merge(
        Router::new()
            .route("/product", get(TestController::product))
            .route("/apply", get(TestController::apply))
            .route("/socket", get(TestController::socket)),
    )
}
