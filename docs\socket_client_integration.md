# Socket客户端连接池集成指南

本文档介绍如何在项目中使用集成了连接池的Socket客户端服务。

## 概述

项目现在提供了两种Socket客户端模式：

1. **单连接模式** (`SocketClientService`) - 传统的单连接管理
2. **连接池模式** (`PooledSocketClientService`) - 基于bb8连接池的高效连接管理

## 架构优势

### 连接池模式优势

- **连接复用**: 避免频繁建立和关闭连接的开销
- **并发支持**: 支持多个并发请求同时使用不同的连接
- **自动管理**: 连接的创建、维护和销毁由连接池自动处理
- **故障恢复**: 连接池会自动检测和替换失效的连接
- **资源控制**: 通过连接池大小限制资源使用

### 单连接模式优势

- **简单直接**: 适合简单的一次性通信场景
- **资源占用少**: 只维护一个连接
- **长连接支持**: 适合需要长时间保持连接的场景

## 快速开始

### 1. 连接池模式（推荐）

```rust
use crate::app::services::socket::pooled_client_service::PooledSocketClientService;

// 创建连接池客户端
let client = PooledSocketClientService::from_env();

// 发送数据并接收响应
let data = b"Hello, Server!";
match client.send_and_receive(data).await {
    Ok(response) => {
        println!("收到响应: {:?}", String::from_utf8_lossy(&response));
    }
    Err(e) => {
        eprintln!("通信失败: {:?}", e);
    }
}
```

### 2. 单连接模式

```rust
use crate::app::services::socket::client_service::{SocketClientService, SocketConnectionMode};

// 创建单连接客户端
let mut client = SocketClientService::from_env();

// 建立连接
client.connect().await?;

// 发送数据并接收响应
let data = b"Hello, Server!";
match client.send_and_receive(data).await {
    Ok(response) => {
        println!("收到响应: {:?}", String::from_utf8_lossy(&response));
    }
    Err(e) => {
        eprintln!("通信失败: {:?}", e);
    }
}

// 关闭连接
client.close().await;
```

## 配置说明

### 环境变量配置

在`.env`文件中配置Socket连接参数：

```env
# Socket服务器配置
SOCKET_HOST=127.0.0.1
SOCKET_PORT=8080
SOCKET_TIMEOUT=5
SOCKET_RECONNECT_INTERVAL=3
SOCKET_MAX_RECONNECT_ATTEMPTS=10
SOCKET_HEARTBEAT_INTERVAL=30
SOCKET_READ_BUFFER_SIZE=8192
SOCKET_WRITE_BUFFER_SIZE=8192
```

### 代码配置

```rust
use crate::app::services::socket::client_service::{SocketClientConfig, SocketConnectionMode};

let config = SocketClientConfig {
    host: "*************".to_string(),
    port: 9090,
    timeout_ms: 5000,
    keep_alive: true,
    keep_alive_interval: 60,
    read_timeout_ms: Some(10000),
    read_buffer_size: 8192,
    write_buffer_size: 8192,
    connection_mode: SocketConnectionMode::Pool, // 或 SocketConnectionMode::Single
};
```

## 高级用法

### 1. 批量发送

```rust
let client = PooledSocketClientService::from_env();

let batch_data = vec![
    b"Message 1".as_slice(),
    b"Message 2".as_slice(),
    b"Message 3".as_slice(),
];

match client.send_batch(&batch_data).await {
    Ok(responses) => {
        println!("批量发送成功，收到 {} 个响应", responses.len());
    }
    Err(e) => {
        eprintln!("批量发送失败: {:?}", e);
    }
}
```

### 2. 只发送数据（不等待响应）

```rust
let client = PooledSocketClientService::from_env();

let notification_data = b"Notification message";
if let Err(e) = client.send_only(notification_data).await {
    eprintln!("发送通知失败: {:?}", e);
}
```

### 3. 并发通信

```rust
let client = PooledSocketClientService::from_env();
let mut handles = Vec::new();

// 创建10个并发任务
for i in 0..10 {
    let client_clone = client.clone();
    let handle = tokio::spawn(async move {
        let message = format!("Concurrent message {}", i);
        client_clone.send_and_receive(message.as_bytes()).await
    });
    handles.push(handle);
}

// 等待所有任务完成
for handle in handles {
    match handle.await {
        Ok(Ok(response)) => println!("任务完成"),
        Ok(Err(e)) => eprintln!("任务失败: {:?}", e),
        Err(e) => eprintln!("任务异常: {:?}", e),
    }
}
```

## 错误处理

### 错误类型

```rust
use crate::app::services::socket::client_service::SocketClientError;

match client.send_and_receive(data).await {
    Ok(response) => { /* 处理响应 */ }
    Err(e) => {
        match e {
            SocketClientError::ConnectionTimeout => {
                eprintln!("连接超时，建议检查网络状况");
            }
            SocketClientError::SendError(msg) => {
                eprintln!("发送失败: {}, 建议重试", msg);
            }
            SocketClientError::ReceiveError(msg) => {
                eprintln!("接收失败: {}, 建议检查服务器状态", msg);
            }
            SocketClientError::NotConnected => {
                eprintln!("未连接到服务器");
            }
            SocketClientError::ConnectionError(msg) => {
                eprintln!("连接错误: {}", msg);
            }
            SocketClientError::IoError(e) => {
                eprintln!("IO错误: {}", e);
            }
        }
    }
}
```

## 性能优化建议

### 1. 选择合适的模式

- **高并发场景**: 使用连接池模式
- **简单通信**: 使用单连接模式
- **长连接**: 使用单连接模式
- **微服务通信**: 使用连接池模式

### 2. 配置优化

```rust
// 针对高并发场景的配置
let config = SocketClientConfig {
    // ... 其他配置
    read_buffer_size: 16384,  // 增大缓冲区
    write_buffer_size: 16384,
    read_timeout_ms: Some(3000), // 减少超时时间
    connection_mode: SocketConnectionMode::Pool,
};
```

### 3. 连接池配置

在`SocketServiceProvider::init()`中调整连接池参数：

```rust
Pool::builder()
    .max_size(20)        // 增加最大连接数
    .min_idle(Some(5))   // 增加最小空闲连接数
    .connection_timeout(Duration::from_secs(3))
    .build(manager)
```

## 监控和调试

### 1. 启用日志

```rust
use tracing::{info, debug, error};

// 客户端操作会自动记录结构化日志
let client = PooledSocketClientService::from_env();
client.send_and_receive(data).await?; // 自动记录操作日志
```

### 2. 连接测试

```rust
let client = PooledSocketClientService::from_env();

// 测试连接池连接
match client.test_connection().await {
    Ok(_) => println!("连接池正常"),
    Err(e) => eprintln!("连接池异常: {:?}", e),
}
```

## 最佳实践

1. **优先使用连接池模式** - 除非有特殊需求
2. **合理设置超时时间** - 避免长时间阻塞
3. **启用结构化日志** - 便于问题排查
4. **错误处理要完整** - 覆盖所有可能的错误情况
5. **配置要合理** - 根据实际场景调整参数
6. **监控连接池状态** - 定期检查连接池健康状况

## 示例代码

完整的使用示例请参考：`src/app/services/socket/examples.rs`

该文件包含了各种使用场景的完整示例代码。

## 故障排除

### 常见问题

1. **连接超时**: 检查网络连接和服务器状态
2. **连接池获取失败**: 检查连接池配置和服务器负载
3. **数据发送失败**: 检查数据格式和服务器协议
4. **响应接收超时**: 检查服务器处理时间和网络延迟

### 调试步骤

1. 启用详细日志记录
2. 使用连接测试功能
3. 检查网络连接状况
4. 验证服务器端配置
5. 调整客户端超时参数
