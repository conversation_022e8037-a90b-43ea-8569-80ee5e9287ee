/// Socket客户端服务使用示例
/// 
/// 本文件展示了如何在实际项目中使用Socket客户端服务，
/// 包括单连接模式和连接池模式的对比使用。

use crate::app::services::socket::{
    client_service::{SocketClientService, SocketClientConfig, SocketConnectionMode},
    pooled_client_service::PooledSocketClientService,
};
use tracing::{info, error};

/// 单连接模式使用示例
/// 
/// 适用场景：
/// - 简单的一次性通信
/// - 长时间保持连接的场景
/// - 对连接数有严格限制的环境
pub async fn single_connection_example() -> Result<(), Box<dyn std::error::Error>> {
    info!("=== 单连接模式示例 ===");

    // 1. 创建客户端配置
    let config = SocketClientConfig {
        host: "127.0.0.1".to_string(),
        port: 8080,
        timeout_ms: 5000,
        keep_alive: true,
        keep_alive_interval: 60,
        read_timeout_ms: Some(10000),
        read_buffer_size: 8192,
        write_buffer_size: 8192,
        connection_mode: SocketConnectionMode::Single,
    };

    // 2. 创建客户端实例
    let mut client = SocketClientService::new(config);

    // 3. 建立连接
    client.connect().await?;
    info!("连接建立成功");

    // 4. 发送数据并接收响应
    let request_data = b"Hello, Server!";
    match client.send_and_receive(request_data).await {
        Ok(response) => {
            info!("收到响应: {:?}", String::from_utf8_lossy(&response));
        }
        Err(e) => {
            error!("通信失败: {:?}", e);
        }
    }

    // 5. 关闭连接
    client.close().await;
    info!("连接已关闭");

    Ok(())
}

/// 连接池模式使用示例
/// 
/// 适用场景：
/// - 高并发通信
/// - 频繁的短连接通信
/// - 需要连接复用的场景
/// - 微服务间通信
pub async fn connection_pool_example() -> Result<(), Box<dyn std::error::Error>> {
    info!("=== 连接池模式示例 ===");

    // 1. 创建连接池客户端（无需手动连接）
    let client = PooledSocketClientService::from_env();

    // 2. 直接发送数据并接收响应
    let request_data = b"Hello from Pool!";
    match client.send_and_receive(request_data).await {
        Ok(response) => {
            info!("收到响应: {:?}", String::from_utf8_lossy(&response));
        }
        Err(e) => {
            error!("通信失败: {:?}", e);
        }
    }

    // 3. 批量发送示例
    let batch_data = vec![
        b"Message 1".as_slice(),
        b"Message 2".as_slice(),
        b"Message 3".as_slice(),
    ];

    match client.send_batch(&batch_data).await {
        Ok(responses) => {
            info!("批量发送成功，收到 {} 个响应", responses.len());
            for (i, response) in responses.iter().enumerate() {
                info!("响应 {}: {:?}", i + 1, String::from_utf8_lossy(response));
            }
        }
        Err(e) => {
            error!("批量发送失败: {:?}", e);
        }
    }

    // 4. 只发送数据（不等待响应）
    let notification_data = b"Notification message";
    if let Err(e) = client.send_only(notification_data).await {
        error!("发送通知失败: {:?}", e);
    } else {
        info!("通知发送成功");
    }

    info!("连接池模式示例完成（无需手动关闭连接）");

    Ok(())
}

/// 并发通信示例
/// 
/// 展示连接池模式在并发场景下的优势
pub async fn concurrent_communication_example() -> Result<(), Box<dyn std::error::Error>> {
    info!("=== 并发通信示例 ===");

    let client = PooledSocketClientService::from_env();
    let mut handles = Vec::new();

    // 创建10个并发任务
    for i in 0..10 {
        let client_clone = client.clone();
        let handle = tokio::spawn(async move {
            let message = format!("Concurrent message {}", i);
            match client_clone.send_and_receive(message.as_bytes()).await {
                Ok(response) => {
                    info!("任务 {} 完成: {:?}", i, String::from_utf8_lossy(&response));
                }
                Err(e) => {
                    error!("任务 {} 失败: {:?}", i, e);
                }
            }
        });
        handles.push(handle);
    }

    // 等待所有任务完成
    for handle in handles {
        let _ = handle.await;
    }

    info!("并发通信示例完成");

    Ok(())
}

/// 性能对比示例
/// 
/// 对比单连接模式和连接池模式的性能差异
pub async fn performance_comparison_example() -> Result<(), Box<dyn std::error::Error>> {
    info!("=== 性能对比示例 ===");

    let test_data = b"Performance test data";
    let iterations = 100;

    // 测试单连接模式
    info!("测试单连接模式性能...");
    let start = std::time::Instant::now();
    
    let mut single_client = SocketClientService::from_env();
    if single_client.connect().await.is_ok() {
        for i in 0..iterations {
            if let Err(e) = single_client.send_and_receive(test_data).await {
                error!("单连接模式第 {} 次请求失败: {:?}", i, e);
                break;
            }
        }
        single_client.close().await;
    }
    
    let single_duration = start.elapsed();
    info!("单连接模式完成 {} 次请求，耗时: {:?}", iterations, single_duration);

    // 测试连接池模式
    info!("测试连接池模式性能...");
    let start = std::time::Instant::now();
    
    let pool_client = PooledSocketClientService::from_env();
    for i in 0..iterations {
        if let Err(e) = pool_client.send_and_receive(test_data).await {
            error!("连接池模式第 {} 次请求失败: {:?}", i, e);
            break;
        }
    }
    
    let pool_duration = start.elapsed();
    info!("连接池模式完成 {} 次请求，耗时: {:?}", iterations, pool_duration);

    // 性能对比
    if pool_duration < single_duration {
        let improvement = ((single_duration.as_millis() as f64 - pool_duration.as_millis() as f64) 
                          / single_duration.as_millis() as f64) * 100.0;
        info!("连接池模式性能提升: {:.2}%", improvement);
    } else {
        let degradation = ((pool_duration.as_millis() as f64 - single_duration.as_millis() as f64) 
                          / single_duration.as_millis() as f64) * 100.0;
        info!("连接池模式性能下降: {:.2}%", degradation);
    }

    Ok(())
}

/// 错误处理示例
/// 
/// 展示如何处理各种Socket通信错误
pub async fn error_handling_example() -> Result<(), Box<dyn std::error::Error>> {
    info!("=== 错误处理示例 ===");

    let client = PooledSocketClientService::from_env();

    // 1. 连接测试
    match client.test_connection().await {
        Ok(_) => info!("连接测试成功"),
        Err(e) => {
            error!("连接测试失败: {:?}", e);
            return Ok(()); // 如果连接测试失败，直接返回
        }
    }

    // 2. 处理发送错误
    let invalid_data = vec![0u8; 1024 * 1024]; // 1MB数据，可能导致超时
    match client.send_and_receive(&invalid_data).await {
        Ok(response) => {
            info!("大数据发送成功，响应长度: {}", response.len());
        }
        Err(e) => {
            error!("大数据发送失败: {:?}", e);
            // 根据错误类型进行不同处理
            match e {
                crate::app::services::socket::client_service::SocketClientError::ConnectionTimeout => {
                    error!("连接超时，建议检查网络状况");
                }
                crate::app::services::socket::client_service::SocketClientError::SendError(_) => {
                    error!("发送失败，建议重试");
                }
                crate::app::services::socket::client_service::SocketClientError::ReceiveError(_) => {
                    error!("接收失败，建议检查服务器状态");
                }
                _ => {
                    error!("其他错误，建议检查配置");
                }
            }
        }
    }

    info!("错误处理示例完成");

    Ok(())
}

/// 配置示例
/// 
/// 展示如何使用不同的配置创建客户端
pub async fn configuration_example() -> Result<(), Box<dyn std::error::Error>> {
    info!("=== 配置示例 ===");

    // 1. 使用环境变量配置
    let env_client = PooledSocketClientService::from_env();
    info!("使用环境变量配置创建客户端");

    // 2. 使用自定义配置
    let custom_client = PooledSocketClientService::with_config("192.168.1.100", 9090);
    info!("使用自定义配置创建客户端");

    // 3. 使用全局配置
    let global_client = PooledSocketClientService::global();
    info!("使用全局配置创建客户端");

    // 测试不同配置的客户端
    let test_data = b"Configuration test";
    
    for (name, client) in [
        ("环境变量", &env_client),
        ("自定义", &custom_client),
        ("全局", &global_client),
    ] {
        match client.send_and_receive(test_data).await {
            Ok(_) => info!("{} 配置客户端测试成功", name),
            Err(e) => error!("{} 配置客户端测试失败: {:?}", name, e),
        }
    }

    info!("配置示例完成");

    Ok(())
}

/// 运行所有示例
pub async fn run_all_examples() -> Result<(), Box<dyn std::error::Error>> {
    info!("开始运行Socket客户端服务示例");

    // 注意：在实际使用中，请确保Socket服务器已启动
    // 这些示例假设服务器运行在 127.0.0.1:8080

    single_connection_example().await?;
    connection_pool_example().await?;
    concurrent_communication_example().await?;
    performance_comparison_example().await?;
    error_handling_example().await?;
    configuration_example().await?;

    info!("所有示例运行完成");

    Ok(())
}
