use serde::Deserialize;
use std::env;

/// 邮件配置结构体
#[derive(Debu<PERSON>, <PERSON><PERSON>, Deserialize)]
pub struct MailConfig {
    /// 邮件驱动
    pub mailer: String,
    /// SMTP主机
    pub host: String,
    /// SMTP端口
    pub port: u16,
    /// SMTP用户名
    pub username: String,
    /// SMTP密码
    pub password: String,
    /// 加密方式
    pub encryption: String,
    /// 发件人地址
    pub from_address: String,
    /// 发件人名称
    pub from_name: String,
}

impl MailConfig {
    /// 从环境变量加载邮件配置
    pub fn from_env() -> Self {
        Self {
            mailer: env::var("MAIL_MAILER").unwrap_or_else(|_| "smtp".to_string()),
            host: env::var("MAIL_HOST").unwrap_or_else(|_| "smtp.qq.com".to_string()),
            port: env::var("MAIL_PORT")
                .unwrap_or_else(|_| "465".to_string())
                .parse()
                .unwrap_or(465),
            username: env::var("MAIL_USERNAME").unwrap_or_else(|_| "".to_string()),
            password: env::var("MAIL_PASSWORD").unwrap_or_else(|_| "".to_string()),
            encryption: env::var("MAIL_ENCRYPTION").unwrap_or_else(|_| "ssl".to_string()),
            from_address: env::var("MAIL_FROM_ADDRESS").unwrap_or_else(|_| "".to_string()),
            from_name: env::var("MAIL_FROM_NAME").unwrap_or_else(|_| "".to_string()),
        }
    }
}
