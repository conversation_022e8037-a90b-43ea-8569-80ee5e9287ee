use super::manager::{get_facade_manager, Facade};
use crate::bootstrap::app::Application;
use std::sync::Arc;

/// App Facade
pub struct App;

impl App {
    /// 获取应用实例
    pub fn get() -> Arc<Application> {
        Self::get_instance()
            .downcast::<Application>()
            .expect("无法获取应用实例")
    }

    /// 获取应用名称
    pub fn name() -> String {
        Self::get().app_config.name.clone()
    }

    /// 获取应用环境
    pub fn env() -> String {
        Self::get().app_config.env.clone()
    }

    /// 获取应用调试模式
    pub fn debug() -> bool {
        Self::get().app_config.debug
    }

    /// 获取应用 URL
    pub fn url() -> String {
        let config = &Self::get().app_config;
        format!("http://{}:{}", config.host, config.port)
    }
}

impl Facade for App {
    fn get_instance() -> Arc<dyn std::any::Any + Send + Sync> {
        get_facade_manager()
            .lock()
            .expect("无法获取 Facade 管理器锁")
            .get::<Application>()
            .expect("应用实例未注册")
    }
}
