use crate::app::http::controllers::login::LoginController;
use crate::app::providers::DatabaseServiceProvider;
use axum::{
    routing::{get, post},
    Router,
};
use std::sync::Arc;

/// 登录相关路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new()
        .route("/captcha", get(LoginController::captcha))
        .route("/login", post(LoginController::login))
        .route("/logout", post(LoginController::logout))
}
