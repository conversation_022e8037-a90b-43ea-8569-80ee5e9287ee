use serde::Deserialize;
use std::env;

/// Redis配置结构体
#[derive(Debu<PERSON>, <PERSON><PERSON>, Deserialize)]
pub struct RedisConfig {
    /// Redis主机地址
    pub host: String,
    /// Redis端口
    pub port: u16,
    /// Redis密码
    pub password: Option<String>,
    /// Redis数据库
    pub db: i32,
    /// 连接池最大连接数
    pub pool_max_size: u32,
    /// 连接池最小空闲连接数
    pub pool_min_idle: u32,
    /// 连接超时时间（秒）
    pub pool_timeout: u64,
}

impl RedisConfig {
    /// 从环境变量加载Redis配置
    pub fn from_env() -> Self {
        Self {
            host: env::var("REDIS_HOST").unwrap_or_else(|_| "127.0.0.1".to_string()),
            port: env::var("REDIS_PORT")
                .unwrap_or_else(|_| "6379".to_string())
                .parse()
                .unwrap_or(6379),
            password: env::var("REDIS_PASSWORD").ok(),
            db: env::var("REDIS_DB")
                .unwrap_or_else(|_| "0".to_string())
                .parse()
                .unwrap_or(0),
            pool_max_size: env::var("REDIS_POOL_MAX_SIZE")
                .unwrap_or_else(|_| "10".to_string())
                .parse()
                .unwrap_or(10),
            pool_min_idle: env::var("REDIS_POOL_MIN_IDLE")
                .unwrap_or_else(|_| "2".to_string())
                .parse()
                .unwrap_or(2),
            pool_timeout: env::var("REDIS_POOL_TIMEOUT")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .unwrap_or(30),
        }
    }

    /// 获取Redis连接URL
    pub fn connection_url(&self) -> String {
        let auth = if let Some(password) = &self.password {
            format!(":{}@", password)
        } else {
            String::new()
        };
        format!("redis://{}{}:{}/{}", auth, self.host, self.port, self.db)
    }
}
