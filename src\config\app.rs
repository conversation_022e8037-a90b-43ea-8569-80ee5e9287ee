use serde::Deserialize;
use std::env;

/// 应用配置结构体
#[derive(Debug, <PERSON><PERSON>, Deserialize)]
pub struct AppConfig {
    /// 应用名称
    pub name: String,
    /// 应用环境
    pub env: String,
    /// 应用密钥
    pub key: String,
    /// 调试模式
    pub debug: bool,
    /// 应用主机
    pub host: String,
    /// 应用端口
    pub port: u16,
    /// 时区
    pub timezone: String,
    /// 语言
    pub locale: String,
}

impl AppConfig {
    /// 从环境变量加载应用配置
    pub fn from_env() -> Self {
        let name = env::var("APP_NAME").unwrap_or_else(|_| "Rustravel".to_string());
        let env = env::var("APP_ENV").unwrap_or_else(|_| "local".to_string());
        let key = env::var("APP_KEY").unwrap_or_else(|_| "".to_string());
        let debug = env::var("APP_DEBUG")
            .unwrap_or_else(|_| "false".to_string())
            .parse()
            .unwrap_or(false);
        let host = env::var("APP_HOST").unwrap_or_else(|_| "0.0.0.0".to_string());
        let port = env::var("APP_PORT")
            .unwrap_or_else(|_| "3000".to_string())
            .parse()
            .expect("APP_PORT 必须是数字");
        let timezone = env::var("TIMEZONE").unwrap_or_else(|_| "UTC".to_string());
        let locale = env::var("APP_LOCALE").unwrap_or_else(|_| "en".to_string());

        AppConfig {
            name,
            env,
            key,
            debug,
            host,
            port,
            timezone,
            locale,
        }
    }
}
