use serde::Deserialize;
use std::env;

/// 日志配置结构体
#[derive(Debu<PERSON>, <PERSON><PERSON>, Deserialize)]
pub struct LoggingConfig {
    /// 日志通道
    pub channel: String,
    /// 日志级别
    pub level: String,
    /// 日志文件路径
    pub path: String,
    /// 日志文件最大大小（MB）
    pub max_size: u64,
    /// 日志文件保留天数
    pub max_age: u64,
    /// 日志文件最大数量
    pub max_backups: u64,
    /// 是否压缩
    pub compress: bool,
}

impl LoggingConfig {
    /// 从环境变量加载日志配置
    pub fn from_env() -> Self {
        Self {
            channel: env::var("LOG_CHANNEL").unwrap_or_else(|_| "stack".to_string()),
            level: env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string()),
            path: env::var("LOG_PATH").unwrap_or_else(|_| "src/storage/logs".to_string()),
            max_size: env::var("LOG_MAX_SIZE")
                .unwrap_or_else(|_| "100".to_string())
                .parse()
                .unwrap_or(100),
            max_age: env::var("LOG_MAX_AGE")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .unwrap_or(30),
            max_backups: env::var("LOG_MAX_BACKUPS")
                .unwrap_or_else(|_| "10".to_string())
                .parse()
                .unwrap_or(10),
            compress: env::var("LOG_COMPRESS")
                .unwrap_or_else(|_| "true".to_string())
                .parse()
                .unwrap_or(true),
        }
    }
}
