use crate::app::models::basic::product::Product;
use crate::app::models::transit::code::Code;
use crate::app::providers::DatabaseServiceProvider;
use std::sync::Arc;
use tiberius::{Client, ToSql};
use tokio::net::TcpStream;
use tokio_util::compat::Compat;
use tracing::error;

/// 单据服务 - 提供各种数据库操作功能

/// 处理批次数据，获取产品信息和码信息
pub async fn process_batch(
    db: &Arc<DatabaseServiceProvider>,
    batch: &serde_json::Value,
    product_id: i32,
    order_id: i32,
    batch_no: &str,
) -> serde_json::Value {
    let mut batch_data = batch.clone();

    // 获取产品信息
    if let Ok(mut product_conn) = db.get_connection().await {
        if let Some(product_info) = get_product_info(&mut product_conn, product_id).await {
            batch_data["productInfo"] = product_info;
        }
    }

    // 获取托盘码信息
    if let Ok(mut code_conn) = db.get_connection().await {
        let code_info = get_code_info(&mut code_conn, order_id, batch_no).await;
        if !code_info.is_empty() {
            batch_data["codeInfo"] = serde_json::json!(code_info);
        }
    }

    batch_data
}

/// 获取单据编号
pub async fn get_ware_code(
    client: &mut Client<Compat<TcpStream>>,
    ware_order_id: i32,
) -> Result<String, String> {
    let result = match client
        .query(
            "SELECT wareCode FROM ware_order WITH (NOLOCK) WHERE id = @P1",
            &[&ware_order_id as &dyn ToSql],
        )
        .await
    {
        Ok(result) => match result.into_first_result().await {
            Ok(rows) => rows,
            Err(e) => return Err(format!("解析查询结果失败: {}", e)),
        },
        Err(e) => return Err(format!("查询单据编号失败: {}", e)),
    };

    if result.is_empty() {
        return Err("单据不存在".to_string());
    }

    Ok(result[0]
        .get::<&str, _>("wareCode")
        .unwrap_or_default()
        .to_string())
}

/// 检查重复码
pub async fn check_duplicate_code(
    client: &mut Client<Compat<TcpStream>>,
    ware_order_id: i32,
    code: &str,
) -> Result<(), String> {
    let result = match client
        .query(
            "SELECT id FROM ware_code WITH (NOLOCK) WHERE wareID = @P1 AND code = @P2",
            &[&ware_order_id as &dyn ToSql, &code as &dyn ToSql],
        )
        .await
    {
        Ok(result) => match result.into_first_result().await {
            Ok(rows) => rows,
            Err(e) => return Err(format!("解析查询结果失败: {}", e)),
        },
        Err(e) => return Err(format!("查询重复码失败: {}", e)),
    };

    if !result.is_empty() {
        return Err("此码重复上传了".to_string());
    }

    Ok(())
}

/// 验证码有效性
pub async fn validate_code(
    client: &mut Client<Compat<TcpStream>>,
    code: &str,
) -> Result<(u8, i32, String, String, i32, String, i32), String> {
    // 1. 查询码信息
    let code_info_rows = match client.query(
        "SELECT id, levelCode, codeFlag, orderId, batchNo, poNumber, amount FROM transit_code WITH (NOLOCK) WHERE code = @P1",
        &[&code as &dyn ToSql],
    ).await {
        Ok(result) => match result.into_first_result().await {
            Ok(rows) => rows,
            Err(e) => return Err(format!("解析查询结果失败: {}", e)),
        },
        Err(e) => return Err(format!("查询码信息失败: {}", e)),
    };

    if code_info_rows.is_empty() {
        return Err("没有码信息".to_string());
    }

    // 2. 检查码类型
    let level_code = code_info_rows[0].get::<u8, _>("levelCode").unwrap_or(0);
    if level_code != 4 {
        return Err("必须是托盘码".to_string());
    }

    // 3. 检查码状态
    let code_flag = code_info_rows[0].get::<u8, _>("codeFlag").unwrap_or(0);
    if code_flag != 2 {
        return Err("码状态错误,必须为已生产的码".to_string());
    }

    // 4. 获取相关信息
    let order_id = code_info_rows[0].get::<i32, _>("orderId").unwrap_or(0);
    let batch_no = code_info_rows[0]
        .get::<&str, _>("batchNo")
        .unwrap_or_default()
        .to_string();
    let po_number = code_info_rows[0]
        .get::<&str, _>("poNumber")
        .unwrap_or_default()
        .to_string();
    let amount = code_info_rows[0].get::<i32, _>("amount").unwrap_or(0);

    // 5. 检查生产订单
    let order_rows = match client
        .query(
            "SELECT id, estate, productId FROM produce_order WITH (NOLOCK) WHERE id = @P1",
            &[&order_id as &dyn ToSql],
        )
        .await
    {
        Ok(result) => match result.into_first_result().await {
            Ok(rows) => rows,
            Err(e) => return Err(format!("解析查询结果失败: {}", e)),
        },
        Err(e) => return Err(format!("查询生产订单失败: {}", e)),
    };

    if order_rows.is_empty() {
        return Err("生产订单不存在".to_string());
    }

    // 6. 检查订单状态
    let order_status = order_rows[0].get::<u8, _>("estate").unwrap_or(0);
    if order_status <= 3 {
        return Err("生产订单状态必须为正在生产".to_string());
    }

    // 7. 获取产品ID
    let product_id = order_rows[0].get::<i32, _>("productId").unwrap_or(0);

    // 8. 获取层级名称
    let level_name = get_level_name(client, level_code).await;

    Ok((
        level_code, order_id, batch_no, po_number, amount, level_name, product_id,
    ))
}

/// 获取层级名称
pub async fn get_level_name(client: &mut Client<Compat<TcpStream>>, level_code: u8) -> String {
    let rows = match client
        .query(
            "SELECT levelName FROM basic_level WITH (NOLOCK) WHERE levelCode = @P1",
            &[&(level_code as i32) as &dyn ToSql],
        )
        .await
    {
        Ok(result) => match result.into_first_result().await {
            Ok(rows) => rows,
            Err(_) => return "托盘".to_string(),
        },
        Err(_) => return "托盘".to_string(),
    };

    if !rows.is_empty() {
        rows[0]
            .get::<&str, _>("levelName")
            .unwrap_or_default()
            .to_string()
    } else {
        "托盘".to_string()
    }
}

/// 检查订单详情
pub async fn check_order_detail(
    client: &mut Client<Compat<TcpStream>>,
    order_id: i32,
    batch_no: &str,
) -> Result<i32, String> {
    let rows = match client
        .query(
            "SELECT id FROM ware_order_detail WITH (NOLOCK) WHERE orderId = @P1 AND batchNo = @P2",
            &[&order_id as &dyn ToSql, &batch_no as &dyn ToSql],
        )
        .await
    {
        Ok(result) => match result.into_first_result().await {
            Ok(rows) => rows,
            Err(e) => return Err(format!("解析查询结果失败: {}", e)),
        },
        Err(e) => return Err(format!("查询出库批号失败: {}", e)),
    };

    if rows.is_empty() {
        return Err("出库批号不存在".to_string());
    }

    Ok(rows[0].get::<i32, _>("id").unwrap_or(0))
}

/// 插入ware_code记录
pub async fn insert_ware_code(
    client: &mut Client<Compat<TcpStream>>,
    ware_code: &str,
    code: &str,
    level_code: u8,
    level_name: &str,
    type_flag: i32,
    product_id: i32,
    order_id: i32,
    batch_no: &str,
    po_number: &str,
    scan_time: &str,
    ware_id: i32,
) -> Result<(), String> {
    match client.query(
        "INSERT INTO ware_code (wareCode, code, levelCode, levelName, typeFlag, productID, orderID, batchNo, poNumber, scanTime, wareID) VALUES (@P1, @P2, @P3, @P4, @P5, @P6, @P7, @P8, @P9, @P10, @P11)",
        &[
            &ware_code as &dyn ToSql,
            &code as &dyn ToSql,
            &(level_code as i32) as &dyn ToSql,
            &level_name as &dyn ToSql,
            &type_flag as &dyn ToSql,
            &product_id as &dyn ToSql,
            &order_id as &dyn ToSql,
            &batch_no as &dyn ToSql,
            &po_number as &dyn ToSql,
            &scan_time as &dyn ToSql,
            &ware_id as &dyn ToSql,
        ],
    ).await {
        Ok(_) => Ok(()),
        Err(e) => Err(format!("插入ware_code记录失败: {}", e)),
    }
}

/// 更新订单详情
pub async fn update_order_detail(
    client: &mut Client<Compat<TcpStream>>,
    amount: i32,
    detail_id: i32,
) -> Result<(), String> {
    match client.query(
        "UPDATE ware_order_detail SET L4Amount = L4Amount + 1, amount = amount + @P1 WHERE id = @P2",
        &[&amount as &dyn ToSql, &detail_id as &dyn ToSql],
    ).await {
        Ok(_) => Ok(()),
        Err(e) => Err(format!("更新订单详情失败: {}", e)),
    }
}

/// 更新订单状态
pub async fn update_order_status(
    client: &mut Client<Compat<TcpStream>>,
    channel: i32,
    ware_order_id: i32,
) -> Result<(), String> {
    match client
        .query(
            "UPDATE ware_order SET channel = @P1, estate = 2 WHERE id = @P2",
            &[&channel as &dyn ToSql, &ware_order_id as &dyn ToSql],
        )
        .await
    {
        Ok(_) => Ok(()),
        Err(e) => Err(format!("更新订单状态失败: {}", e)),
    }
}

/// 记录响应日志
pub async fn record_response_log(
    client: &mut Client<Compat<TcpStream>>,
    request_log: &str,
    response_log: &str,
    client_ip: &str,
    result: &str,
    log_type: &str,
) -> Result<(), String> {
    match client.query(
        "INSERT INTO wms_api_log (requestLog, responseLog, api, ip, result, type) VALUES (@P1, @P2, @P3, @P4, @P5, @P6)",
        &[
            &request_log as &dyn ToSql,
            &response_log as &dyn ToSql,
            &"api/produce/order/ware" as &dyn ToSql,
            &client_ip as &dyn ToSql,
            &result as &dyn ToSql,
            &log_type as &dyn ToSql,
        ],
    ).await {
        Ok(_) => Ok(()),
        Err(e) => Err(format!("记录响应日志失败: {}", e)),
    }
}

/// 获取产品信息
pub async fn get_product_info(
    client: &mut Client<Compat<TcpStream>>,
    product_id: i32,
) -> Option<serde_json::Value> {
    let sql = r#"
        SELECT productName, productCode, ndc, recipeNo 
        FROM basic_product WITH (NOLOCK)
        WHERE id = @P1
    "#;

    let params: Vec<&dyn ToSql> = vec![&product_id];

    match client.query(sql, &params).await {
        Ok(result) => {
            match result.into_row().await {
                Ok(Some(row)) => {
                    // 使用Product的renovation方法获取数据
                    let product = Product::renovation(&row);
                    Some(product.to_response())
                }
                _ => None,
            }
        }
        Err(e) => {
            error!(
                "[单据详情] 查询产品信息失败: {}, product_id={}",
                e, product_id
            );
            None
        }
    }
}

/// 获取托盘码信息
pub async fn get_code_info(
    client: &mut Client<Compat<TcpStream>>,
    order_id: i32,
    batch_no: &str,
) -> Vec<serde_json::Value> {
    let mut result = Vec::new();

    // 查询托盘码信息，使用JOIN同时获取小瓶数量
    let sql = r#"
        SELECT 
            t1.code, t1.amount, t1.levelCode, t1.resCode, 
            (SELECT COUNT(*) 
             FROM transit_code WITH (NOLOCK)
             WHERE orderId = @P1 
             AND batchNo = @P2 
             AND levelCode = 1 
             AND codeFlag = 2 
             AND palletCode = t1.code) as bottleCount
        FROM transit_code t1 WITH (NOLOCK)
        WHERE t1.orderId = @P1 
        AND t1.batchNo = @P2 
        AND t1.levelCode = 4 
        AND t1.codeFlag = 2 
        AND (t1.palletCode IS NOT NULL AND t1.palletCode <> '')
    "#;

    let params: Vec<&dyn ToSql> = vec![&order_id, &batch_no];

    match client.query(sql, &params).await {
        Ok(result_set) => {
            match result_set.into_results().await {
                Ok(rows_sets) => {
                    if let Some(rows) = rows_sets.first() {
                        for row in rows {
                            let code_model = Code::renovation(row);
                            let mut code_data = code_model.to_response();

                            // 添加瓶子数量
                            let bottle_count = row.get::<i32, _>("bottleCount").unwrap_or(0);
                            code_data["bottleCount"] = serde_json::json!(bottle_count);

                            result.push(code_data);
                        }
                    }
                }
                Err(e) => {
                    error!(
                        "[单据详情] 解析托盘码结果失败: {}, order_id={}, batch_no={}",
                        e, order_id, batch_no
                    );
                }
            }
        }
        Err(e) => {
            error!(
                "[单据详情] 查询托盘码信息失败: {}, order_id={}, batch_no={}",
                e, order_id, batch_no
            );
        }
    }

    result
}

/// 批量获取小瓶数量
pub async fn get_batch_bottle_counts(
    client: &mut Client<Compat<TcpStream>>,
    order_id: i32,
    batch_no: &str,
    pallet_codes: &[String],
) -> std::collections::HashMap<String, i32> {
    if pallet_codes.is_empty() {
        return std::collections::HashMap::new();
    }

    // 构建IN子句的参数
    let pallet_codes_list = pallet_codes
        .iter()
        .map(|code| format!("'{}'", code.replace('\'', "''")))
        .collect::<Vec<_>>()
        .join(",");

    let sql = format!(
        r#"
        SELECT palletCode, COUNT(*) as bottleCount
        FROM transit_code WITH (NOLOCK)
        WHERE orderId = @P1 
        AND batchNo = @P2 
        AND levelCode = 1 
        AND codeFlag = 2 
        AND palletCode IN ({})
        GROUP BY palletCode
        "#,
        pallet_codes_list
    );

    let params: Vec<&dyn ToSql> = vec![&order_id, &batch_no];
    let mut result = std::collections::HashMap::new();

    match client.query(&sql, &params).await {
        Ok(result_set) => match result_set.into_results().await {
            Ok(rows_sets) => {
                if let Some(rows) = rows_sets.first() {
                    for row in rows {
                        if let Some(pallet_code) = row.get::<&str, _>("palletCode") {
                            let count = row.get::<i32, _>("bottleCount").unwrap_or(0);
                            result.insert(pallet_code.to_string(), count);
                        }
                    }
                }
            }
            Err(e) => {
                error!(
                    "[单据详情] 解析小瓶数量结果失败: {}, order_id={}, batch_no={}",
                    e, order_id, batch_no
                );
            }
        },
        Err(e) => {
            error!(
                "[单据详情] 批量查询小瓶数量失败: {}, order_id={}, batch_no={}",
                e, order_id, batch_no
            );
        }
    }

    result
}
