2025-07-19 07:18:51.469  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-19 07:18:51.470  INFO 数据库连接管理器创建成功
2025-07-19 07:18:51.498  INFO 数据库连接池创建成功
2025-07-19 07:18:51.502  INFO 服务器运行在 0.0.0.0:3000
2025-07-19 07:18:54.077  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-19 07:18:54.078  INFO 数据库连接管理器创建成功
2025-07-19 07:18:54.103  INFO 数据库连接池创建成功
2025-07-19 07:18:54.108  INFO 服务器运行在 0.0.0.0:3000
2025-07-19 07:29:46.376  INFO 网络连接完成，耗时: 87ms
2025-07-19 07:30:10.714  INFO [查询序列码] 开始查询序列码信息
2025-07-19 07:30:10.723  INFO [查询序列码] 数据库连接获取成功
2025-07-19 07:30:10.735  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 07:30:10.742  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 07:30:10.743  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 07:30:17.514  INFO [取出] 开始取出操作流程
2025-07-19 07:30:17.515  INFO [取出] 接收到请求参数: code=015034354727603921100000127123
2025-07-19 07:30:17.524  INFO [取出] 数据库连接获取成功
2025-07-19 07:30:17.526  INFO [取出] 开始查询序列码: 015034354727603921100000127123
2025-07-19 07:30:17.529  INFO [取出] 查询到序列码信息: code_flag=2, level_code=3, zero_box=0, batch_no=R202500040
2025-07-19 07:30:17.529  INFO [取出] 序列码状态正常: code_flag=2
2025-07-19 07:30:17.530  INFO [取出] 开始判断箱码是否符合取出条件
2025-07-19 07:30:17.531  INFO [取出] 大箱关联的托盘码: 00703435470002290297
2025-07-19 07:30:17.531  INFO [取出] 开始从托盘中取出箱码
2025-07-19 07:30:17.532  INFO [取出] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount - 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290297'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-19 07:30:17.538  INFO [取出] 更新托盘码信息成功
2025-07-19 07:30:17.538  INFO [取出] 开始执行解除箱码与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '015034354727603921100000127123'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-19 07:30:17.542  INFO [取出] 解除箱码与托盘的绑定成功
2025-07-19 07:30:17.543  INFO [取出] 开始执行解除小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '015034354727603921100000127123'
              AND boxCode = '015034354727603921100000127123'
              AND palletCode = '00703435470002290297'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-19 07:30:17.569  INFO [取出] 解除小瓶与托盘的绑定成功
2025-07-19 07:30:17.571  INFO [取出] 事务提交成功，取出操作完成
2025-07-19 07:34:33.572  INFO [查询序列码] 开始查询序列码信息
2025-07-19 07:34:33.582  INFO [查询序列码] 数据库连接获取成功
2025-07-19 07:34:33.585  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 07:34:33.587  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 07:34:33.588  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290334, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 08:30:04.534  INFO [查询序列码] 开始查询序列码信息
2025-07-19 08:30:04.564  INFO [查询序列码] 数据库连接获取成功
2025-07-19 08:30:04.588  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 08:30:04.594  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 08:30:04.595  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290334, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 08:49:46.295  INFO [查询序列码] 开始查询序列码信息
2025-07-19 08:49:46.313  INFO [查询序列码] 数据库连接获取成功
2025-07-19 08:49:46.326  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-19 08:49:46.332  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-19 08:49:46.333  INFO [查询序列码] 查询序列码信息完成: code=00703435470002346529, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-19 08:49:53.563  INFO [查询序列码] 开始查询序列码信息
2025-07-19 08:49:53.571  INFO [查询序列码] 数据库连接获取成功
2025-07-19 08:49:53.576  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-19 08:49:53.578  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-19 08:49:53.578  INFO [查询序列码] 查询序列码信息完成: code=00703435470002346529, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-19 09:10:16.847  INFO [查询序列码] 开始查询序列码信息
2025-07-19 09:10:16.862  INFO [查询序列码] 数据库连接获取成功
2025-07-19 09:10:16.884  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-19 09:10:16.888  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-19 09:10:16.889  INFO [查询序列码] 查询序列码信息完成: code=00703435470002346529, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-19 09:10:33.539  INFO [查询序列码] 开始查询序列码信息
2025-07-19 09:10:33.546  INFO [查询序列码] 数据库连接获取成功
2025-07-19 09:10:33.550  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 09:10:33.551  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 09:10:33.552  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290334, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 09:10:37.052  INFO [查询序列码] 开始查询序列码信息
2025-07-19 09:10:37.063  INFO [查询序列码] 数据库连接获取成功
2025-07-19 09:10:37.066  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 09:10:37.069  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 09:10:37.070  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290334, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 09:11:08.888  INFO [查询序列码] 开始查询序列码信息
2025-07-19 09:11:21.392  INFO [查询序列码] 开始查询序列码信息
2025-07-19 09:11:21.403  INFO [查询序列码] 数据库连接获取成功
2025-07-19 09:11:21.426  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 09:11:21.431  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 09:11:21.432  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 09:19:16.551  INFO [查询序列码] 开始查询序列码信息
2025-07-19 09:19:16.564  INFO [查询序列码] 数据库连接获取成功
2025-07-19 09:19:16.593  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 09:19:16.598  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 09:19:16.599  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290334, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 09:19:22.864  INFO [查询序列码] 开始查询序列码信息
2025-07-19 09:19:22.873  INFO [查询序列码] 数据库连接获取成功
2025-07-19 09:19:22.878  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 09:19:22.880  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 09:19:22.880  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 09:19:27.262  INFO [聚合] 开始聚合操作流程
2025-07-19 09:19:27.263  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-19 09:19:27.282  INFO [聚合] 数据库连接获取成功
2025-07-19 09:19:27.287  INFO [聚合] |放入| 托盘码: 00703435470002290334
2025-07-19 09:19:27.299  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-19 09:19:27.301  INFO [聚合] |放入| 处理箱码 1/1: 015034354727603921100000127123
2025-07-19 09:19:27.305  INFO [聚合] |放入| 查询到箱码信息: code_flag=2, level_code=3
2025-07-19 09:19:27.308  INFO [聚合] |放入| 开始执行放入操作
2025-07-19 09:19:27.316  INFO [聚合] |放入| 处理箱码: 015034354727603921100000127123
2025-07-19 09:19:27.318  INFO [聚合] |放入| 开始执行更新瓶码SQL: 
                UPDATE transit_code
                SET palletCode = '00703435470002290334',
                    updateTime = '2025-07-19 09:19:27'
                WHERE levelCode = 1
                  AND codeFlag = 2
                  AND boxCode = '015034354727603921100000127123'
            
2025-07-19 09:19:27.559  INFO [聚合] |放入| 更新瓶码信息成功
2025-07-19 09:19:27.559  INFO [聚合] |放入| 开始执行更新箱码SQL: 
                UPDATE transit_code
                SET parentCode = '00703435470002290334',
                    palletCode = '00703435470002290334',
                    updateTime = '2025-07-19 09:20:27'
                WHERE levelCode = 3
                  AND codeFlag = 2
                  AND code = '015034354727603921100000127123'
            
2025-07-19 09:19:27.564  INFO [聚合] |放入| 更新箱码信息成功
2025-07-19 09:19:27.565  INFO [聚合] |放入| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET updateTime = '2025-07-19 09:22:27'
            WHERE levelCode = 4
              AND codeFlag = 2
              AND code = '00703435470002290334'
        
2025-07-19 09:19:27.568  INFO [聚合] |放入| 更新托盘码信息成功
2025-07-19 09:19:27.569  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-19 11:21:16.359  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:21:16.385  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:21:16.398  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:21:16.404  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:21:16.405  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:21:27.424  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:21:27.434  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:21:27.439  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:21:27.442  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:21:27.443  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290297, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:21:35.111  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:21:35.133  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:21:35.135  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:21:35.137  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:21:35.138  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:22:51.716  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:22:51.728  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:22:51.732  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:22:51.734  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:22:51.736  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:22:54.609  INFO [取出] 开始取出操作流程
2025-07-19 11:22:54.610  INFO [取出] 接收到请求参数: code=00503435470000872620
2025-07-19 11:22:54.621  INFO [取出] 数据库连接获取成功
2025-07-19 11:22:54.622  INFO [取出] 开始查询序列码: 00503435470000872620
2025-07-19 11:22:54.630  INFO [取出] 查询到序列码信息: code_flag=2, level_code=3, zero_box=1, batch_no=R202500040
2025-07-19 11:22:54.630  INFO [取出] 序列码状态正常: code_flag=2
2025-07-19 11:22:54.630  INFO [取出] 开始判断箱码是否符合取出条件
2025-07-19 11:22:54.630  INFO [取出] 大箱关联的托盘码: 00703435470002290334
2025-07-19 11:22:54.631  INFO [取出] 开始从托盘中取出箱码
2025-07-19 11:22:54.631  INFO [取出] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount - 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290334'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-19 11:22:54.635  INFO [取出] 更新托盘码信息成功
2025-07-19 11:22:54.635  INFO [取出] 开始执行解除箱码与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00503435470000872620'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-19 11:22:54.639  INFO [取出] 解除箱码与托盘的绑定成功
2025-07-19 11:22:54.646  INFO [取出] 开始执行解除小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '00503435470000872620'
              AND boxCode = '00503435470000872620'
              AND palletCode = '00703435470002290334'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-19 11:22:54.672  INFO [取出] 解除小瓶与托盘的绑定成功
2025-07-19 11:22:54.674  INFO [取出] 事务提交成功，取出操作完成
2025-07-19 11:24:17.797  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:24:21.373  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:24:21.376  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:24:21.378  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:24:21.379  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290297, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:24:29.050  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:24:29.060  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:24:29.062  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:24:29.064  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:24:29.065  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:24:32.020  INFO [聚合] 开始聚合操作流程
2025-07-19 11:24:32.021  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-19 11:24:32.032  INFO [聚合] 数据库连接获取成功
2025-07-19 11:24:32.033  INFO [聚合] |放入| 托盘码: 00703435470002290297
2025-07-19 11:24:32.035  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-19 11:24:32.035  INFO [聚合] |放入| 处理箱码 1/1: 00503435470000872620
2025-07-19 11:24:32.036  INFO [聚合] |放入| 查询到箱码信息: code_flag=2, level_code=3
2025-07-19 11:24:32.037  INFO [聚合] |放入| 开始执行放入操作
2025-07-19 11:24:32.037  INFO [聚合] |放入| 处理箱码: 00503435470000872620
2025-07-19 11:24:32.037  INFO [聚合] |放入| 开始执行更新瓶码SQL: 
                UPDATE transit_code
                SET palletCode = '00703435470002290297',
                    updateTime = '2025-07-19 11:24:32'
                WHERE levelCode = 1
                  AND codeFlag = 2
                  AND boxCode = '00503435470000872620'
            
2025-07-19 11:24:32.235  INFO [聚合] |放入| 更新瓶码信息成功
2025-07-19 11:24:32.236  INFO [聚合] |放入| 开始执行更新箱码SQL: 
                UPDATE transit_code
                SET parentCode = '00703435470002290297',
                    palletCode = '00703435470002290297',
                    updateTime = '2025-07-19 11:25:32'
                WHERE levelCode = 3
                  AND codeFlag = 2
                  AND code = '00503435470000872620'
            
2025-07-19 11:24:32.241  INFO [聚合] |放入| 更新箱码信息成功
2025-07-19 11:24:32.241  INFO [聚合] |放入| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET updateTime = '2025-07-19 11:27:32'
            WHERE levelCode = 4
              AND codeFlag = 2
              AND code = '00703435470002290297'
        
2025-07-19 11:24:32.245  INFO [聚合] |放入| 更新托盘码信息成功
2025-07-19 11:24:32.245  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-19 11:24:59.889  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:24:59.897  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:24:59.899  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:24:59.900  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:24:59.901  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290297, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:25:02.180  INFO [解绑] 开始解绑操作流程
2025-07-19 11:25:02.181  INFO [解绑] 接收到请求参数: code=00703435470002290297
2025-07-19 11:25:02.189  INFO [解绑] 数据库连接获取成功
2025-07-19 11:25:02.189  INFO [解绑] 开始查询序列码: 00703435470002290297
2025-07-19 11:25:02.191  INFO [解绑] 查询到序列码信息: code_flag=2, level_code=4, zero_box=0, batch_no=R202500040
2025-07-19 11:25:02.191  INFO [解绑] 序列码状态正常: code_flag=2
2025-07-19 11:25:02.192  INFO [解绑] 开始判断托盘码是否符合解绑条件
2025-07-19 11:25:02.193  INFO [解绑] 开始解绑托盘下所有大箱及相关瓶码与托盘的关联关系
2025-07-19 11:25:02.194  INFO [解绑] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = 0,
                parentCode = NULL,
                boxCode = NULL,
                palletCode = NULL,
                codeFlag = 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290297'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-19 11:25:02.211  INFO [解绑] 更新托盘码信息成功
2025-07-19 11:25:02.211  INFO [解绑] 开始执行解除托盘下所有大箱与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '00703435470002290297'
              AND palletCode = '00703435470002290297'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-19 11:25:02.230  INFO [解绑] 解除托盘下所有大箱与托盘的绑定成功
2025-07-19 11:25:02.230  INFO [解绑] 开始执行解除托盘下所有小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND palletCode = '00703435470002290297'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-19 11:25:02.249  INFO [解绑] 解除托盘下所有小瓶与托盘的绑定成功
2025-07-19 11:25:02.251  INFO [解绑] 事务提交成功，解绑操作完成
2025-07-19 11:25:10.289  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:25:10.297  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:25:10.300  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:25:10.301  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:25:10.302  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290334, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:25:12.090  INFO [解绑] 开始解绑操作流程
2025-07-19 11:25:12.090  INFO [解绑] 接收到请求参数: code=00703435470002290334
2025-07-19 11:25:12.121  INFO [解绑] 数据库连接获取成功
2025-07-19 11:25:12.122  INFO [解绑] 开始查询序列码: 00703435470002290334
2025-07-19 11:25:12.123  INFO [解绑] 查询到序列码信息: code_flag=2, level_code=4, zero_box=0, batch_no=R202500040
2025-07-19 11:25:12.124  INFO [解绑] 序列码状态正常: code_flag=2
2025-07-19 11:25:12.125  INFO [解绑] 开始判断托盘码是否符合解绑条件
2025-07-19 11:25:12.125  INFO [解绑] 开始解绑托盘下所有大箱及相关瓶码与托盘的关联关系
2025-07-19 11:25:12.125  INFO [解绑] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = 0,
                parentCode = NULL,
                boxCode = NULL,
                palletCode = NULL,
                codeFlag = 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290334'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-19 11:25:12.127  INFO [解绑] 更新托盘码信息成功
2025-07-19 11:25:12.128  INFO [解绑] 开始执行解除托盘下所有大箱与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '00703435470002290334'
              AND palletCode = '00703435470002290334'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-19 11:25:12.141  INFO [解绑] 解除托盘下所有大箱与托盘的绑定成功
2025-07-19 11:25:12.142  INFO [解绑] 开始执行解除托盘下所有小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND palletCode = '00703435470002290334'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-19 11:25:12.156  INFO [解绑] 解除托盘下所有小瓶与托盘的绑定成功
2025-07-19 11:25:12.159  INFO [解绑] 事务提交成功，解绑操作完成
2025-07-19 11:25:47.226  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:25:47.260  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:25:47.263  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:25:47.264  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:25:47.265  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:25:50.206  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:25:50.218  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:25:50.220  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:25:50.222  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:25:50.223  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:25:53.810  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:25:53.819  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:25:53.821  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:25:53.823  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:25:53.824  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:25:56.732  INFO [聚合] 开始聚合操作流程
2025-07-19 11:25:56.732  INFO [聚合] 接收到请求参数: aggregate_type=newPackage, box_codes数量=3
2025-07-19 11:25:56.741  INFO [聚合] 数据库连接获取成功
2025-07-19 11:25:56.741  INFO [聚合] |新建包装| 开始执行新建包装模式
2025-07-19 11:25:56.742  INFO [聚合] |新建包装| 处理箱码 1/3: 00503435470000872620
2025-07-19 11:25:56.743  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-19 11:25:56.744  INFO [聚合] |新建包装| 处理箱码 2/3: 015034354727603921100000127123
2025-07-19 11:25:56.744  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-19 11:25:56.744  INFO [聚合] |新建包装| 处理箱码 3/3: 015034354727603921100000127124
2025-07-19 11:25:56.745  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-19 11:25:56.746  INFO [聚合] |新建包装| 开始执行新建包装操作
2025-07-19 11:25:56.748  INFO [取码函数] 查询生产订单信息SQL: SELECT TOP 1 id, productId, poNumber, batchNo FROM [produce_order] WHERE id = '4'
2025-07-19 11:25:56.759  INFO [取码函数] 查询产品信息SQL: SELECT TOP 1 id, productCode, packageRules, codeStyle FROM [basic_product] WHERE id = '3'
2025-07-19 11:25:56.761  INFO [取码函数] 查询序列码信息SQL: SELECT TOP 1 tc.id, tc.code, tc.snCode, tc.fileID 
        FROM [transit_code] tc
        JOIN [sn_record] sr ON sr.id = tc.fileID
        WHERE tc.orderId = '4' 
        AND tc.levelCode = '4' 
        AND tc.typeFlag = '1' 
        AND tc.codeFlag IN (0, 1)
        AND sr.filterValue = '7'
        AND sr.resCode = '0343547'
        AND sr.typeFlag = '1'
        AND sr.codeStyle = 'tracelink'
        ORDER BY tc.id
2025-07-19 11:25:56.820  INFO [取码函数] 数据库中符合条件的序列码数量: 1
2025-07-19 11:25:56.821  INFO [聚合] |新建包装| 获取托盘码结果: {"errmsg": String("序列码获取成功"), "data": Array [String("00703435470002290235")], "errcode": Number(0)}
2025-07-19 11:25:56.823  INFO [聚合] |新建包装| 获取托盘码数据: [String("00703435470002290235")]
2025-07-19 11:25:56.823  INFO [聚合] |新建包装| 获取到托盘码: 00703435470002290235
2025-07-19 11:25:56.824  INFO [聚合] |新建包装| 开始执行更新瓶码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290235',
                updateTime = '2025-07-19 11:25:56'
            WHERE levelCode = 1
              AND codeFlag = 2
              AND boxCode IN ('00503435470000872620','015034354727603921100000127123','015034354727603921100000127124')
        
2025-07-19 11:25:57.049  INFO [聚合] |新建包装| 更新瓶码信息成功
2025-07-19 11:25:57.049  INFO [聚合] |新建包装| 开始执行更新箱码SQL: 
            UPDATE transit_code
            SET parentCode = '00703435470002290235',
                palletCode = '00703435470002290235',
                updateTime = '2025-07-19 11:26:56'
            WHERE levelCode = 3
              AND codeFlag = 2
              AND code IN ('00503435470000872620','015034354727603921100000127123','015034354727603921100000127124')
        
2025-07-19 11:25:57.054  INFO [聚合] |新建包装| 更新箱码信息成功
2025-07-19 11:25:57.055  INFO [聚合] |新建包装| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290235',
                codeFlag = 2,
                updateTime = '2025-07-19 11:30:56'
            WHERE levelCode = 4
              AND code = '00703435470002290235'
        
2025-07-19 11:25:57.057  INFO [聚合] |新建包装| 更新托盘码信息成功
2025-07-19 11:25:57.059  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-19 11:31:39.187  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-19 11:31:39.188  INFO 数据库连接管理器创建成功
2025-07-19 11:31:39.230  INFO 数据库连接池创建成功
2025-07-19 11:31:39.235  INFO 服务器运行在 0.0.0.0:3000
2025-07-19 11:32:41.865  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:32:41.912  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:32:41.915  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:32:41.916  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:32:41.917  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:32:44.224  INFO [解绑] 开始解绑操作流程
2025-07-19 11:32:44.225  INFO [解绑] 接收到请求参数: code=00703435470002290235
2025-07-19 11:32:44.235  INFO [解绑] 数据库连接获取成功
2025-07-19 11:32:44.237  INFO [解绑] 开始查询序列码: 00703435470002290235
2025-07-19 11:32:44.241  INFO [解绑] 查询到序列码信息: code_flag=2, level_code=4, zero_box=0, batch_no=R202500040
2025-07-19 11:32:44.241  INFO [解绑] 序列码状态正常: code_flag=2
2025-07-19 11:32:44.242  INFO [解绑] 开始判断托盘码是否符合解绑条件
2025-07-19 11:32:44.243  INFO [解绑] 开始解绑托盘下所有大箱及相关瓶码与托盘的关联关系
2025-07-19 11:32:44.243  INFO [解绑] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = 0,
                parentCode = NULL,
                boxCode = NULL,
                palletCode = NULL,
                codeFlag = 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-19 11:32:44.246  INFO [解绑] 更新托盘码信息成功
2025-07-19 11:32:44.246  INFO [解绑] 开始执行解除托盘下所有大箱与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '00703435470002290235'
              AND palletCode = '00703435470002290235'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-19 11:32:44.261  INFO [解绑] 解除托盘下所有大箱与托盘的绑定成功
2025-07-19 11:32:44.262  INFO [解绑] 开始执行解除托盘下所有小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-19 11:32:44.277  INFO [解绑] 解除托盘下所有小瓶与托盘的绑定成功
2025-07-19 11:32:44.278  INFO [解绑] 事务提交成功，解绑操作完成
2025-07-19 11:33:28.942  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:33:28.970  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:33:28.979  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:33:28.983  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:33:28.984  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:33:31.586  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:33:31.596  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:33:31.598  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:33:31.599  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:33:31.600  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:33:34.035  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:33:34.046  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:33:34.048  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:33:34.050  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:33:34.051  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:33:36.297  INFO [聚合] 开始聚合操作流程
2025-07-19 11:33:36.297  INFO [聚合] 接收到请求参数: aggregate_type=newPackage, box_codes数量=3
2025-07-19 11:33:36.307  INFO [聚合] 数据库连接获取成功
2025-07-19 11:33:36.308  INFO [聚合] |新建包装| 开始执行新建包装模式
2025-07-19 11:33:36.308  INFO [聚合] |新建包装| 处理箱码 1/3: 00503435470000872620
2025-07-19 11:33:36.313  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-19 11:33:36.314  INFO [聚合] |新建包装| 处理箱码 2/3: 015034354727603921100000127123
2025-07-19 11:33:36.315  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-19 11:33:36.316  INFO [聚合] |新建包装| 处理箱码 3/3: 015034354727603921100000127124
2025-07-19 11:33:36.317  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-19 11:33:36.317  INFO [聚合] |新建包装| 开始执行新建包装操作
2025-07-19 11:33:36.318  INFO [取码函数] 查询生产订单信息SQL: SELECT TOP 1 id, productId, poNumber, batchNo FROM [produce_order] WHERE id = '4'
2025-07-19 11:33:36.326  INFO [取码函数] 查询产品信息SQL: SELECT TOP 1 id, productCode, packageRules, codeStyle FROM [basic_product] WHERE id = '3'
2025-07-19 11:33:36.334  INFO [取码函数] 查询序列码信息SQL: SELECT TOP 1 tc.id, tc.code, tc.snCode, tc.fileID 
        FROM [transit_code] tc
        JOIN [sn_record] sr ON sr.id = tc.fileID
        WHERE tc.orderId = '4' 
        AND tc.levelCode = '4' 
        AND tc.typeFlag = '1' 
        AND tc.codeFlag IN (0, 1)
        AND sr.filterValue = '7'
        AND sr.resCode = '0343547'
        AND sr.typeFlag = '1'
        AND sr.codeStyle = 'tracelink'
        ORDER BY tc.id
2025-07-19 11:33:36.342  INFO [取码函数] 数据库中符合条件的序列码数量: 1
2025-07-19 11:33:36.343  INFO [聚合] |新建包装| 获取托盘码结果: {"errcode": Number(0), "data": Array [String("00703435470002290235")], "errmsg": String("序列码获取成功")}
2025-07-19 11:33:36.344  INFO [聚合] |新建包装| 获取托盘码数据: [String("00703435470002290235")]
2025-07-19 11:33:36.345  INFO [聚合] |新建包装| 获取到托盘码: 00703435470002290235
2025-07-19 11:33:36.345  INFO [聚合] |新建包装| 开始执行更新瓶码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290235',
                updateTime = '2025-07-19 11:33:36'
            WHERE levelCode = 1
              AND codeFlag = 2
              AND boxCode IN ('00503435470000872620','015034354727603921100000127123','015034354727603921100000127124')
        
2025-07-19 11:33:36.586  INFO [聚合] |新建包装| 更新瓶码信息成功
2025-07-19 11:33:36.587  INFO [聚合] |新建包装| 开始执行更新箱码SQL: 
            UPDATE transit_code
            SET parentCode = '00703435470002290235',
                palletCode = '00703435470002290235',
                updateTime = '2025-07-19 11:34:36'
            WHERE levelCode = 3
              AND codeFlag = 2
              AND code IN ('00503435470000872620','015034354727603921100000127123','015034354727603921100000127124')
        
2025-07-19 11:33:36.594  INFO [聚合] |新建包装| 更新箱码信息成功
2025-07-19 11:33:36.594  INFO [聚合] |新建包装| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290235',
                amount = 3,
                codeFlag = 2,
                updateTime = '2025-07-19 11:38:36'
            WHERE levelCode = 4
              AND code = '00703435470002290235'
        
2025-07-19 11:33:36.598  INFO [聚合] |新建包装| 更新托盘码信息成功
2025-07-19 11:33:36.599  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-19 11:36:59.871  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:36:59.885  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:36:59.888  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:36:59.889  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:36:59.890  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:37:03.955  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:37:03.967  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:37:03.969  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:37:03.971  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:37:03.972  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:37:07.585  INFO [解绑] 开始解绑操作流程
2025-07-19 11:37:07.586  INFO [解绑] 接收到请求参数: code=00703435470002290235
2025-07-19 11:37:07.597  INFO [解绑] 数据库连接获取成功
2025-07-19 11:37:07.598  INFO [解绑] 开始查询序列码: 00703435470002290235
2025-07-19 11:37:07.599  INFO [解绑] 查询到序列码信息: code_flag=2, level_code=4, zero_box=0, batch_no=R202500040
2025-07-19 11:37:07.600  INFO [解绑] 序列码状态正常: code_flag=2
2025-07-19 11:37:07.600  INFO [解绑] 开始判断托盘码是否符合解绑条件
2025-07-19 11:37:07.600  INFO [解绑] 开始解绑托盘下所有大箱及相关瓶码与托盘的关联关系
2025-07-19 11:37:07.601  INFO [解绑] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = 0,
                parentCode = NULL,
                boxCode = NULL,
                palletCode = NULL,
                codeFlag = 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-19 11:37:07.602  INFO [解绑] 更新托盘码信息成功
2025-07-19 11:37:07.602  INFO [解绑] 开始执行解除托盘下所有大箱与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '00703435470002290235'
              AND palletCode = '00703435470002290235'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-19 11:37:07.619  INFO [解绑] 解除托盘下所有大箱与托盘的绑定成功
2025-07-19 11:37:07.620  INFO [解绑] 开始执行解除托盘下所有小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-19 11:37:07.635  INFO [解绑] 解除托盘下所有小瓶与托盘的绑定成功
2025-07-19 11:37:07.638  INFO [解绑] 事务提交成功，解绑操作完成
2025-07-19 11:37:29.097  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:37:29.107  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:37:29.110  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:37:29.112  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:37:29.112  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:37:32.071  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:37:32.083  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:37:32.085  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:37:32.088  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:37:32.090  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:37:35.116  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:37:35.128  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:37:35.130  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:37:35.132  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:37:35.133  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:37:38.097  INFO [聚合] 开始聚合操作流程
2025-07-19 11:37:38.097  INFO [聚合] 接收到请求参数: aggregate_type=newPackage, box_codes数量=3
2025-07-19 11:37:38.110  INFO [聚合] 数据库连接获取成功
2025-07-19 11:37:38.111  INFO [聚合] |新建包装| 开始执行新建包装模式
2025-07-19 11:37:38.111  INFO [聚合] |新建包装| 处理箱码 1/3: 00503435470000872620
2025-07-19 11:37:38.112  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-19 11:37:38.113  INFO [聚合] |新建包装| 处理箱码 2/3: 015034354727603921100000127123
2025-07-19 11:37:38.114  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-19 11:37:38.114  INFO [聚合] |新建包装| 处理箱码 3/3: 015034354727603921100000127124
2025-07-19 11:37:38.115  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-19 11:37:38.115  INFO [聚合] |新建包装| 开始执行新建包装操作
2025-07-19 11:37:38.116  INFO [取码函数] 查询生产订单信息SQL: SELECT TOP 1 id, productId, poNumber, batchNo FROM [produce_order] WHERE id = '4'
2025-07-19 11:37:38.116  INFO [取码函数] 查询产品信息SQL: SELECT TOP 1 id, productCode, packageRules, codeStyle FROM [basic_product] WHERE id = '3'
2025-07-19 11:37:38.117  INFO [取码函数] 查询序列码信息SQL: SELECT TOP 1 tc.id, tc.code, tc.snCode, tc.fileID 
        FROM [transit_code] tc
        JOIN [sn_record] sr ON sr.id = tc.fileID
        WHERE tc.orderId = '4' 
        AND tc.levelCode = '4' 
        AND tc.typeFlag = '1' 
        AND tc.codeFlag IN (0, 1)
        AND sr.filterValue = '7'
        AND sr.resCode = '0343547'
        AND sr.typeFlag = '1'
        AND sr.codeStyle = 'tracelink'
        ORDER BY tc.id
2025-07-19 11:37:38.127  INFO [取码函数] 数据库中符合条件的序列码数量: 1
2025-07-19 11:37:38.132  INFO [聚合] |新建包装| 获取托盘码结果: {"data": Array [String("00703435470002290235")], "errcode": Number(0), "errmsg": String("序列码获取成功")}
2025-07-19 11:37:38.132  INFO [聚合] |新建包装| 获取托盘码数据: [String("00703435470002290235")]
2025-07-19 11:37:38.133  INFO [聚合] |新建包装| 获取到托盘码: 00703435470002290235
2025-07-19 11:37:38.133  INFO [聚合] |新建包装| 开始执行更新瓶码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290235',
                updateTime = '2025-07-19 11:37:38'
            WHERE levelCode = 1
              AND codeFlag = 2
              AND boxCode IN ('00503435470000872620','015034354727603921100000127123','015034354727603921100000127124')
        
2025-07-19 11:37:38.366  INFO [聚合] |新建包装| 更新瓶码信息成功
2025-07-19 11:37:38.367  INFO [聚合] |新建包装| 开始执行更新箱码SQL: 
            UPDATE transit_code
            SET parentCode = '00703435470002290235',
                palletCode = '00703435470002290235',
                updateTime = '2025-07-19 11:38:38'
            WHERE levelCode = 3
              AND codeFlag = 2
              AND code IN ('00503435470000872620','015034354727603921100000127123','015034354727603921100000127124')
        
2025-07-19 11:37:38.375  INFO [聚合] |新建包装| 更新箱码信息成功
2025-07-19 11:37:38.376  INFO [聚合] |新建包装| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290235',
                amount = 3,
                codeFlag = 2,
                updateTime = '2025-07-19 11:42:38'
            WHERE levelCode = 4
              AND code = '00703435470002290235'
        
2025-07-19 11:37:38.378  INFO [聚合] |新建包装| 更新托盘码信息成功
2025-07-19 11:37:38.380  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-19 11:38:21.701  INFO [关联查询] 开始查询关联关系
2025-07-19 11:38:21.713  INFO [关联查询] 数据库连接获取成功
2025-07-19 11:38:21.715  INFO [关联查询] 码查询: 标识=true
2025-07-19 11:38:21.715  INFO [关联查询] 关键字段: code_flag=2, level_code=4, order_id=4
2025-07-19 11:38:21.715  INFO [关联查询] 使用自身作为托盘码关联
2025-07-19 11:38:21.716  INFO [关联查询] 查询订单信息: order_id=4
2025-07-19 11:38:21.719  INFO [关联查询] 订单查询: 找到=true
2025-07-19 11:38:21.719  INFO [关联查询] 查询产品信息: product_id=3
2025-07-19 11:38:21.722  INFO [关联查询] 产品查询: 找到=true
2025-07-19 11:38:21.723  INFO [关联查询] 开始查询出库信息
2025-07-19 11:38:21.727  INFO [关联查询] 出库信息查询完成: wareID=0
2025-07-19 11:38:21.736  INFO [关联查询] 成功返回追溯信息
2025-07-19 11:38:26.804  INFO [子级追溯] 开始查询子级追溯信息
2025-07-19 11:38:26.815  INFO [子级追溯] 数据库连接获取成功
2025-07-19 11:38:26.815  INFO [子级追溯] 查询参数: parentCode=00703435470002290235
2025-07-19 11:38:26.816  INFO [子级追溯] 执行SQL查询
2025-07-19 11:38:27.094  INFO [子级追溯] 开始处理查询结果
2025-07-19 11:38:27.095  INFO [子级追溯] 查询完成: 找到=3, 处理记录数=4
2025-07-19 11:38:27.096  INFO [子级追溯] 查询结束: parentCode=00703435470002290235, 数量=3
2025-07-19 11:38:29.772  INFO [子级追溯] 开始查询子级追溯信息
2025-07-19 11:38:29.783  INFO [子级追溯] 数据库连接获取成功
2025-07-19 11:38:29.783  INFO [子级追溯] 查询参数: parentCode=015034354727603921100000127123
2025-07-19 11:38:29.784  INFO [子级追溯] 执行SQL查询
2025-07-19 11:38:30.079  INFO [子级追溯] 开始处理查询结果
2025-07-19 11:38:30.080  INFO [子级追溯] 查询完成: 找到=12, 处理记录数=13
2025-07-19 11:38:30.081  INFO [子级追溯] 查询结束: parentCode=015034354727603921100000127123, 数量=12
2025-07-19 11:38:35.663  INFO [子级追溯] 开始查询子级追溯信息
2025-07-19 11:38:35.676  INFO [子级追溯] 数据库连接获取成功
2025-07-19 11:38:35.676  INFO [子级追溯] 查询参数: parentCode=00503435470000872620
2025-07-19 11:38:35.677  INFO [子级追溯] 执行SQL查询
2025-07-19 11:38:36.086  INFO [子级追溯] 开始处理查询结果
2025-07-19 11:38:36.087  INFO [子级追溯] 查询完成: 找到=6, 处理记录数=7
2025-07-19 11:38:36.089  INFO [子级追溯] 查询结束: parentCode=00503435470000872620, 数量=6
2025-07-19 11:38:47.654  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:38:47.666  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:38:47.668  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:38:47.670  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:38:47.670  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:39:03.506  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:39:03.553  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:39:03.559  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:39:03.563  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:39:03.566  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:40:52.256  INFO [查询序列码] 开始查询序列码信息
2025-07-19 11:40:52.267  INFO [查询序列码] 数据库连接获取成功
2025-07-19 11:40:52.270  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 11:40:52.271  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 11:40:52.272  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 11:59:45.049  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-19 11:59:45.050  INFO 数据库连接管理器创建成功
2025-07-19 11:59:45.084  INFO 数据库连接池创建成功
2025-07-19 11:59:45.089  INFO 服务器运行在 0.0.0.0:3000
2025-07-19 12:04:54.800  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-19 12:04:54.801  INFO 数据库连接管理器创建成功
2025-07-19 12:04:54.833  INFO 数据库连接池创建成功
2025-07-19 12:04:54.837  INFO 服务器运行在 0.0.0.0:3000
2025-07-19 12:07:39.259  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-19 12:07:39.262  INFO 数据库连接管理器创建成功
2025-07-19 12:07:39.291  INFO 数据库连接池创建成功
2025-07-19 12:07:39.296  INFO 服务器运行在 0.0.0.0:3000
2025-07-19 14:24:14.236  INFO [查询序列码] 开始查询序列码信息
2025-07-19 14:24:14.276  INFO [查询序列码] 数据库连接获取成功
2025-07-19 14:24:14.300  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 14:24:14.306  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 14:24:14.307  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-19 14:24:15.484  INFO [查询序列码] 托盘内箱码数量: 3
2025-07-19 14:24:17.068  INFO [查询序列码] 托盘内瓶码数量: 30
2025-07-19 14:24:17.069  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 14:32:31.044  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-19 14:32:31.045  INFO 数据库连接管理器创建成功
2025-07-19 14:32:31.075  INFO 数据库连接池创建成功
2025-07-19 14:32:31.079  INFO 服务器运行在 0.0.0.0:3000
2025-07-19 14:32:52.712  INFO [查询序列码] 开始查询序列码信息
2025-07-19 14:32:52.764  INFO [查询序列码] 数据库连接获取成功
2025-07-19 14:32:52.786  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 14:32:52.791  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 14:32:52.791  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-19 14:32:52.792  INFO [查询序列码] 瓶码不需要查询数量
2025-07-19 14:32:52.792  INFO [查询序列码] 查询序列码信息完成: code=010034354727603421100002880452, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 14:38:28.114  INFO [关联查询] 开始查询关联关系
2025-07-19 14:38:28.126  INFO [关联查询] 数据库连接获取成功
2025-07-19 14:38:28.141  INFO [关联查询] 码查询: 标识=true
2025-07-19 14:38:28.141  INFO [关联查询] 关键字段: code_flag=2, level_code=1, order_id=4
2025-07-19 14:38:28.142  INFO [关联查询] 查询托盘码: 00703435470002290235
2025-07-19 14:38:28.147  INFO [关联查询] 托盘码查询: 标识=true
2025-07-19 14:38:28.148  INFO [关联查询] 查询订单信息: order_id=4
2025-07-19 14:38:28.155  INFO [关联查询] 订单查询: 找到=true
2025-07-19 14:38:28.156  INFO [关联查询] 查询产品信息: product_id=3
2025-07-19 14:38:28.161  INFO [关联查询] 产品查询: 找到=true
2025-07-19 14:38:28.163  INFO [关联查询] 开始查询出库信息
2025-07-19 14:38:28.167  INFO [关联查询] 出库信息查询完成: wareID=0
2025-07-19 14:38:28.168  INFO [关联查询] 成功返回追溯信息
2025-07-19 14:39:22.051  INFO [产品分页] 开始获取产品分页列表
2025-07-19 14:39:22.052  INFO [产品分页] 查询参数: page=1, per_page=5, product_name=Some(""), product_code=None, ndc=None
2025-07-19 14:39:22.066  INFO [产品分页] 数据库连接获取成功
2025-07-19 14:39:22.086  INFO [产品分页] 查询成功，获取到5条记录
2025-07-19 14:39:26.183  INFO [产品分页] 开始获取产品分页列表
2025-07-19 14:39:26.184  INFO [产品分页] 查询参数: page=1, per_page=5, product_name=Some(""), product_code=None, ndc=None
2025-07-19 14:39:26.196  INFO [产品分页] 数据库连接获取成功
2025-07-19 14:39:26.201  INFO [产品分页] 查询成功，获取到5条记录
2025-07-19 14:39:28.810  INFO [订单分页] 开始获取订单分页列表
2025-07-19 14:39:28.810  INFO [订单分页] 查询参数: page=1, per_page=5, batch_no=Some(""), po_number=None, estate=None
2025-07-19 14:39:28.821  INFO [订单分页] 数据库连接获取成功
2025-07-19 14:39:28.836  INFO [订单分页] 查询成功，获取到5条记录
2025-07-19 14:39:28.843  INFO [订单分页] 成功加载关联的订单请求数据
2025-07-19 14:40:56.551  INFO [查询序列码] 开始查询序列码信息
2025-07-19 14:40:56.564  INFO [查询序列码] 数据库连接获取成功
2025-07-19 14:40:56.571  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 14:40:56.574  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 14:40:56.574  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-19 14:40:57.434  INFO [查询序列码] 托盘内箱码数量: 3
2025-07-19 14:40:57.729  INFO [查询序列码] 托盘内瓶码数量: 30
2025-07-19 14:40:57.730  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 14:42:39.615  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-19 14:42:39.616  INFO 数据库连接管理器创建成功
2025-07-19 14:42:39.645  INFO 数据库连接池创建成功
2025-07-19 14:42:39.649  INFO 服务器运行在 0.0.0.0:3000
2025-07-19 14:44:31.365  INFO [查询序列码] 开始查询序列码信息
2025-07-19 14:44:31.401  INFO [查询序列码] 数据库连接获取成功
2025-07-19 14:44:31.404  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 14:44:31.405  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 14:44:31.406  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-19 14:44:31.558  INFO [查询序列码] 托盘内箱码数量: 3
2025-07-19 14:44:31.841  INFO [查询序列码] 托盘内瓶码数量: 30
2025-07-19 14:44:31.842  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 14:45:36.525  INFO [查询序列码] 开始查询序列码信息
2025-07-19 14:45:36.535  INFO [查询序列码] 数据库连接获取成功
2025-07-19 14:45:36.537  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 14:45:36.540  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 14:45:36.541  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-19 14:45:36.541  INFO [查询序列码] 瓶码不需要查询数量
2025-07-19 14:45:36.542  INFO [查询序列码] 查询序列码信息完成: code=010034354727603421100002880452, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 14:47:17.056  INFO [查询序列码] 开始查询序列码信息
2025-07-19 14:47:17.066  INFO [查询序列码] 数据库连接获取成功
2025-07-19 14:47:17.069  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 14:47:17.072  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 14:47:17.072  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-19 14:47:17.258  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-19 14:47:17.259  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 14:49:20.109  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-19 14:49:20.110  INFO 数据库连接管理器创建成功
2025-07-19 14:49:20.142  INFO 数据库连接池创建成功
2025-07-19 14:49:20.147  INFO 服务器运行在 0.0.0.0:3000
2025-07-19 14:54:44.857  INFO [查询序列码] 开始查询序列码信息
2025-07-19 14:54:44.893  INFO [查询序列码] 数据库连接获取成功
2025-07-19 14:54:44.896  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 14:54:44.897  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 14:54:44.898  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-19 14:54:44.898  INFO [查询序列码] 瓶码不需要查询数量
2025-07-19 14:54:44.899  INFO [查询序列码] 查询序列码信息完成: code=010034354727603421100002880452, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 15:39:30.220  INFO [查询序列码] 开始查询序列码信息
2025-07-19 15:39:30.249  INFO [查询序列码] 数据库连接获取成功
2025-07-19 15:39:30.251  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 15:39:30.253  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 15:39:30.253  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-19 15:39:30.341  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-19 15:39:30.342  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 15:39:52.813  INFO [查询序列码] 开始查询序列码信息
2025-07-19 15:39:52.822  INFO [查询序列码] 数据库连接获取成功
2025-07-19 15:39:52.825  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 15:39:52.827  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 15:39:52.827  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-19 15:39:52.999  INFO [查询序列码] 托盘内箱码数量: 3
2025-07-19 15:39:53.578  INFO [查询序列码] 托盘内瓶码数量: 30
2025-07-19 15:39:53.579  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 16:04:07.932  INFO [查询序列码] 开始查询序列码信息
2025-07-19 16:04:07.946  INFO [查询序列码] 数据库连接获取成功
2025-07-19 16:04:07.948  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 16:04:07.950  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 16:04:07.950  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-19 16:04:08.112  INFO [查询序列码] 托盘内箱码数量: 3
2025-07-19 16:04:08.383  INFO [查询序列码] 托盘内瓶码数量: 30
2025-07-19 16:04:08.384  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 16:05:24.849  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-19 16:05:24.850  INFO 数据库连接管理器创建成功
2025-07-19 16:05:24.879  INFO 数据库连接池创建成功
2025-07-19 16:05:24.883  INFO 服务器运行在 0.0.0.0:3000
2025-07-19 21:32:16.849  INFO [查询序列码] 开始查询序列码信息
2025-07-19 21:32:16.885  INFO [查询序列码] 数据库连接获取成功
2025-07-19 21:32:16.900  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 21:32:16.904  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 21:32:16.905  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-19 21:32:16.907  INFO [查询序列码] 瓶码不需要查询数量
2025-07-19 21:32:16.908  INFO [查询序列码] 查询序列码信息完成: code=010034354727603421100002880452, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 22:21:54.711  INFO [查询序列码] 开始查询序列码信息
2025-07-19 22:21:54.744  INFO [查询序列码] 数据库连接获取成功
2025-07-19 22:21:54.769  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 22:21:54.773  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 22:21:54.774  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-19 22:21:54.775  INFO [查询序列码] 瓶码不需要查询数量
2025-07-19 22:21:54.775  INFO [查询序列码] 查询序列码信息完成: code=010034354727603421100002880452, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 22:25:52.684  INFO [查询序列码] 开始查询序列码信息
2025-07-19 22:25:52.698  INFO [查询序列码] 数据库连接获取成功
2025-07-19 22:25:52.719  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 22:25:52.724  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 22:25:52.724  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-19 22:25:52.724  INFO [查询序列码] 瓶码不需要查询数量
2025-07-19 22:25:52.725  INFO [查询序列码] 查询序列码信息完成: code=010034354727603421100002880452, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 22:26:27.231  INFO [查询序列码] 开始查询序列码信息
2025-07-19 22:26:27.242  INFO [查询序列码] 数据库连接获取成功
2025-07-19 22:26:27.263  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 22:26:27.268  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 22:26:27.268  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-19 22:26:27.455  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-19 22:26:27.456  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 23:22:35.005  INFO [查询序列码] 开始查询序列码信息
2025-07-19 23:22:35.033  INFO [查询序列码] 数据库连接获取成功
2025-07-19 23:22:35.040  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 23:22:35.044  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 23:22:35.044  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-19 23:22:35.045  INFO [查询序列码] 瓶码不需要查询数量
2025-07-19 23:22:35.046  INFO [查询序列码] 查询序列码信息完成: code=010034354727603421100002880452, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 23:22:44.397  INFO [查询序列码] 开始查询序列码信息
2025-07-19 23:22:44.407  INFO [查询序列码] 数据库连接获取成功
2025-07-19 23:22:44.410  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 23:22:44.413  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 23:22:44.413  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-19 23:22:45.914  INFO [查询序列码] 托盘内箱码数量: 3
2025-07-19 23:22:46.232  INFO [查询序列码] 托盘内瓶码数量: 30
2025-07-19 23:22:46.233  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 23:35:51.879  INFO [查询序列码] 开始查询序列码信息
2025-07-19 23:35:51.895  INFO [查询序列码] 数据库连接获取成功
2025-07-19 23:35:51.897  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 23:35:51.899  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 23:35:51.900  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-19 23:35:51.900  INFO [查询序列码] 瓶码不需要查询数量
2025-07-19 23:35:51.901  INFO [查询序列码] 查询序列码信息完成: code=010034354727603421100002880452, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-19 23:35:56.308  INFO [查询序列码] 开始查询序列码信息
2025-07-19 23:35:56.315  INFO [查询序列码] 数据库连接获取成功
2025-07-19 23:35:56.317  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-19 23:35:56.319  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-19 23:35:56.319  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-19 23:35:56.486  INFO [查询序列码] 托盘内箱码数量: 3
2025-07-19 23:35:56.781  INFO [查询序列码] 托盘内瓶码数量: 30
2025-07-19 23:35:56.782  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 06:29:46.980  INFO [查询序列码] 开始查询序列码信息
2025-07-20 06:29:46.993  INFO [查询序列码] 数据库连接获取成功
2025-07-20 06:29:46.996  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 06:29:46.997  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 06:29:46.997  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-20 06:29:46.998  INFO [查询序列码] 瓶码不需要查询数量
2025-07-20 06:29:46.998  INFO [查询序列码] 查询序列码信息完成: code=010034354727603421100002880452, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 06:30:02.106  INFO [查询序列码] 开始查询序列码信息
2025-07-20 06:30:02.117  INFO [查询序列码] 数据库连接获取成功
2025-07-20 06:30:02.123  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 06:30:02.125  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 06:30:02.125  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 06:30:02.259  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 06:30:02.260  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 06:30:04.434  INFO [查询序列码] 开始查询序列码信息
2025-07-20 06:30:04.444  INFO [查询序列码] 数据库连接获取成功
2025-07-20 06:30:04.447  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 06:30:04.449  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 06:30:04.449  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 06:30:04.557  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 06:30:04.558  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 06:30:05.607  INFO [查询序列码] 开始查询序列码信息
2025-07-20 06:30:05.617  INFO [查询序列码] 数据库连接获取成功
2025-07-20 06:30:05.620  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 06:30:05.622  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 06:30:05.622  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 06:30:05.706  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 06:30:05.707  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 06:32:30.192  INFO 网络连接完成，耗时: 10ms
2025-07-20 06:32:49.790  INFO [查询序列码] 开始查询序列码信息
2025-07-20 06:32:49.799  INFO [查询序列码] 数据库连接获取成功
2025-07-20 06:32:49.801  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 06:32:49.803  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 06:32:49.803  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 06:32:49.893  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 06:32:49.894  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 06:32:59.418  INFO [查询序列码] 开始查询序列码信息
2025-07-20 06:32:59.430  INFO [查询序列码] 数据库连接获取成功
2025-07-20 06:32:59.434  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 06:32:59.436  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 06:32:59.437  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 06:32:59.527  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 06:32:59.529  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 06:33:08.189  INFO [查询序列码] 开始查询序列码信息
2025-07-20 06:33:08.202  INFO [查询序列码] 数据库连接获取成功
2025-07-20 06:33:08.204  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 06:33:08.206  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 06:33:08.207  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 06:33:08.291  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 06:33:08.292  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 06:33:29.826  INFO [查询序列码] 开始查询序列码信息
2025-07-20 06:33:29.837  INFO [查询序列码] 数据库连接获取成功
2025-07-20 06:33:29.840  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 06:33:29.842  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 06:33:29.842  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-20 06:33:29.983  INFO [查询序列码] 托盘内箱码数量: 3
2025-07-20 06:33:30.284  INFO [查询序列码] 托盘内瓶码数量: 30
2025-07-20 06:33:30.285  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 06:34:13.235  INFO [查询序列码] 开始查询序列码信息
2025-07-20 06:34:13.245  INFO [查询序列码] 数据库连接获取成功
2025-07-20 06:34:13.247  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 06:34:13.248  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 06:34:13.248  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 06:34:13.346  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 06:34:13.347  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 06:35:09.380  INFO [查询序列码] 开始查询序列码信息
2025-07-20 06:35:09.392  INFO [查询序列码] 数据库连接获取成功
2025-07-20 06:35:09.395  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 06:35:09.397  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 06:35:09.397  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-20 06:35:09.633  INFO [查询序列码] 托盘内箱码数量: 3
2025-07-20 06:35:09.900  INFO [查询序列码] 托盘内瓶码数量: 30
2025-07-20 06:35:09.900  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 06:35:12.944  INFO [解绑] 开始解绑操作流程
2025-07-20 06:35:12.945  INFO [解绑] 接收到请求参数: code=00703435470002290235
2025-07-20 06:35:12.954  INFO [解绑] 数据库连接获取成功
2025-07-20 06:35:12.955  INFO [解绑] 开始查询序列码: 00703435470002290235
2025-07-20 06:35:12.958  INFO [解绑] 查询到序列码信息: code_flag=2, level_code=4, zero_box=0, batch_no=R202500040
2025-07-20 06:35:12.959  INFO [解绑] 序列码状态正常: code_flag=2
2025-07-20 06:35:12.960  INFO [解绑] 开始判断托盘码是否符合解绑条件
2025-07-20 06:35:12.961  INFO [解绑] 开始解绑托盘下所有大箱及相关瓶码与托盘的关联关系
2025-07-20 06:35:12.961  INFO [解绑] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = 0,
                parentCode = NULL,
                boxCode = NULL,
                palletCode = NULL,
                codeFlag = 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-20 06:35:12.985  INFO [解绑] 更新托盘码信息成功
2025-07-20 06:35:12.985  INFO [解绑] 开始执行解除托盘下所有大箱与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '00703435470002290235'
              AND palletCode = '00703435470002290235'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-20 06:35:13.006  INFO [解绑] 解除托盘下所有大箱与托盘的绑定成功
2025-07-20 06:35:13.007  INFO [解绑] 开始执行解除托盘下所有小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-20 06:35:13.025  INFO [解绑] 解除托盘下所有小瓶与托盘的绑定成功
2025-07-20 06:35:13.026  INFO [解绑] 事务提交成功，解绑操作完成
2025-07-20 06:35:45.795  INFO [查询序列码] 开始查询序列码信息
2025-07-20 06:35:45.808  INFO [查询序列码] 数据库连接获取成功
2025-07-20 06:35:45.811  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 06:35:45.813  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 06:35:45.813  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 06:35:46.105  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 06:35:46.106  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
