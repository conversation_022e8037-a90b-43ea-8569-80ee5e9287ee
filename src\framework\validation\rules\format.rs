use crate::framework::validation::traits::ValidationRule;
use regex::Regex;
use std::sync::OnceLock;

/// 邮箱规则
pub struct Email;

fn email_regex() -> &'static Regex {
    static EMAIL_REGEX: OnceLock<Regex> = OnceLock::new();
    EMAIL_REGEX
        .get_or_init(|| Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$").unwrap())
}

impl ValidationRule for Email {
    fn name(&self) -> &'static str {
        "email"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        email_regex().is_match(value)
    }

    fn message(&self) -> &'static str {
        "请输入有效的邮箱地址"
    }
}

/// 手机号规则
pub struct Phone;

fn phone_regex() -> &'static Regex {
    static PHONE_REGEX: OnceLock<Regex> = OnceLock::new();
    PHONE_REGEX.get_or_init(|| Regex::new(r"^1[3-9]\d{9}$").unwrap())
}

impl ValidationRule for Phone {
    fn name(&self) -> &'static str {
        "phone"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        phone_regex().is_match(value)
    }

    fn message(&self) -> &'static str {
        "请输入有效的手机号码"
    }
}

/// 身份证号规则
pub struct IdCard;

fn id_card_regex() -> &'static Regex {
    static ID_CARD_REGEX: OnceLock<Regex> = OnceLock::new();
    ID_CARD_REGEX.get_or_init(|| {
        Regex::new(r"^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$")
            .unwrap()
    })
}

impl ValidationRule for IdCard {
    fn name(&self) -> &'static str {
        "id_card"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        id_card_regex().is_match(value)
    }

    fn message(&self) -> &'static str {
        "请输入有效的身份证号码"
    }
}

/// URL规则
pub struct Url;

fn url_regex() -> &'static Regex {
    static URL_REGEX: OnceLock<Regex> = OnceLock::new();
    URL_REGEX.get_or_init(|| Regex::new(r"^(https?|ftp)://[^\s/$.?#].[^\s]*$").unwrap())
}

impl ValidationRule for Url {
    fn name(&self) -> &'static str {
        "url"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        url_regex().is_match(value)
    }

    fn message(&self) -> &'static str {
        "请输入有效的URL地址"
    }
}

/// IP地址规则
pub struct Ip;

fn ip_regex() -> &'static Regex {
    static IP_REGEX: OnceLock<Regex> = OnceLock::new();
    IP_REGEX.get_or_init(|| {
        Regex::new(r"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$").unwrap()
    })
}

impl ValidationRule for Ip {
    fn name(&self) -> &'static str {
        "ip"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        ip_regex().is_match(value)
    }

    fn message(&self) -> &'static str {
        "请输入有效的IP地址"
    }
}

/// 正则表达式规则
pub struct RegexRule {
    pattern: String,
    message_text: String,
}

impl RegexRule {
    pub fn new(pattern: &str, message: &str) -> Self {
        Self {
            pattern: pattern.to_string(),
            message_text: message.to_string(),
        }
    }
}

impl ValidationRule for RegexRule {
    fn name(&self) -> &'static str {
        "regex"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        if let Ok(re) = Regex::new(&self.pattern) {
            re.is_match(value)
        } else {
            false
        }
    }

    fn message(&self) -> &str {
        &self.message_text
    }
}
