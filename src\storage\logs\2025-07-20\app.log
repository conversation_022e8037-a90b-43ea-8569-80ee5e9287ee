2025-07-20 06:36:22.910  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-20 06:36:22.911  INFO 数据库连接管理器创建成功
2025-07-20 06:36:22.943  INFO 数据库连接池创建成功
2025-07-20 06:36:22.948  INFO 服务器运行在 0.0.0.0:3000
2025-07-20 06:36:25.563  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-20 06:36:25.566  INFO 数据库连接管理器创建成功
2025-07-20 06:36:25.593  INFO 数据库连接池创建成功
2025-07-20 06:36:25.597  INFO 服务器运行在 0.0.0.0:3000
2025-07-20 06:41:28.759  INFO [查询序列码] 开始查询序列码信息
2025-07-20 06:41:28.798  INFO [查询序列码] 数据库连接获取成功
2025-07-20 06:41:28.800  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 06:41:28.802  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 06:41:28.803  INFO [查询序列码] 开始查询数量信息: levelCode=1
2025-07-20 06:41:28.803  INFO [查询序列码] 瓶码不需要查询数量
2025-07-20 06:41:28.804  INFO [查询序列码] 查询序列码信息完成: code=010034354727603421100002880452, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 06:41:33.664  INFO [查询序列码] 开始查询序列码信息
2025-07-20 06:41:33.676  INFO [查询序列码] 数据库连接获取成功
2025-07-20 06:41:33.679  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 06:41:33.681  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 06:41:33.681  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 06:41:33.766  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 06:41:33.767  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 08:55:34.554  INFO [查询序列码] 开始查询序列码信息
2025-07-20 08:55:34.584  INFO [查询序列码] 数据库连接获取成功
2025-07-20 08:55:34.596  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 08:55:34.600  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 08:55:34.601  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 08:55:34.787  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 08:55:34.787  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 08:55:43.108  INFO [查询序列码] 开始查询序列码信息
2025-07-20 08:55:43.118  INFO [查询序列码] 数据库连接获取成功
2025-07-20 08:55:43.120  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 08:55:43.122  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 08:55:43.122  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 08:55:43.239  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 08:55:43.240  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:04:33.341  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:04:33.352  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:04:33.356  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:04:33.358  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:04:33.358  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:04:33.443  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 09:04:33.444  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:04:36.769  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:04:36.779  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:04:36.783  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:04:36.785  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:04:36.785  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:04:36.899  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 09:04:36.901  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:04:44.576  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:04:44.593  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:04:44.596  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:04:44.598  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:04:44.598  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:04:44.777  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 09:04:44.778  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:04:49.393  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:04:49.405  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:04:49.407  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:04:49.409  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:04:49.410  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:04:49.495  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 09:04:49.496  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:04:55.349  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:04:55.359  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:04:55.361  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:04:55.363  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:04:55.363  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:04:55.448  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 09:04:55.449  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:06:30.549  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:06:30.560  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:06:30.562  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:06:30.564  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:06:30.565  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:06:30.682  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 09:06:30.683  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:11:04.270  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:11:04.283  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:11:04.285  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:11:04.286  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:11:04.287  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:11:04.372  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 09:11:04.373  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:18:16.528  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:18:16.540  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:18:16.552  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-20 09:18:16.557  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-20 09:18:16.558  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:18:16.755  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-20 09:18:16.755  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216900, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-20 09:18:26.483  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:18:26.494  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:18:26.496  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-20 09:18:26.498  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-20 09:18:26.499  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:18:26.616  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-20 09:18:26.617  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216900, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-20 09:18:28.492  INFO [解绑] 开始解绑操作流程
2025-07-20 09:18:28.493  INFO [解绑] 接收到请求参数: code=015034354728010421100000216900
2025-07-20 09:18:28.501  INFO [解绑] 数据库连接获取成功
2025-07-20 09:18:28.502  INFO [解绑] 开始查询序列码: 015034354728010421100000216900
2025-07-20 09:18:28.506  INFO [解绑] 查询到序列码信息: code_flag=2, level_code=3, zero_box=0, batch_no=0000128886
2025-07-20 09:18:28.507  INFO [解绑] 序列码状态正常: code_flag=2
2025-07-20 09:18:28.509  INFO [解绑] 开始判断整箱码是否符合解绑条件
2025-07-20 09:18:28.509  INFO [解绑] 大箱关联的托盘码: 00703435470002346529
2025-07-20 09:18:28.510  INFO [解绑] 开始解绑托盘码下所有大箱及相关瓶码与托盘的关联关系
2025-07-20 09:18:28.511  INFO [解绑] 开始解绑托盘下所有大箱及相关瓶码与托盘的关联关系
2025-07-20 09:18:28.511  INFO [解绑] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = 0,
                parentCode = NULL,
                boxCode = NULL,
                palletCode = NULL,
                codeFlag = 1
            WHERE orderId = '7'
              AND batchNo = '0000128886'
              AND code = '00703435470002346529'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-20 09:18:28.541  INFO [解绑] 更新托盘码信息成功
2025-07-20 09:18:28.542  INFO [解绑] 开始执行解除托盘下所有大箱与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '7'
              AND batchNo = '0000128886'
              AND parentCode = '00703435470002346529'
              AND palletCode = '00703435470002346529'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-20 09:18:28.602  INFO [解绑] 解除托盘下所有大箱与托盘的绑定成功
2025-07-20 09:18:28.603  INFO [解绑] 开始执行解除托盘下所有小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '7'
              AND batchNo = '0000128886'
              AND palletCode = '00703435470002346529'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-20 09:18:28.699  INFO [解绑] 解除托盘下所有小瓶与托盘的绑定成功
2025-07-20 09:18:28.702  INFO [解绑] 事务提交成功，解绑操作完成
2025-07-20 09:18:56.283  INFO 数据库连接字符串: server=127.0.0.1;port=1433;database=gs1_huahai;user id=sa;password=********;Encrypt=true;TrustServerCertificate=true;Integrated Security=false;Column Encryption Setting=Enabled;MultiSubnetFailover=True;Application Intent=ReadWrite;Authentication=SqlPassword
2025-07-20 09:18:56.283  INFO 数据库连接管理器创建成功
2025-07-20 09:18:56.313  INFO 数据库连接池创建成功
2025-07-20 09:18:56.317  INFO 服务器运行在 0.0.0.0:3000
2025-07-20 09:19:00.926  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:19:00.963  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:19:00.966  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-20 09:19:00.968  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-20 09:19:00.968  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:19:01.083  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-20 09:19:01.084  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216900, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-20 09:19:21.611  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:19:21.619  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:19:21.622  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:19:21.624  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:19:21.625  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:19:21.721  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 09:19:21.721  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:19:32.293  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:19:32.304  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:19:32.307  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:19:32.310  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:19:32.310  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:19:32.420  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 09:19:32.421  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:25:04.669  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:25:04.678  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:25:04.681  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:25:04.683  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:25:04.683  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:25:04.773  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 09:25:04.774  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:25:06.792  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:25:06.807  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:25:06.809  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:25:06.811  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:25:06.812  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:25:06.926  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 09:25:06.927  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:25:20.654  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:25:20.668  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:25:20.671  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:25:20.674  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:25:20.674  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:25:20.762  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 09:25:20.763  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:25:26.278  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:25:26.288  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:25:26.290  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:25:26.292  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:25:26.292  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:25:26.401  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 09:25:26.402  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:29:30.132  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:29:30.144  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:29:30.147  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:29:30.149  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:29:30.149  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:29:30.282  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 09:29:30.283  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:29:48.139  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:29:48.151  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:29:48.154  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:29:48.156  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:29:48.156  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:29:48.240  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 09:29:48.241  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:31:52.329  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:31:52.344  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:31:52.346  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-20 09:31:52.347  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-20 09:31:52.348  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:31:52.468  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-20 09:31:52.469  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216900, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-20 09:31:59.915  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:31:59.927  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:31:59.930  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 09:31:59.932  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 09:31:59.933  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:32:00.094  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 09:32:00.095  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 09:34:11.190  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:34:11.200  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:34:11.203  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-20 09:34:11.205  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-20 09:34:11.206  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:34:11.321  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-20 09:34:11.322  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216900, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-20 09:34:14.625  INFO [查询序列码] 开始查询序列码信息
2025-07-20 09:34:14.633  INFO [查询序列码] 数据库连接获取成功
2025-07-20 09:34:14.635  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-20 09:34:14.636  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-20 09:34:14.637  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 09:34:14.754  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-20 09:34:14.755  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216900, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-20 11:39:00.733  INFO [查询序列码] 开始查询序列码信息
2025-07-20 11:39:00.763  INFO [查询序列码] 数据库连接获取成功
2025-07-20 11:39:00.785  INFO [查询序列码] 开始查询生产订单信息: order_id=7
2025-07-20 11:39:00.790  INFO [查询序列码] 开始查询产品信息: product_id=4
2025-07-20 11:39:00.790  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 11:39:00.979  INFO [查询序列码] 箱码内瓶码数量: 24
2025-07-20 11:39:00.980  INFO [查询序列码] 查询序列码信息完成: code=015034354728010421100000216900, batch=0000128886, product=ESCITALOPRAM TABLETS, USP
2025-07-20 11:39:09.898  INFO [查询序列码] 开始查询序列码信息
2025-07-20 11:39:09.908  INFO [查询序列码] 数据库连接获取成功
2025-07-20 11:39:09.927  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 11:39:09.933  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 11:39:09.933  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 11:39:10.114  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 11:39:10.114  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 11:39:20.757  INFO [查询序列码] 开始查询序列码信息
2025-07-20 11:39:20.766  INFO [查询序列码] 数据库连接获取成功
2025-07-20 11:39:20.774  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 11:39:20.777  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 11:39:20.778  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 11:39:20.937  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 11:39:20.938  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 11:39:25.399  INFO [查询序列码] 开始查询序列码信息
2025-07-20 11:39:25.407  INFO [查询序列码] 数据库连接获取成功
2025-07-20 11:39:25.409  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 11:39:25.411  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 11:39:25.411  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 11:39:25.531  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 11:39:25.532  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 11:39:29.820  INFO [查询序列码] 开始查询序列码信息
2025-07-20 11:39:29.833  INFO [查询序列码] 数据库连接获取成功
2025-07-20 11:39:29.835  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 11:39:29.837  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 11:39:29.837  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 11:39:29.948  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 11:39:29.949  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 11:39:36.588  INFO [查询序列码] 开始查询序列码信息
2025-07-20 11:39:36.597  INFO [查询序列码] 数据库连接获取成功
2025-07-20 11:39:36.600  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 11:39:36.602  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 11:39:36.602  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 11:39:36.713  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 11:39:36.714  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 11:41:06.484  INFO [查询序列码] 开始查询序列码信息
2025-07-20 11:41:06.517  INFO [查询序列码] 数据库连接获取成功
2025-07-20 11:41:06.546  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 11:41:06.553  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 11:41:06.553  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 11:41:06.780  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 11:41:06.781  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 11:58:07.989  INFO [查询序列码] 开始查询序列码信息
2025-07-20 11:58:08.007  INFO [查询序列码] 数据库连接获取成功
2025-07-20 11:58:08.027  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 11:58:08.032  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 11:58:08.033  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 11:58:08.220  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 11:58:08.221  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 12:02:57.248  INFO [查询序列码] 开始查询序列码信息
2025-07-20 12:02:57.261  INFO [查询序列码] 数据库连接获取成功
2025-07-20 12:02:57.282  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 12:02:57.287  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 12:02:57.287  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 12:02:57.473  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 12:02:57.474  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 12:03:01.872  INFO [查询序列码] 开始查询序列码信息
2025-07-20 12:03:01.880  INFO [查询序列码] 数据库连接获取成功
2025-07-20 12:03:01.884  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 12:03:01.886  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 12:03:01.886  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 12:03:02.004  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 12:03:02.005  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 12:14:53.829  INFO [查询序列码] 开始查询序列码信息
2025-07-20 12:14:53.848  INFO [查询序列码] 数据库连接获取成功
2025-07-20 12:14:53.871  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 12:14:53.877  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 12:14:53.877  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 12:14:54.071  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 12:14:54.071  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 12:17:12.088  INFO [查询序列码] 开始查询序列码信息
2025-07-20 12:17:12.106  INFO [查询序列码] 数据库连接获取成功
2025-07-20 12:17:12.132  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 12:17:12.141  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 12:17:12.141  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 12:17:12.395  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 12:17:12.395  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 12:18:52.746  INFO [查询序列码] 开始查询序列码信息
2025-07-20 12:18:52.758  INFO [查询序列码] 数据库连接获取成功
2025-07-20 12:18:52.784  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 12:18:52.790  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 12:18:52.790  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 12:18:52.987  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 12:18:52.988  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 15:16:26.667  INFO [查询序列码] 开始查询序列码信息
2025-07-20 15:16:26.694  INFO [查询序列码] 数据库连接获取成功
2025-07-20 15:16:26.718  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 15:16:26.722  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 15:16:26.723  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 15:16:26.903  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 15:16:26.903  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 15:57:18.446  INFO [单据统计] 开始获取单据统计信息
2025-07-20 15:57:18.447  INFO [单据统计] 本周日期范围: ["2025-07-14", "2025-07-15", "2025-07-16", "2025-07-17", "2025-07-18", "2025-07-19", "2025-07-20"]
2025-07-20 15:57:18.475  INFO [单据统计] 数据库连接获取成功
2025-07-20 15:57:18.490  INFO [单据统计] 执行单据统计查询SQL
2025-07-20 15:57:18.500  INFO [单据统计] 统计查询执行成功
2025-07-20 15:57:18.501  INFO [单据统计] 成功获取单据统计信息
2025-07-20 15:57:21.345  INFO [单据分页] 开始获取单据分页列表
2025-07-20 15:57:21.345  INFO [单据分页] 查询参数: page=1, per_page=10, id=None, ware_code=Some(""), status=Some(10), ware_date=None, channel=None
2025-07-20 15:57:21.357  INFO [单据分页] 数据库连接获取成功
2025-07-20 15:57:21.363  INFO [单据分页] 总记录数为0，直接返回空结果
2025-07-20 15:57:23.018  INFO [单据分页] 开始获取单据分页列表
2025-07-20 15:57:23.018  INFO [单据分页] 查询参数: page=1, per_page=10, id=None, ware_code=Some(""), status=Some(10), ware_date=None, channel=None
2025-07-20 15:57:23.027  INFO [单据分页] 数据库连接获取成功
2025-07-20 15:57:23.028  INFO [单据分页] 总记录数为0，直接返回空结果
2025-07-20 15:57:23.755  INFO [单据分页] 开始获取单据分页列表
2025-07-20 15:57:23.755  INFO [单据分页] 查询参数: page=1, per_page=10, id=None, ware_code=Some(""), status=Some(10), ware_date=None, channel=None
2025-07-20 15:57:23.764  INFO [单据分页] 数据库连接获取成功
2025-07-20 15:57:23.765  INFO [单据分页] 总记录数为0，直接返回空结果
2025-07-20 15:57:25.432  INFO [单据列表] 开始获取单据列表
2025-07-20 15:57:25.432  INFO [单据列表] 查询参数: id=None, ware_code=Some("250224"), status=Some(3), ware_date=Some("2025-02-24"), channel=None
2025-07-20 15:57:25.444  INFO [单据列表] 数据库连接获取成功
2025-07-20 15:57:25.472  INFO [单据列表] 查询成功，获取到1条记录
2025-07-20 15:57:25.529  INFO [单据列表] 返回成功，返回1条记录
2025-07-20 15:57:26.993  INFO [单据列表] 开始获取单据列表
2025-07-20 15:57:26.993  INFO [单据列表] 查询参数: id=None, ware_code=Some("250224"), status=Some(3), ware_date=Some("2025-02-24"), channel=None
2025-07-20 15:57:27.001  INFO [单据列表] 数据库连接获取成功
2025-07-20 15:57:27.004  INFO [单据列表] 查询成功，获取到1条记录
2025-07-20 15:57:27.024  INFO [单据列表] 返回成功，返回1条记录
2025-07-20 15:57:29.063  INFO [单据列表] 开始获取单据列表
2025-07-20 15:57:29.064  INFO [单据列表] 查询参数: id=None, ware_code=Some("250224"), status=Some(3), ware_date=Some("2025-02-24"), channel=None
2025-07-20 15:57:29.074  INFO [单据列表] 数据库连接获取成功
2025-07-20 15:57:29.075  INFO [单据列表] 查询成功，获取到1条记录
2025-07-20 15:57:29.095  INFO [单据列表] 返回成功，返回1条记录
2025-07-20 15:57:54.187  INFO [查询序列码] 开始查询序列码信息
2025-07-20 15:57:54.211  INFO [查询序列码] 数据库连接获取成功
2025-07-20 15:57:54.231  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 15:57:54.235  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 15:57:54.236  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 15:57:54.420  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 15:57:54.420  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 16:00:02.757  INFO [查询序列码] 开始查询序列码信息
2025-07-20 16:00:02.770  INFO [查询序列码] 数据库连接获取成功
2025-07-20 16:00:02.791  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 16:00:02.796  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 16:00:02.796  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 16:00:02.981  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 16:00:02.982  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 19:41:40.078  INFO [查询序列码] 开始查询序列码信息
2025-07-20 19:41:40.096  INFO [查询序列码] 数据库连接获取成功
2025-07-20 19:41:40.123  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 19:41:40.128  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 19:41:40.128  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 19:41:40.315  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 19:41:40.315  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 19:43:43.878  INFO [查询序列码] 开始查询序列码信息
2025-07-20 19:43:43.894  INFO [查询序列码] 数据库连接获取成功
2025-07-20 19:43:43.921  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 19:43:43.927  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 19:43:43.927  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 19:43:44.113  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 19:43:44.114  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:19:22.149  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:19:22.181  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:19:22.204  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:19:22.209  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:19:22.213  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:19:22.399  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 21:19:22.399  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:19:30.006  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:19:30.017  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:19:30.020  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:19:30.022  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:19:30.022  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:19:30.136  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:19:30.137  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:22:24.829  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:22:24.843  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:22:24.869  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:22:24.875  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:22:24.875  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:22:25.054  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:22:25.055  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:22:41.009  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:22:41.022  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:22:41.025  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:22:41.026  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:22:41.026  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:22:41.145  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:22:41.146  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:22:44.744  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:22:44.755  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:22:44.757  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:22:44.759  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:22:44.760  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:22:44.854  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 21:22:44.855  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:27:19.783  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:27:19.796  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:27:19.825  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:27:19.832  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:27:19.833  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:27:20.018  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 21:27:20.019  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:27:23.362  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:27:23.372  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:27:23.375  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:27:23.377  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:27:23.378  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:27:23.489  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:27:23.490  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:27:26.033  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:27:26.041  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:27:26.043  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:27:26.044  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:27:26.044  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:27:26.160  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:27:26.160  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:27:28.371  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:27:28.379  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:27:28.381  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:27:28.382  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:27:28.382  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:27:28.499  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:27:28.499  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:27:33.056  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:27:33.068  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:27:33.072  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:27:33.074  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:27:33.074  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:27:33.188  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:27:33.190  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:27:35.294  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:27:35.305  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:27:35.307  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:27:35.310  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:27:35.310  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:27:35.438  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:27:35.439  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:27:37.036  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:27:37.046  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:27:37.049  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:27:37.053  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:27:37.056  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:27:37.170  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:27:37.171  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:27:38.386  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:27:38.397  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:27:38.400  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:27:38.402  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:27:38.402  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:27:38.524  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:27:38.524  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:27:46.317  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:27:46.327  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:27:46.330  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:27:46.333  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:27:46.333  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:27:46.461  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:27:46.462  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:27:57.761  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:27:57.773  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:27:57.775  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:27:57.778  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:27:57.778  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:27:57.899  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:27:57.900  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:34:45.242  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:34:45.254  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:34:45.282  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:34:45.288  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:34:45.289  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:34:45.473  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 21:34:45.474  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:34:47.415  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:34:47.424  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:34:47.427  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:34:47.429  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:34:47.430  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:34:47.549  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:34:47.550  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:34:49.875  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:34:49.887  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:34:49.890  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:34:49.893  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:34:49.893  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:34:50.022  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:34:50.023  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:34:53.969  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:34:53.982  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:34:53.985  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:34:53.987  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:34:53.987  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:34:54.106  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:34:54.107  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:35:11.547  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:35:11.557  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:35:11.585  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:35:11.590  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:35:11.590  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:35:11.767  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:35:11.767  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:36:20.118  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:36:20.133  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:36:20.159  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:36:20.164  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:36:20.165  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:36:20.334  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 21:36:20.334  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:36:22.930  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:36:22.938  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:36:22.939  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:36:22.941  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:36:22.941  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:36:23.057  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:36:23.058  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:36:25.766  INFO [聚合] 开始聚合操作流程
2025-07-20 21:36:25.767  INFO [聚合] 接收到请求参数: aggregate_type=newPackage, box_codes数量=2
2025-07-20 21:36:25.785  INFO [聚合] 数据库连接获取成功
2025-07-20 21:36:25.787  INFO [聚合] |新建包装| 开始执行新建包装模式
2025-07-20 21:36:25.787  INFO [聚合] |新建包装| 处理箱码 1/2: 00503435470000872620
2025-07-20 21:36:25.791  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-20 21:36:25.792  INFO [聚合] |新建包装| 处理箱码 2/2: 015034354727603921100000127124
2025-07-20 21:36:25.793  INFO [聚合] |新建包装| 查询到箱码信息: code_flag=2, level_code=3
2025-07-20 21:36:25.794  INFO [聚合] |新建包装| 开始执行新建包装操作
2025-07-20 21:36:25.799  INFO [取码函数] 查询生产订单信息SQL: SELECT TOP 1 id, productId, poNumber, batchNo FROM [produce_order] WHERE id = '4'
2025-07-20 21:36:25.806  INFO [取码函数] 查询产品信息SQL: SELECT TOP 1 id, productCode, packageRules, codeStyle FROM [basic_product] WHERE id = '3'
2025-07-20 21:36:25.809  INFO [取码函数] 查询序列码信息SQL: SELECT TOP 1 tc.id, tc.code, tc.snCode, tc.fileID 
        FROM [transit_code] tc
        JOIN [sn_record] sr ON sr.id = tc.fileID
        WHERE tc.orderId = '4' 
        AND tc.levelCode = '4' 
        AND tc.typeFlag = '1' 
        AND tc.codeFlag IN (0, 1)
        AND sr.filterValue = '7'
        AND sr.resCode = '0343547'
        AND sr.typeFlag = '1'
        AND sr.codeStyle = 'tracelink'
        ORDER BY tc.id
2025-07-20 21:36:25.829  INFO [取码函数] 数据库中符合条件的序列码数量: 1
2025-07-20 21:36:25.830  INFO [聚合] |新建包装| 获取托盘码结果: {"errcode": Number(0), "errmsg": String("序列码获取成功"), "data": Array [String("00703435470002290235")]}
2025-07-20 21:36:25.831  INFO [聚合] |新建包装| 获取托盘码数据: [String("00703435470002290235")]
2025-07-20 21:36:25.831  INFO [聚合] |新建包装| 获取到托盘码: 00703435470002290235
2025-07-20 21:36:25.832  INFO [聚合] |新建包装| 开始执行更新瓶码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290235',
                updateTime = '2025-07-20 21:36:25'
            WHERE levelCode = 1
              AND codeFlag = 2
              AND boxCode IN ('00503435470000872620','015034354727603921100000127124')
        
2025-07-20 21:36:26.033  INFO [聚合] |新建包装| 更新瓶码信息成功
2025-07-20 21:36:26.033  INFO [聚合] |新建包装| 开始执行更新箱码SQL: 
            UPDATE transit_code
            SET parentCode = '00703435470002290235',
                palletCode = '00703435470002290235',
                updateTime = '2025-07-20 21:37:25'
            WHERE levelCode = 3
              AND codeFlag = 2
              AND code IN ('00503435470000872620','015034354727603921100000127124')
        
2025-07-20 21:36:26.038  INFO [聚合] |新建包装| 更新箱码信息成功
2025-07-20 21:36:26.039  INFO [聚合] |新建包装| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET palletCode = '00703435470002290235',
                amount = 2,
                codeFlag = 2,
                updateTime = '2025-07-20 21:41:25'
            WHERE levelCode = 4
              AND code = '00703435470002290235'
        
2025-07-20 21:36:26.042  INFO [聚合] |新建包装| 更新托盘码信息成功
2025-07-20 21:36:26.043  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-20 21:42:26.883  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:42:26.921  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:42:26.936  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:42:26.943  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:42:26.943  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:42:27.138  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:42:27.138  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:46:02.816  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:46:02.828  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:46:02.831  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:46:02.833  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:46:02.834  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-20 21:46:03.015  INFO [查询序列码] 托盘内箱码数量: 2
2025-07-20 21:46:03.336  INFO [查询序列码] 托盘内瓶码数量: 18
2025-07-20 21:46:03.337  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:46:17.242  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:46:17.252  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:46:17.255  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:46:17.257  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:46:17.257  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:46:17.344  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 21:46:17.345  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:46:19.502  INFO [取出] 开始取出操作流程
2025-07-20 21:46:19.503  INFO [取出] 接收到请求参数: code=00503435470000872620
2025-07-20 21:46:19.518  INFO [取出] 数据库连接获取成功
2025-07-20 21:46:19.519  INFO [取出] 开始查询序列码: 00503435470000872620
2025-07-20 21:46:19.522  INFO [取出] 查询到序列码信息: code_flag=2, level_code=3, zero_box=1, batch_no=R202500040
2025-07-20 21:46:19.523  INFO [取出] 序列码状态正常: code_flag=2
2025-07-20 21:46:19.525  INFO [取出] 开始判断箱码是否符合取出条件
2025-07-20 21:46:19.525  INFO [取出] 大箱关联的托盘码: 00703435470002290235
2025-07-20 21:46:19.527  INFO [取出] 开始从托盘中取出箱码
2025-07-20 21:46:19.527  INFO [取出] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount - 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-20 21:46:19.533  INFO [取出] 更新托盘码信息成功
2025-07-20 21:46:19.534  INFO [取出] 开始执行解除箱码与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00503435470000872620'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-20 21:46:19.538  INFO [取出] 解除箱码与托盘的绑定成功
2025-07-20 21:46:19.540  INFO [取出] 开始执行解除小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '00503435470000872620'
              AND boxCode = '00503435470000872620'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-20 21:46:19.572  INFO [取出] 解除小瓶与托盘的绑定成功
2025-07-20 21:46:19.574  INFO [取出] 事务提交成功，取出操作完成
2025-07-20 21:47:19.697  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:47:19.708  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:47:19.711  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:47:19.714  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:47:19.714  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-20 21:47:19.976  INFO [查询序列码] 托盘内箱码数量: 1
2025-07-20 21:47:20.286  INFO [查询序列码] 托盘内瓶码数量: 12
2025-07-20 21:47:20.287  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:47:25.336  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:47:25.346  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:47:25.350  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:47:25.352  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:47:25.353  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:47:25.467  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:47:25.468  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:48:01.240  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:48:01.252  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:48:01.255  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:48:01.258  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:48:01.258  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:48:01.404  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:48:01.405  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:48:04.312  INFO [查询序列码] 开始查询序列码信息
2025-07-20 21:48:04.321  INFO [查询序列码] 数据库连接获取成功
2025-07-20 21:48:04.324  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 21:48:04.326  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 21:48:04.326  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 21:48:04.440  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 21:48:04.441  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 21:48:56.077  INFO [聚合] 开始聚合操作流程
2025-07-20 21:48:56.077  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-20 21:48:56.089  INFO [聚合] 数据库连接获取成功
2025-07-20 21:48:56.099  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-20 21:48:56.109  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-20 21:48:56.110  INFO [聚合] |放入| 处理箱码 1/1: 015034354727603921100000127123
2025-07-20 21:48:56.112  INFO [聚合] |放入| 查询到箱码信息: code_flag=2, level_code=3
2025-07-20 21:48:56.141  INFO [聚合] |放入| 开始执行放入操作
2025-07-20 21:48:56.142  INFO [聚合] |放入| 处理箱码: 015034354727603921100000127123
2025-07-20 21:48:56.143  INFO [聚合] |放入| 开始执行更新瓶码SQL: 
                UPDATE transit_code
                SET palletCode = '00703435470002290235',
                    updateTime = '2025-07-20 21:48:56'
                WHERE levelCode = 1
                  AND codeFlag = 2
                  AND boxCode = '015034354727603921100000127123'
            
2025-07-20 21:48:56.318  INFO [聚合] |放入| 更新瓶码信息成功
2025-07-20 21:48:56.318  INFO [聚合] |放入| 开始执行更新箱码SQL: 
                UPDATE transit_code
                SET parentCode = '00703435470002290235',
                    palletCode = '00703435470002290235',
                    updateTime = '2025-07-20 21:49:56'
                WHERE levelCode = 3
                  AND codeFlag = 2
                  AND code = '015034354727603921100000127123'
            
2025-07-20 21:48:56.323  INFO [聚合] |放入| 更新箱码信息成功
2025-07-20 21:48:56.324  INFO [聚合] |放入| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET updateTime = '2025-07-20 21:51:56'
            WHERE levelCode = 4
              AND codeFlag = 2
              AND code = '00703435470002290235'
        
2025-07-20 21:48:56.328  INFO [聚合] |放入| 更新托盘码信息成功
2025-07-20 21:48:56.360  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-20 22:16:42.299  INFO [查询序列码] 开始查询序列码信息
2025-07-20 22:16:42.314  INFO [查询序列码] 数据库连接获取成功
2025-07-20 22:16:42.317  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 22:16:42.318  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 22:16:42.319  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-20 22:16:42.497  INFO [查询序列码] 托盘内箱码数量: 2
2025-07-20 22:16:42.810  INFO [查询序列码] 托盘内瓶码数量: 24
2025-07-20 22:16:42.812  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 22:16:46.963  INFO [查询序列码] 开始查询序列码信息
2025-07-20 22:16:46.972  INFO [查询序列码] 数据库连接获取成功
2025-07-20 22:16:46.974  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 22:16:46.976  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 22:16:46.977  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 22:16:47.099  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 22:16:47.100  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 22:16:48.896  INFO [查询序列码] 开始查询序列码信息
2025-07-20 22:16:48.906  INFO [查询序列码] 数据库连接获取成功
2025-07-20 22:16:48.909  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 22:16:48.911  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 22:16:48.911  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 22:16:49.025  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 22:16:49.026  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 22:16:51.712  INFO [查询序列码] 开始查询序列码信息
2025-07-20 22:16:51.723  INFO [查询序列码] 数据库连接获取成功
2025-07-20 22:16:51.726  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 22:16:51.728  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 22:16:51.729  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 22:16:51.844  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-20 22:16:51.844  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127123, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 22:16:54.265  INFO [查询序列码] 开始查询序列码信息
2025-07-20 22:16:54.275  INFO [查询序列码] 数据库连接获取成功
2025-07-20 22:16:54.280  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 22:16:54.287  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 22:16:54.287  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 22:16:54.370  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 22:16:54.372  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 22:22:06.523  INFO [查询序列码] 开始查询序列码信息
2025-07-20 22:22:06.535  INFO [查询序列码] 数据库连接获取成功
2025-07-20 22:22:06.538  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 22:22:06.540  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 22:22:06.541  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 22:22:06.626  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 22:22:06.627  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 22:22:08.268  INFO [查询序列码] 开始查询序列码信息
2025-07-20 22:22:08.280  INFO [查询序列码] 数据库连接获取成功
2025-07-20 22:22:08.283  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 22:22:08.285  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 22:22:08.286  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 22:22:08.371  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 22:22:08.372  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 22:22:11.219  INFO [查询序列码] 开始查询序列码信息
2025-07-20 22:22:11.229  INFO [查询序列码] 数据库连接获取成功
2025-07-20 22:22:11.232  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 22:22:11.234  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 22:22:11.234  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-20 22:22:11.508  INFO [查询序列码] 托盘内箱码数量: 2
2025-07-20 22:22:12.038  INFO [查询序列码] 托盘内瓶码数量: 24
2025-07-20 22:22:12.039  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-20 22:22:15.714  INFO [查询序列码] 开始查询序列码信息
2025-07-20 22:22:15.725  INFO [查询序列码] 数据库连接获取成功
2025-07-20 22:22:15.728  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-20 22:22:15.730  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-20 22:22:15.731  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-20 22:22:15.817  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-20 22:22:15.818  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 00:16:16.871  INFO [查询序列码] 开始查询序列码信息
2025-07-21 00:16:16.907  INFO [查询序列码] 数据库连接获取成功
2025-07-21 00:16:16.937  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 00:16:16.944  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 00:16:16.945  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 00:16:17.150  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 00:16:17.150  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 00:16:18.854  INFO [查询序列码] 开始查询序列码信息
2025-07-21 00:16:18.867  INFO [查询序列码] 数据库连接获取成功
2025-07-21 00:16:18.869  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 00:16:18.872  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 00:16:18.875  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 00:16:18.962  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 00:16:18.963  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 00:16:20.771  INFO [查询序列码] 开始查询序列码信息
2025-07-21 00:16:20.782  INFO [查询序列码] 数据库连接获取成功
2025-07-21 00:16:20.785  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 00:16:20.787  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 00:16:20.788  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 00:16:21.843  INFO [查询序列码] 托盘内箱码数量: 2
2025-07-21 00:16:22.143  INFO [查询序列码] 托盘内瓶码数量: 24
2025-07-21 00:16:22.144  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 00:16:29.143  INFO [查询序列码] 开始查询序列码信息
2025-07-21 00:16:29.153  INFO [查询序列码] 数据库连接获取成功
2025-07-21 00:16:29.156  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 00:16:29.158  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 00:16:29.158  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 00:16:29.245  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 00:16:29.246  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 00:16:33.400  INFO [聚合] 开始聚合操作流程
2025-07-21 00:16:33.401  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-21 00:16:33.416  INFO [聚合] 数据库连接获取成功
2025-07-21 00:16:33.418  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-21 00:16:33.421  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-21 00:16:33.421  INFO [聚合] |放入| 处理箱码 1/1: 00503435470000872620
2025-07-21 00:16:33.423  INFO [聚合] |放入| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 00:16:33.423  INFO [聚合] |放入| 开始执行放入操作
2025-07-21 00:16:33.424  INFO [聚合] |放入| 处理箱码: 00503435470000872620
2025-07-21 00:16:33.425  INFO [聚合] |放入| 开始执行更新瓶码SQL: 
                UPDATE transit_code
                SET palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 00:16:33'
                WHERE levelCode = 1
                  AND codeFlag = 2
                  AND boxCode = '00503435470000872620'
            
2025-07-21 00:16:33.542  INFO [聚合] |放入| 更新瓶码信息成功
2025-07-21 00:16:33.543  INFO [聚合] |放入| 开始执行更新箱码SQL: 
                UPDATE transit_code
                SET parentCode = '00703435470002290235',
                    palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 00:17:33'
                WHERE levelCode = 3
                  AND codeFlag = 2
                  AND code = '00503435470000872620'
            
2025-07-21 00:16:33.548  INFO [聚合] |放入| 更新箱码信息成功
2025-07-21 00:16:33.548  INFO [聚合] |放入| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET updateTime = '2025-07-21 00:19:33'
            WHERE levelCode = 4
              AND codeFlag = 2
              AND code = '00703435470002290235'
        
2025-07-21 00:16:33.552  INFO [聚合] |放入| 更新托盘码信息成功
2025-07-21 00:16:33.553  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-21 00:17:15.324  INFO [查询序列码] 开始查询序列码信息
2025-07-21 00:17:15.334  INFO [查询序列码] 数据库连接获取成功
2025-07-21 00:17:15.336  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 00:17:15.338  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 00:17:15.340  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 00:17:15.425  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 00:17:15.426  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 00:17:17.250  INFO [查询序列码] 开始查询序列码信息
2025-07-21 00:17:17.263  INFO [查询序列码] 数据库连接获取成功
2025-07-21 00:17:17.266  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 00:17:17.268  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 00:17:17.269  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 00:17:17.356  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 00:17:17.357  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 00:17:22.052  INFO [查询序列码] 开始查询序列码信息
2025-07-21 00:17:22.064  INFO [查询序列码] 数据库连接获取成功
2025-07-21 00:17:22.067  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 00:17:22.069  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 00:17:22.069  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 00:17:22.156  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 00:17:22.156  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 00:19:21.967  INFO [查询序列码] 开始查询序列码信息
2025-07-21 00:19:21.978  INFO [查询序列码] 数据库连接获取成功
2025-07-21 00:19:21.981  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 00:19:21.983  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 00:19:21.984  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 00:19:22.120  INFO [查询序列码] 托盘内箱码数量: 3
2025-07-21 00:19:22.408  INFO [查询序列码] 托盘内瓶码数量: 30
2025-07-21 00:19:22.409  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 06:37:26.750  INFO [查询序列码] 开始查询序列码信息
2025-07-21 06:37:26.784  INFO [查询序列码] 数据库连接获取成功
2025-07-21 06:37:26.788  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 06:37:26.790  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 06:37:26.791  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 06:37:26.973  INFO [查询序列码] 托盘内箱码数量: 3
2025-07-21 06:37:27.275  INFO [查询序列码] 托盘内瓶码数量: 30
2025-07-21 06:37:27.276  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 06:37:34.162  INFO [查询序列码] 开始查询序列码信息
2025-07-21 06:37:34.174  INFO [查询序列码] 数据库连接获取成功
2025-07-21 06:37:34.177  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 06:37:34.179  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 06:37:34.180  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 06:37:34.349  INFO [查询序列码] 箱码内瓶码数量: 12
2025-07-21 06:37:34.349  INFO [查询序列码] 查询序列码信息完成: code=015034354727603921100000127124, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 06:37:50.180  INFO [查询序列码] 开始查询序列码信息
2025-07-21 06:37:50.192  INFO [查询序列码] 数据库连接获取成功
2025-07-21 06:37:50.195  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 06:37:50.199  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 06:37:50.201  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 06:37:50.285  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 06:37:50.286  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 06:37:56.229  INFO [查询序列码] 开始查询序列码信息
2025-07-21 06:37:56.240  INFO [查询序列码] 数据库连接获取成功
2025-07-21 06:37:56.243  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 06:37:56.245  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 06:37:56.245  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 06:37:56.333  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 06:37:56.334  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 06:37:58.420  INFO [取出] 开始取出操作流程
2025-07-21 06:37:58.421  INFO [取出] 接收到请求参数: code=00503435470000872620
2025-07-21 06:37:58.434  INFO [取出] 数据库连接获取成功
2025-07-21 06:37:58.435  INFO [取出] 开始查询序列码: 00503435470000872620
2025-07-21 06:37:58.437  INFO [取出] 查询到序列码信息: code_flag=2, level_code=3, zero_box=1, batch_no=R202500040
2025-07-21 06:37:58.437  INFO [取出] 序列码状态正常: code_flag=2
2025-07-21 06:37:58.439  INFO [取出] 开始判断箱码是否符合取出条件
2025-07-21 06:37:58.439  INFO [取出] 大箱关联的托盘码: 00703435470002290235
2025-07-21 06:37:58.440  INFO [取出] 开始从托盘中取出箱码
2025-07-21 06:37:58.441  INFO [取出] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount - 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-21 06:37:58.446  INFO [取出] 更新托盘码信息成功
2025-07-21 06:37:58.446  INFO [取出] 开始执行解除箱码与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00503435470000872620'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-21 06:37:58.452  INFO [取出] 解除箱码与托盘的绑定成功
2025-07-21 06:37:58.452  INFO [取出] 开始执行解除小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '00503435470000872620'
              AND boxCode = '00503435470000872620'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-21 06:37:58.478  INFO [取出] 解除小瓶与托盘的绑定成功
2025-07-21 06:37:58.481  INFO [取出] 事务提交成功，取出操作完成
2025-07-21 06:38:21.546  INFO [查询序列码] 开始查询序列码信息
2025-07-21 06:38:21.578  INFO [查询序列码] 数据库连接获取成功
2025-07-21 06:38:21.580  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 06:38:21.583  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 06:38:21.583  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 06:38:21.726  INFO [查询序列码] 托盘内箱码数量: 2
2025-07-21 06:38:22.022  INFO [查询序列码] 托盘内瓶码数量: 24
2025-07-21 06:38:22.023  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 06:38:25.328  INFO [查询序列码] 开始查询序列码信息
2025-07-21 06:38:25.341  INFO [查询序列码] 数据库连接获取成功
2025-07-21 06:38:25.345  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 06:38:25.347  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 06:38:25.348  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 06:38:25.433  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 06:38:25.434  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 06:38:30.480  INFO [聚合] 开始聚合操作流程
2025-07-21 06:38:30.481  INFO [聚合] 接收到请求参数: aggregate_type=putInto, box_codes数量=1
2025-07-21 06:38:30.492  INFO [聚合] 数据库连接获取成功
2025-07-21 06:38:30.493  INFO [聚合] |放入| 托盘码: 00703435470002290235
2025-07-21 06:38:30.494  INFO [聚合] |放入| 查询到托盘码信息: code_flag=2, level_code=4
2025-07-21 06:38:30.495  INFO [聚合] |放入| 处理箱码 1/1: 00503435470000872620
2025-07-21 06:38:30.497  INFO [聚合] |放入| 查询到箱码信息: code_flag=2, level_code=3
2025-07-21 06:38:30.497  INFO [聚合] |放入| 开始执行放入操作
2025-07-21 06:38:30.498  INFO [聚合] |放入| 处理箱码: 00503435470000872620
2025-07-21 06:38:30.498  INFO [聚合] |放入| 开始执行更新瓶码SQL: 
                UPDATE transit_code
                SET palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 06:38:30'
                WHERE levelCode = 1
                  AND codeFlag = 2
                  AND boxCode = '00503435470000872620'
            
2025-07-21 06:38:30.607  INFO [聚合] |放入| 更新瓶码信息成功
2025-07-21 06:38:30.608  INFO [聚合] |放入| 开始执行更新箱码SQL: 
                UPDATE transit_code
                SET parentCode = '00703435470002290235',
                    palletCode = '00703435470002290235',
                    updateTime = '2025-07-21 06:39:30'
                WHERE levelCode = 3
                  AND codeFlag = 2
                  AND code = '00503435470000872620'
            
2025-07-21 06:38:30.609  INFO [聚合] |放入| 更新箱码信息成功
2025-07-21 06:38:30.610  INFO [聚合] |放入| 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET updateTime = '2025-07-21 06:41:30'
            WHERE levelCode = 4
              AND codeFlag = 2
              AND code = '00703435470002290235'
        
2025-07-21 06:38:30.611  INFO [聚合] |放入| 更新托盘码信息成功
2025-07-21 06:38:30.614  INFO [聚合] 事务提交成功，聚合操作完成
2025-07-21 06:40:42.738  INFO [查询序列码] 开始查询序列码信息
2025-07-21 06:40:42.749  INFO [查询序列码] 数据库连接获取成功
2025-07-21 06:40:42.752  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 06:40:42.754  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 06:40:42.755  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 06:40:42.847  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 06:40:42.848  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 06:40:45.592  INFO [取出] 开始取出操作流程
2025-07-21 06:40:45.592  INFO [取出] 接收到请求参数: code=00503435470000872620
2025-07-21 06:40:45.604  INFO [取出] 数据库连接获取成功
2025-07-21 06:40:45.605  INFO [取出] 开始查询序列码: 00503435470000872620
2025-07-21 06:40:45.607  INFO [取出] 查询到序列码信息: code_flag=2, level_code=3, zero_box=1, batch_no=R202500040
2025-07-21 06:40:45.609  INFO [取出] 序列码状态正常: code_flag=2
2025-07-21 06:40:45.611  INFO [取出] 开始判断箱码是否符合取出条件
2025-07-21 06:40:45.612  INFO [取出] 大箱关联的托盘码: 00703435470002290235
2025-07-21 06:40:45.612  INFO [取出] 开始从托盘中取出箱码
2025-07-21 06:40:45.612  INFO [取出] 开始执行更新托盘码SQL: 
            UPDATE transit_code
            SET amount = amount - 1
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00703435470002290235'
              AND levelCode = 4
              AND codeFlag = 2
        
2025-07-21 06:40:45.613  INFO [取出] 更新托盘码信息成功
2025-07-21 06:40:45.613  INFO [取出] 开始执行解除箱码与托盘的绑定SQL: 
            UPDATE transit_code
            SET parentCode = NULL,
                palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND code = '00503435470000872620'
              AND levelCode = 3
              AND codeFlag = 2
        
2025-07-21 06:40:45.614  INFO [取出] 解除箱码与托盘的绑定成功
2025-07-21 06:40:45.614  INFO [取出] 开始执行解除小瓶与托盘的绑定SQL: 
            UPDATE transit_code
            SET palletCode = NULL
            WHERE orderId = '4'
              AND batchNo = 'R202500040'
              AND parentCode = '00503435470000872620'
              AND boxCode = '00503435470000872620'
              AND palletCode = '00703435470002290235'
              AND levelCode = 1
              AND codeFlag = 2
        
2025-07-21 06:40:45.634  INFO [取出] 解除小瓶与托盘的绑定成功
2025-07-21 06:40:45.638  INFO [取出] 事务提交成功，取出操作完成
2025-07-21 06:42:42.860  INFO [查询序列码] 开始查询序列码信息
2025-07-21 06:42:42.872  INFO [查询序列码] 数据库连接获取成功
2025-07-21 06:42:42.874  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 06:42:42.876  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 06:42:42.877  INFO [查询序列码] 开始查询数量信息: levelCode=4
2025-07-21 06:42:43.157  INFO [查询序列码] 托盘内箱码数量: 2
2025-07-21 06:42:43.850  INFO [查询序列码] 托盘内瓶码数量: 24
2025-07-21 06:42:43.851  INFO [查询序列码] 查询序列码信息完成: code=00703435470002290235, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
2025-07-21 06:42:46.174  INFO [查询序列码] 开始查询序列码信息
2025-07-21 06:42:46.193  INFO [查询序列码] 数据库连接获取成功
2025-07-21 06:42:46.200  INFO [查询序列码] 开始查询生产订单信息: order_id=4
2025-07-21 06:42:46.206  INFO [查询序列码] 开始查询产品信息: product_id=3
2025-07-21 06:42:46.207  INFO [查询序列码] 开始查询数量信息: levelCode=3
2025-07-21 06:42:46.297  INFO [查询序列码] 箱码内瓶码数量: 6
2025-07-21 06:42:46.298  INFO [查询序列码] 查询序列码信息完成: code=00503435470000872620, batch=R202500040, product=DONEPEZIL HYDROCHLORIDE TABLETS, USP
