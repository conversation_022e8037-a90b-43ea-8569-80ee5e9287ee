use crate::framework::validation::traits::ValidationRule;

/// 长度验证规则
pub struct Length {
    min: usize,
    max: usize,
    message_template: String,
}

impl Length {
    pub fn new(min: usize, max: usize) -> Self {
        Self {
            min,
            max,
            message_template: format!("字段长度必须在 {} 到 {} 之间", min, max),
        }
    }

    pub fn with_message(mut self, message: &str) -> Self {
        self.message_template = message.to_string();
        self
    }
}

impl ValidationRule for Length {
    fn name(&self) -> &'static str {
        "length"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        let len = value.chars().count();
        len >= self.min && len <= self.max
    }

    fn message(&self) -> &str {
        &self.message_template
    }
}

/// 最小长度验证规则
pub struct MinLength {
    min: usize,
    message_template: String,
}

impl MinLength {
    pub fn new(min: usize) -> Self {
        Self {
            min,
            message_template: format!("字段长度不能小于 {}", min),
        }
    }

    pub fn with_message(mut self, message: &str) -> Self {
        self.message_template = message.to_string();
        self
    }
}

impl ValidationRule for MinLength {
    fn name(&self) -> &'static str {
        "min"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        value.chars().count() >= self.min
    }

    fn message(&self) -> &str {
        &self.message_template
    }
}

/// 最大长度验证规则
pub struct MaxLength {
    max: usize,
    message_template: String,
}

impl MaxLength {
    pub fn new(max: usize) -> Self {
        Self {
            max,
            message_template: format!("字段长度不能大于 {}", max),
        }
    }

    pub fn with_message(mut self, message: &str) -> Self {
        self.message_template = message.to_string();
        self
    }
}

impl ValidationRule for MaxLength {
    fn name(&self) -> &'static str {
        "max"
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        value.chars().count() <= self.max
    }

    fn message(&self) -> &str {
        &self.message_template
    }
}
