use crate::app::models::produce::Order;
use crate::app::providers::DatabaseServiceProvider;
use crate::app::traits::response::track;
use axum::{extract::Query, extract::State, response::IntoResponse};
use futures_util::TryStreamExt;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tiberius::ToSql;
use tracing::{error, info};

/// 查询参数
#[derive(Debug, Deserialize)]
pub struct QueryParams {
    /// 页码
    #[serde(default = "default_page")]
    pub page: i32,
    /// 每页数量
    #[serde(rename = "limit", default = "default_limit")]
    pub per_page: i32,
    /// 批次号
    #[serde(rename = "batchNo")]
    pub batch_no: Option<String>,
    /// 订单号
    #[serde(rename = "poNumber")]
    pub po_number: Option<String>,
    /// 状态
    #[serde(rename = "estate")]
    pub estate: Option<i8>,
}

/// 默认页码
fn default_page() -> i32 {
    1
}

/// 默认每页数量
fn default_limit() -> i32 {
    20
}

/// 查询结果
#[derive(Debug, Serialize)]
pub struct QueryResult {
    /// 数据列表
    pub list: Vec<Order>,
    /// 总记录数
    pub count: i32,
}

/// 订单控制器
pub struct OrderController;

impl OrderController {
    /// 查询订单列表（分页）
    pub async fn page(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Query(params): Query<QueryParams>,
    ) -> impl IntoResponse {
        info!("[订单分页] 开始获取订单分页列表");
        info!(
            "[订单分页] 查询参数: page={}, per_page={}, batch_no={:?}, po_number={:?}, estate={:?}",
            params.page, params.per_page, params.batch_no, params.po_number, params.estate
        );

        // 验证分页参数
        let page = if params.page <= 0 {
            info!("[订单分页] 页码参数不合法({}), 使用默认值1", params.page);
            1
        } else {
            params.page
        };

        // 限制每页数量，防止请求过大导致性能问题
        let max_per_page = 100; // 设置最大每页数量为100
        let per_page = if params.per_page <= 0 {
            info!(
                "[订单分页] 每页数量参数不合法({}), 使用默认值20",
                params.per_page
            );
            20
        } else if params.per_page > max_per_page {
            info!(
                "[订单分页] 每页数量过大({}), 已限制为最大值{}",
                params.per_page, max_per_page
            );
            max_per_page
        } else {
            params.per_page
        };

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[订单分页] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[订单分页] 获取数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 构建查询条件
        let mut where_conditions = Vec::new();
        let mut query_params: Vec<Box<dyn ToSql>> = Vec::new();
        let mut param_index = 1;

        // 添加查询条件
        if let Some(batch_no) = &params.batch_no {
            if !batch_no.trim().is_empty() {
                where_conditions.push(format!("po.batchNo LIKE @P{}", param_index));
                query_params.push(Box::new(format!("%{}%", batch_no.trim())));
                param_index += 1;
            }
        }

        if let Some(po_number) = &params.po_number {
            if !po_number.trim().is_empty() {
                where_conditions.push(format!("po.poNumber LIKE @P{}", param_index));
                query_params.push(Box::new(format!("%{}%", po_number.trim())));
                param_index += 1;
            }
        }

        if let Some(estate) = params.estate {
            where_conditions.push(format!("po.estate = @P{}", param_index));
            query_params.push(Box::new(estate as i32));
            param_index += 1;
        }

        // 组合 WHERE 子句
        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 计算分页参数
        let offset = (page - 1) * per_page;
        let limit = per_page;

        // 构建计数 SQL - 获取满足条件的记录总数
        let count_sql = format!(
            r#"
            SELECT COUNT(1) as total
            FROM produce_order po WITH (NOLOCK)
            {where_clause}
            "#,
            where_clause = where_clause
        );

        // 构建参数引用 - 为计数查询创建一个独立的参数引用
        let count_params_ref: Vec<&dyn ToSql> = query_params.iter().map(|p| p.as_ref()).collect();

        // 执行计数查询
        let total = match conn.query(&count_sql, &count_params_ref).await {
            Ok(stream) => match stream.into_first_result().await {
                Ok(rows) => {
                    if !rows.is_empty() {
                        let count = rows[0].get::<i32, _>("total").unwrap_or(0);
                        count
                    } else {
                        0
                    }
                }
                Err(e) => {
                    error!("[订单分页] 解析计数查询结果失败: {}", e);
                    0
                }
            },
            Err(e) => {
                error!("[订单分页] 执行计数查询失败: {}", e);
                0
            }
        };

        // 如果总记录数为0，直接返回空结果
        if total == 0 {
            return track()
                .victory()
                .message("成功返回订单分页列表")
                .data(serde_json::json!({
                    "list": Vec::<serde_json::Value>::new(),
                    "count": 0
                }))
                .build();
        }

        // 调整分页参数，确保不会超出范围
        let adjusted_page = if offset >= total {
            // 如果请求的页码超出范围，则返回第一页
            1
        } else {
            page
        };

        let adjusted_offset = (adjusted_page - 1) * per_page;

        // 构建 SQL 查询 - 移除了子查询部分
        let sql = format!(
            r#"
            WITH OrderData AS (
                SELECT 
                    po.id,
                    po.dataID,
                    po.poNumber,
                    po.productId,
                    po.plantId,
                    po.lineId,
                    po.batchNo,
                    CONVERT(varchar(19), po.makeDate, 120) as makeDate,
                    CONVERT(varchar(19), po.validDate, 120) as validDate,
                    po.planAmount,
                    po.actualAmount,
                    po.packageRules,
                    po.estate,
                    CONVERT(varchar(19), po.createTime, 120) as createTime,
                    CONVERT(varchar(19), po.updateTime, 120) as updateTime,
                    po.method,
                    po.generateFlag,
                    po.printMarket,
                    po.printFrom,
                    po.printLogo,
                    po.volumeRatio,
                    po.isMonthly,
                    po.ndc,
                    po.recipeNo,
                    bp.productName,
                    bl.lineName,
                    bpl.plantName,
                    ROW_NUMBER() OVER (ORDER BY po.id DESC) AS RowNum
                FROM produce_order po WITH (NOLOCK)
                LEFT JOIN basic_product bp WITH (NOLOCK) ON po.productId = bp.id
                LEFT JOIN basic_line bl WITH (NOLOCK) ON po.lineId = bl.id
                LEFT JOIN basic_plant bpl WITH (NOLOCK) ON po.plantId = bpl.id
                {where_clause}
            )
            SELECT * FROM OrderData
            WHERE RowNum BETWEEN @P{param_index} AND @P{next_param_index}
            "#,
            where_clause = where_clause,
            param_index = param_index,
            next_param_index = param_index + 1
        );

        // 添加分页参数
        query_params.push(Box::new(adjusted_offset + 1)); // SQL Server的ROW_NUMBER从1开始
        query_params.push(Box::new(adjusted_offset + limit));

        let params_ref: Vec<&dyn ToSql> = query_params.iter().map(|p| p.as_ref()).collect();

        // 执行分页查询
        let mut orders = Vec::new();

        // 执行查询并处理结果
        let result = match conn.query(&sql, &params_ref).await {
            Ok(mut stream) => {
                // 处理查询结果流
                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        // 使用Order的renovation方法，自动处理字段
                        let mut order = Order::renovation(&row);

                        // 添加关联表字段
                        if let Some(product_name) = row.get::<&str, _>("productName") {
                            order.set_dynamic("productName", product_name).ok();
                        }

                        if let Some(line_name) = row.get::<&str, _>("lineName") {
                            order.set_dynamic("lineName", line_name).ok();
                        }

                        if let Some(plant_name) = row.get::<&str, _>("plantName") {
                            order.set_dynamic("plantName", plant_name).ok();
                        }

                        // 处理订单表中的packageRules字段（如果存在）
                        if let Some(package_rules) = &order.package_rules {
                            if let Ok(rules_json) =
                                serde_json::from_str::<serde_json::Value>(package_rules)
                            {
                                // 直接替换原始的package_rules字段，而不是添加新字段
                                order.package_rules = None; // 清除原始字符串
                                order.set_dynamic("packageRules", rules_json).ok();
                            }
                        }

                        orders.push(order);
                    }
                }
                info!("[订单分页] 查询成功，获取到{}条记录", orders.len());

                if adjusted_page != page {
                    info!(
                        "[订单分页] 请求的页码({})超出范围，已调整为第{}页",
                        page, adjusted_page
                    );
                }

                Ok(())
            }
            Err(e) => {
                error!("[订单分页] 查询失败: {}", e);
                Err(e.to_string())
            }
        };

        // 批量加载订单请求数据
        if result.is_ok() && !orders.is_empty() {
            // 收集所有订单ID
            let order_ids: Vec<i32> = orders.iter().map(|o| o.id).collect();

            // 构建批量查询SQL
            let request_sql = format!(
                r#"
                SELECT 
                    orderId, 
                    packageRules, 
                    amount
                FROM produce_order_request WITH (NOLOCK)
                WHERE orderId IN ({})
                "#,
                order_ids
                    .iter()
                    .map(|id| id.to_string())
                    .collect::<Vec<String>>()
                    .join(",")
            );

            // 执行批量查询
            match conn.query(&request_sql, &[]).await {
                Ok(mut stream) => {
                    // 创建订单ID到请求列表的映射
                    let mut order_requests: HashMap<i32, Vec<serde_json::Value>> = HashMap::new();

                    // 处理查询结果
                    while let Ok(Some(item)) = stream.try_next().await {
                        if let tiberius::QueryItem::Row(row) = item {
                            if let Some(order_id) = row.get::<i32, _>("orderId") {
                                let mut request = serde_json::Map::new();

                                // 处理amount字段
                                if let Some(amount) = row.get::<i32, _>("amount") {
                                    request.insert(
                                        "amount".to_string(),
                                        serde_json::Value::Number(serde_json::Number::from(amount)),
                                    );
                                }

                                // 处理packageRules字段
                                if let Some(rules_str) = row.get::<&str, _>("packageRules") {
                                    // 直接解析JSON字符串
                                    if let Ok(rules_json) =
                                        serde_json::from_str::<serde_json::Value>(rules_str)
                                    {
                                        request.insert("rules".to_string(), rules_json);
                                    } else {
                                        // 如果解析失败，保存原始字符串
                                        request.insert(
                                            "packageRules".to_string(),
                                            serde_json::Value::String(rules_str.to_string()),
                                        );
                                    }
                                }

                                // 将请求添加到对应订单的列表中
                                order_requests
                                    .entry(order_id)
                                    .or_insert_with(Vec::new)
                                    .push(serde_json::Value::Object(request));
                            }
                        }
                    }

                    // 将请求数据关联到对应的订单
                    for order in &mut orders {
                        if let Some(requests) = order_requests.get(&order.id) {
                            order.set_dynamic("distribution", requests.clone()).ok();
                        } else {
                            // 如果没有关联数据，设置为空数组
                            order
                                .set_dynamic("distribution", Vec::<serde_json::Value>::new())
                                .ok();
                        }
                    }

                    info!("[订单分页] 成功加载关联的订单请求数据");
                }
                Err(e) => {
                    error!("[订单分页] 加载订单请求数据失败: {}", e);
                }
            }
        }

        // 根据查询结果返回响应
        match result {
            Ok(_) => {
                // 使用to_response方法，直接返回数组
                let orders_json: Vec<serde_json::Value> = orders
                    .iter()
                    .map(|o| {
                        // 创建一个包含所有字段的响应
                        let mut response = o.to_response();

                        // 确保关联表字段也被包含在响应中
                        if let Some(product_name) = o.get_dynamic::<String>("productName") {
                            response["productName"] = serde_json::Value::String(product_name);
                        }

                        if let Some(line_name) = o.get_dynamic::<String>("lineName") {
                            response["lineName"] = serde_json::Value::String(line_name);
                        }

                        if let Some(plant_name) = o.get_dynamic::<String>("plantName") {
                            response["plantName"] = serde_json::Value::String(plant_name);
                        }

                        // 确保packageRules字段是JSON对象而不是字符串
                        if let Some(package_rules) =
                            o.get_dynamic::<serde_json::Value>("packageRules")
                        {
                            response["packageRules"] = package_rules;
                        } else if response.get("packageRules").is_some() {
                            // 如果响应中有packageRules但是是字符串，尝试解析
                            if let Some(serde_json::Value::String(rules_str)) =
                                response.get("packageRules")
                            {
                                if let Ok(rules_json) =
                                    serde_json::from_str::<serde_json::Value>(rules_str)
                                {
                                    response["packageRules"] = rules_json;
                                }
                            }
                        }

                        // 处理distribution字段
                        if let Some(distribution) =
                            o.get_dynamic::<Vec<serde_json::Value>>("distribution")
                        {
                            response["distribution"] = serde_json::Value::Array(distribution);
                        } else {
                            response["distribution"] = serde_json::Value::Array(Vec::new());
                        }

                        response
                    })
                    .collect();

                track()
                    .victory()
                    .message("成功返回订单分页列表")
                    .data(serde_json::json!({
                        "list": orders_json,
                        "count": total
                    }))
                    .build()
            }
            Err(e) => track().defeat().message(e).build(),
        }
    }
}
