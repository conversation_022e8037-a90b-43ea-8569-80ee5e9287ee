use crate::app::models::basic::product::Product;
use crate::app::providers::DatabaseServiceProvider;
use crate::app::traits::response::track;
use axum::extract::{Query, State};
use axum::response::IntoResponse;
use futures_util::TryStreamExt;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tiberius::ToSql;
use tracing::{error, info};

/// 查询参数
#[derive(Debug, Deserialize)]
pub struct QueryParams {
    /// 页码
    #[serde(default = "default_page")]
    pub page: i32,
    /// 每页数量
    #[serde(rename = "limit", default = "default_limit")]
    pub per_page: i32,
    /// 产品名称
    #[serde(rename = "productName")]
    pub product_name: Option<String>,
    /// 产品编码
    #[serde(rename = "productCode")]
    pub product_code: Option<String>,
    /// NDC编码
    pub ndc: Option<String>,
}

/// 默认页码
fn default_page() -> i32 {
    1
}

/// 默认每页数量
fn default_limit() -> i32 {
    20
}

/// 查询结果
#[derive(Debug, Serialize)]
pub struct QueryResult {
    /// 数据列表
    pub list: Vec<Product>,
    /// 总记录数
    pub count: i32,
}

/// 列表结果
#[derive(Debug, Serialize)]
pub struct ListResult {
    /// 数据列表
    pub list: Vec<Product>,
}

/// 产品控制器
pub struct ProductController;

impl ProductController {
    /// 查询产品列表（不分页）
    pub async fn index(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Query(params): Query<QueryParams>,
    ) -> impl IntoResponse {
        info!("[产品列表] 开始获取产品列表");
        info!(
            "[产品列表] 查询参数: product_name={:?}, product_code={:?}, ndc={:?}",
            params.product_name, params.product_code, params.ndc
        );

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[产品列表] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[产品列表] 获取数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 构建查询条件
        let mut where_conditions = Vec::new();
        let mut query_params: Vec<Box<dyn ToSql>> = Vec::new();

        // 添加查询条件
        if let Some(product_name) = &params.product_name {
            if !product_name.trim().is_empty() {
                where_conditions.push("p.productName LIKE @P1".to_string());
                query_params.push(Box::new(format!("%{}%", product_name.trim())));
            }
        }

        if let Some(product_code) = &params.product_code {
            if !product_code.trim().is_empty() {
                where_conditions.push("p.productCode LIKE @P2".to_string());
                query_params.push(Box::new(format!("%{}%", product_code.trim())));
            }
        }

        if let Some(ndc) = &params.ndc {
            if !ndc.trim().is_empty() {
                where_conditions.push("p.ndc LIKE @P3".to_string());
                query_params.push(Box::new(format!("%{}%", ndc.trim())));
            }
        }

        // 组合 WHERE 子句
        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 构建 SQL 查询
        let sql = format!(
            r#"
            SELECT 
                p.id,
                p.companyId,
                p.plantId,
                p.productName,
                p.productCode,
                p.ndc,
                p.material,
                p.dosageForm,
                p.dosageUsage,
                p.strength,
                p.packageSize,
                p.generic,
                p.composition,
                p.unitPrice,
                p.description,
                p.scheduled,
                p.imageDir,
                p.receiverCode,
                p.remark,
                p.primaryCount,
                p.productType,
                p.country,
                p.packageRules,
                p.estate,
                p.packageRatio,
                p.shelfLife,
                p.productCodeType,
                p.productCodeValue,
                p.eancode,
                p.sendStatus,
                p.dateFormat,
                p.codeStyle,
                p.printFrom,
                p.printLogo,
                p.printMarket,
                p.packageSpec,
                p.recipeNo,
                p.manufacturer,
                p.distributorBy,
                p.manufacturerBy,
                p.distributor,
                CONVERT(varchar(19), p.createTime, 120) as createTime,
                CONVERT(varchar(19), p.updateTime, 120) as updateTime,
                bp.plantName,
                bc.companyPrefix
            FROM basic_product p WITH (NOLOCK)
            LEFT JOIN basic_plant bp WITH (NOLOCK) ON p.plantId = bp.id
            LEFT JOIN basic_company bc WITH (NOLOCK) ON p.companyId = bc.id
            {where_clause}
            ORDER BY p.id DESC
            "#,
            where_clause = where_clause
        );

        // 构建参数引用
        let params_ref: Vec<&dyn ToSql> = query_params.iter().map(|p| p.as_ref()).collect();

        // 执行查询
        let mut products = Vec::new();

        // 执行查询并处理结果
        let result = match conn.query(&sql, &params_ref).await {
            Ok(mut stream) => {
                // 处理查询结果流
                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        // 使用Product的renovation方法，自动处理字段
                        let mut product = Product::renovation(&row);

                        // 添加关联表字段
                        if let Some(plant_name) = row.get::<&str, _>("plantName") {
                            product.set_dynamic("plantName", plant_name).ok();
                        }

                        if let Some(company_prefix) = row.get::<&str, _>("companyPrefix") {
                            product.set_dynamic("companyPrefix", company_prefix).ok();
                        }

                        products.push(product);
                    }
                }
                info!("[产品列表] 查询成功，获取到{}条记录", products.len());
                Ok(())
            }
            Err(e) => {
                error!("[产品列表] 查询失败: {}", e);
                Err(e.to_string())
            }
        };

        // 根据查询结果返回响应
        match result {
            Ok(_) => {
                // 使用to_response方法，直接返回数组
                let products_json: Vec<serde_json::Value> = products
                    .iter()
                    .map(|p| {
                        // 创建一个包含所有字段的响应
                        let mut response = p.to_response();

                        // 确保关联表字段也被包含在响应中
                        if let Some(plant_name) = p.get_dynamic::<String>("plantName") {
                            response["plantName"] = serde_json::Value::String(plant_name);
                        }

                        if let Some(company_prefix) = p.get_dynamic::<String>("companyPrefix") {
                            response["companyPrefix"] = serde_json::Value::String(company_prefix);
                        }

                        response
                    })
                    .collect();

                track().victory().data(products_json).build()
            }
            Err(e) => track().defeat().message(e).build(),
        }
    }

    /// 查询产品列表（分页）
    pub async fn page(
        State(db): State<Arc<DatabaseServiceProvider>>,
        Query(params): Query<QueryParams>,
    ) -> impl IntoResponse {
        info!("[产品分页] 开始获取产品分页列表");
        info!(
            "[产品分页] 查询参数: page={}, per_page={}, product_name={:?}, product_code={:?}, ndc={:?}",
            params.page, params.per_page, params.product_name, params.product_code, params.ndc
        );

        // 验证分页参数
        let page = if params.page <= 0 {
            info!("[产品分页] 页码参数不合法({}), 使用默认值1", params.page);
            1
        } else {
            params.page
        };

        // 限制每页数量，防止请求过大导致性能问题
        let max_per_page = 100; // 设置最大每页数量为100
        let per_page = if params.per_page <= 0 {
            info!(
                "[产品分页] 每页数量参数不合法({}), 使用默认值20",
                params.per_page
            );
            20
        } else if params.per_page > max_per_page {
            info!(
                "[产品分页] 每页数量过大({}), 已限制为最大值{}",
                params.per_page, max_per_page
            );
            max_per_page
        } else {
            params.per_page
        };

        // 获取数据库连接
        let mut conn = match db.get_connection().await {
            Ok(conn) => {
                info!("[产品分页] 数据库连接获取成功");
                conn
            }
            Err(e) => {
                error!("[产品分页] 获取数据库连接获取失败: {}", e);
                return track().defeat().message(e.to_string()).build();
            }
        };

        // 构建查询条件
        let mut where_conditions = Vec::new();
        let mut query_params: Vec<Box<dyn ToSql>> = Vec::new();
        let mut param_index = 1;

        // 添加查询条件
        if let Some(product_name) = &params.product_name {
            if !product_name.trim().is_empty() {
                where_conditions.push(format!("p.productName LIKE @P{}", param_index));
                query_params.push(Box::new(format!("%{}%", product_name.trim())));
                param_index += 1;
            }
        }

        if let Some(product_code) = &params.product_code {
            if !product_code.trim().is_empty() {
                where_conditions.push(format!("p.productCode LIKE @P{}", param_index));
                query_params.push(Box::new(format!("%{}%", product_code.trim())));
                param_index += 1;
            }
        }

        if let Some(ndc) = &params.ndc {
            if !ndc.trim().is_empty() {
                where_conditions.push(format!("p.ndc LIKE @P{}", param_index));
                query_params.push(Box::new(format!("%{}%", ndc.trim())));
                param_index += 1;
            }
        }

        // 组合 WHERE 子句
        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 计算分页参数
        let offset = (page - 1) * per_page;
        let limit = per_page;

        // 构建计数 SQL - 获取满足条件的记录总数
        let count_sql = format!(
            r#"
            SELECT COUNT(1) as total
            FROM basic_product p WITH (NOLOCK)
            {where_clause}
            "#,
            where_clause = where_clause
        );

        // 构建参数引用 - 为计数查询创建一个独立的参数引用
        let count_params_ref: Vec<&dyn ToSql> = query_params.iter().map(|p| p.as_ref()).collect();

        // 执行计数查询
        let total = match conn.query(&count_sql, &count_params_ref).await {
            Ok(stream) => match stream.into_first_result().await {
                Ok(rows) => {
                    if !rows.is_empty() {
                        let count = rows[0].get::<i32, _>("total").unwrap_or(0);
                        count
                    } else {
                        0
                    }
                }
                Err(e) => {
                    error!("[产品分页] 解析计数查询结果失败: {}", e);
                    0
                }
            },
            Err(e) => {
                error!("[产品分页] 执行计数查询失败: {}", e);
                0
            }
        };

        // 如果总记录数为0，直接返回空结果
        if total == 0 {
            return track()
                .victory()
                .message("成功返回产品分页列表")
                .data(serde_json::json!({
                    "list": Vec::<serde_json::Value>::new(),
                    "count": 0
                }))
                .build();
        }

        // 调整分页参数，确保不会超出范围
        let adjusted_page = if offset >= total {
            // 如果请求的页码超出范围，则返回第一页
            1
        } else {
            page
        };

        let adjusted_offset = (adjusted_page - 1) * per_page;

        // 构建 SQL 查询
        let sql = format!(
            r#"
            WITH ProductData AS (
                SELECT 
                    p.id,
                    p.companyId,
                    p.plantId,
                    p.productName,
                    p.productCode,
                    p.ndc,
                    p.material,
                    p.dosageForm,
                    p.dosageUsage,
                    p.strength,
                    p.packageSize,
                    p.generic,
                    p.composition,
                    p.unitPrice,
                    p.description,
                    p.scheduled,
                    p.imageDir,
                    p.receiverCode,
                    p.remark,
                    p.primaryCount,
                    p.productType,
                    p.country,
                    p.packageRules,
                    p.estate,
                    p.packageRatio,
                    p.shelfLife,
                    p.productCodeType,
                    p.productCodeValue,
                    p.eancode,
                    p.sendStatus,
                    p.dateFormat,
                    p.codeStyle,
                    p.printFrom,
                    p.printLogo,
                    p.printMarket,
                    p.packageSpec,
                    p.recipeNo,
                    p.manufacturer,
                    p.distributorBy,
                    p.manufacturerBy,
                    p.distributor,
                    CONVERT(varchar(19), p.createTime, 120) as createTime,
                    CONVERT(varchar(19), p.updateTime, 120) as updateTime,
                    bp.plantName,
                    bc.companyPrefix,
                    ROW_NUMBER() OVER (ORDER BY p.id DESC) AS RowNum
                FROM basic_product p WITH (NOLOCK)
                LEFT JOIN basic_plant bp WITH (NOLOCK) ON p.plantId = bp.id
                LEFT JOIN basic_company bc WITH (NOLOCK) ON p.companyId = bc.id
                {where_clause}
            )
            SELECT * FROM ProductData
            WHERE RowNum BETWEEN @P{param_index} AND @P{next_param_index}
            "#,
            where_clause = where_clause,
            param_index = param_index,
            next_param_index = param_index + 1
        );

        // 添加分页参数
        query_params.push(Box::new(adjusted_offset + 1)); // SQL Server的ROW_NUMBER从1开始
        query_params.push(Box::new(adjusted_offset + limit));

        let params_ref: Vec<&dyn ToSql> = query_params.iter().map(|p| p.as_ref()).collect();

        // 执行分页查询
        let mut products = Vec::new();

        // 执行查询并处理结果
        let result = match conn.query(&sql, &params_ref).await {
            Ok(mut stream) => {
                // 处理查询结果流
                while let Ok(Some(item)) = stream.try_next().await {
                    if let tiberius::QueryItem::Row(row) = item {
                        // 使用Product的renovation方法，自动处理字段
                        let mut product = Product::renovation(&row);

                        // 添加关联表字段
                        if let Some(plant_name) = row.get::<&str, _>("plantName") {
                            product.set_dynamic("plantName", plant_name).ok();
                        }

                        if let Some(company_prefix) = row.get::<&str, _>("companyPrefix") {
                            product.set_dynamic("companyPrefix", company_prefix).ok();
                        }

                        products.push(product);
                    }
                }
                info!("[产品分页] 查询成功，获取到{}条记录", products.len());

                if adjusted_page != page {
                    info!(
                        "[产品分页] 请求的页码({})超出范围，已调整为第{}页",
                        page, adjusted_page
                    );
                }

                Ok(())
            }
            Err(e) => {
                error!("[产品分页] 查询失败: {}", e);
                Err(e.to_string())
            }
        };

        // 根据查询结果返回响应
        match result {
            Ok(_) => {
                // 使用to_response方法，直接返回数组
                let products_json: Vec<serde_json::Value> = products
                    .iter()
                    .map(|p| {
                        // 创建一个包含所有字段的响应
                        let mut response = p.to_response();

                        // 确保关联表字段也被包含在响应中
                        if let Some(plant_name) = p.get_dynamic::<String>("plantName") {
                            response["plantName"] = serde_json::Value::String(plant_name);
                        }

                        if let Some(company_prefix) = p.get_dynamic::<String>("companyPrefix") {
                            response["companyPrefix"] = serde_json::Value::String(company_prefix);
                        }

                        response
                    })
                    .collect();

                track()
                    .victory()
                    .message("成功返回产品分页列表")
                    .data(serde_json::json!({
                        "list": products_json,
                        "count": total
                    }))
                    .build()
            }
            Err(e) => track().defeat().message(e).build(),
        }
    }
}
