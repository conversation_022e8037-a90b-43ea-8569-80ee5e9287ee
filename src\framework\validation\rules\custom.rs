use crate::framework::validation::traits::ValidationRule;

/// 自定义验证规则
pub struct CustomRule<F>
where
    F: Fn(&str) -> bool,
{
    name_str: &'static str,
    validator: F,
    message_str: String,
}

impl<F> CustomRule<F>
where
    F: Fn(&str) -> bool,
{
    /// 创建新的自定义验证规则
    pub fn new(name: &'static str, validator: F, message: &str) -> Self {
        Self {
            name_str: name,
            validator,
            message_str: message.to_string(),
        }
    }
}

impl<F> ValidationRule for CustomRule<F>
where
    F: Fn(&str) -> bool,
{
    fn name(&self) -> &'static str {
        self.name_str
    }

    fn validate(&self, value: &str, _params: &[&str]) -> bool {
        (self.validator)(value)
    }

    fn message(&self) -> &str {
        &self.message_str
    }
}

/// 创建自定义验证规则的便捷函数
pub fn rule<F>(name: &'static str, validator: F, message: &str) -> CustomRule<F>
where
    F: Fn(&str) -> bool,
{
    CustomRule::new(name, validator, message)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_custom_rule() {
        // 创建一个验证偶数的规则
        let even_rule = rule(
            "even",
            |value| value.parse::<i32>().map(|n| n % 2 == 0).unwrap_or(false),
            "必须是偶数",
        );

        assert!(even_rule.validate("2", &[]));
        assert!(even_rule.validate("0", &[]));
        assert!(!even_rule.validate("1", &[]));
        assert!(!even_rule.validate("abc", &[]));
        assert_eq!(even_rule.message(), "必须是偶数");
    }
}
