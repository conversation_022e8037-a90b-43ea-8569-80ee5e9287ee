@echo off
chcp 65001 > nul
title Rustravel Dev Server

:: 启用快速编辑模式（允许鼠标选择复制）
cmd /k "reg add HKEY_CURRENT_USER\Console /v QuickEdit /t REG_DWORD /d 1 /f >nul 2>&1 && exit"

:: 设置控制台缓冲区
mode con buffer=1000

:: 显示欢迎信息
cls
echo.
echo  ===================================================
echo  =                                                 =
echo  =                Rustravel                        =
echo  =            Development Server                   =
echo  =                                                 =
echo  ===================================================
echo.
echo  [INFO] Starting development server...
echo  [INFO] Server will be available at: http://localhost:3000
echo  [INFO] API documentation: http://localhost:3000/api/docs
echo.
echo  [TIP] Press Ctrl+C to stop the server
echo  [TIP] Server logs will be displayed below
echo  [TIP] File changes will trigger auto-recompile
echo  [TIP] Press Ctrl+Break to force stop
echo  [TIP] You can select text with mouse to copy
echo  ===================================================
echo.

:: 检查 .env 配置文件
if not exist .env (
    echo [ERROR] .env file not found!
    echo [TIP] Please create .env file with required configuration
    echo [TIP] You can copy .env.example as a template
    echo.
    if exist .env.example (
        echo [INFO] Found .env.example, copying to .env...
        copy .env.example .env
        if errorlevel 1 (
            echo [ERROR] Failed to copy .env.example to .env
            echo [TIP] Please check file permissions
            echo [TIP] Try running as administrator
            echo [TIP] Or create .env manually with required settings:
            echo [TIP] - DATABASE_URL
            echo [TIP] - SERVER_PORT
            echo [TIP] - JWT_SECRET
            echo.
            echo Press any key to exit...
            pause > nul
            exit /b 1
        )
        echo [SUCCESS] Created .env from .env.example
        echo [INFO] Please check and update the configuration if needed
    ) else (
        echo [ERROR] .env.example not found!
        echo [TIP] Please create .env file manually with these settings:
        echo [TIP] DATABASE_URL=your_database_url
        echo [TIP] SERVER_PORT=3000
        echo [TIP] JWT_SECRET=your_jwt_secret
        echo.
        echo Press any key to exit...
        pause > nul
        exit /b 1
    )
)

:: 检查必要的目录
if not exist "src/storage/logs" (
    echo [INFO] Creating logs directory...
    mkdir "src\storage\logs"
    if errorlevel 1 (
        echo [ERROR] Failed to create logs directory
        echo [TIP] Please check permissions or create manually
    ) else (
        echo [SUCCESS] Created logs directory
    )
)

:: 检查并清理旧的日志文件
if exist "src\storage\logs\*.log" (
    echo [INFO] Cleaning old log files...
    del /Q "src\storage\logs\*.log"
    if errorlevel 1 (
        echo [WARN] Failed to clean old log files
    ) else (
        echo [SUCCESS] Cleaned old log files
    )
)

:: 检查 Rust 是否安装
rustc --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Rust is not installed!
    echo [TIP] Please install Rust from https://rustup.rs/
    echo [TIP] After installation, restart your terminal
    echo [TIP] If you already installed Rust, try:
    echo [TIP] 1. Restart your terminal
    echo [TIP] 2. Run: rustup update
    echo [TIP] 3. Check PATH environment variable
    echo.
    echo Press any key to exit...
    pause > nul
    exit /b 1
)

:: 检查 Cargo 是否可用
cargo --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Cargo is not available!
    echo [TIP] Please make sure Rust is properly installed
    echo [TIP] Try running: rustup update
    echo.
    echo Press any key to exit...
    pause > nul
    exit /b 1
)

:check_watch
echo [INFO] Checking cargo-watch...
cargo watch --version >nul 2>&1
if errorlevel 1 (
    echo [WARN] cargo-watch not found, installing...
    echo [INFO] This may take a few minutes, please wait...
    echo [TIP] If installation fails, you can manually run: cargo install cargo-watch
    cargo install cargo-watch
    if errorlevel 1 (
        echo [ERROR] Failed to install cargo-watch!
        echo [TIP] Please check your network connection
        echo [TIP] Or try running: cargo install cargo-watch
        echo [TIP] If problem persists, try:
        echo [TIP] 1. Check your internet connection
        echo [TIP] 2. Run as administrator
        echo [TIP] 3. Clear cargo cache: cargo clean
        echo.
        echo Press any key to exit...
        pause > nul
        exit /b 1
    )
    echo [SUCCESS] cargo-watch installed successfully!
)

:: 检查项目依赖
echo [INFO] Checking project dependencies...
cargo check >nul 2>&1
if errorlevel 1 (
    echo [WARN] Project dependencies check failed
    echo [INFO] Attempting to update dependencies...
    cargo update
    if errorlevel 1 (
        echo [ERROR] Failed to update dependencies!
        echo [TIP] Please check your network connection
        echo [TIP] Or try running: cargo update manually
    )
)

:start
cls
echo.
echo  ===================================================
echo  =                                                 =
echo  =                Rustravel                        =
echo  =            Development Server                   =
echo  =                                                 =
echo  ===================================================
echo.
echo  [INFO] Starting development server...
echo  [INFO] Server will be available at: http://localhost:3000
echo  [INFO] API documentation: http://localhost:3000/api/docs
echo.
echo  [TIP] Press Ctrl+C to stop the server
echo  [TIP] Server logs will be displayed below
echo  [TIP] File changes will trigger auto-recompile
echo  [TIP] Press Ctrl+Break to force stop
echo  [TIP] You can select text with mouse to copy
echo  ===================================================
echo.

:: 检查项目是否存在
if not exist Cargo.toml (
    echo [ERROR] Cargo.toml not found!
    echo [TIP] Please make sure you are in the correct directory
    echo [TIP] Current directory: %CD%
    echo [TIP] Expected structure:
    echo [TIP] - Cargo.toml
    echo [TIP] - src/
    echo [TIP] - .env
    echo.
    echo Press any key to exit...
    pause > nul
    exit /b 1
)

:: 运行 cargo watch
echo [INFO] Starting cargo watch...
echo [INFO] Watching for changes in: src/, Cargo.toml, templates/
echo [INFO] Ignoring: target/, .git/, logs/, .cursor/
echo.
cargo watch -d 2 -x run --quiet --no-vcs-ignores --why --shell cmd -w src -w Cargo.toml -w templates --ignore "target/*" --ignore ".git/*" --ignore "src/storage/logs/*" --ignore ".cursor/*" --ignore "*.log"

if errorlevel 1 (
    echo.
    echo [WARN] Error occurred! Auto-restarting...
    echo [TIP] If the problem persists, please check the error message above
    echo [TIP] You can try these solutions:
    echo [TIP] 1. Run: cargo clean && cargo build
    echo [TIP] 2. Check for syntax errors in your code
    echo [TIP] 3. Verify all dependencies are installed
    echo [TIP] 4. Check .env configuration
    timeout /t 2 /nobreak > nul
    goto start
)

echo.
echo [INFO] Server stopped. Auto-restarting...
echo [TIP] Press Ctrl+C to exit completely
echo [TIP] Press Ctrl+Break to force stop
timeout /t 2 /nobreak > nul
goto start 