use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};

/// 自定义日期时间类型
/// 用于统一日期时间格式的序列化和反序列化
#[derive(Debug, Deserialize, Clone)]
#[serde(transparent)]
pub struct DateTime(pub NaiveDateTime);

impl Serialize for DateTime {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        let s = self.format("%Y-%m-%d %H:%M:%S");
        serializer.serialize_str(&s)
    }
}

impl DateTime {
    /// 创建一个新的DateTime实例
    pub fn new(dt: NaiveDateTime) -> Self {
        Self(dt)
    }

    /// 将日期时间格式化为字符串
    ///
    /// # Arguments
    ///
    /// * `fmt` - 日期时间格式字符串
    ///
    /// # Examples
    ///
    /// ```
    /// let dt = DateTime::new(chrono::Local::now().naive_local());
    ///
    /// // 完整的日期和时间 (YYYY-MM-DD HH:MM:SS)
    /// let full_datetime = dt.format("%Y-%m-%d %H:%M:%S");
    ///
    /// // 只显示日期部分 (YYYY-MM-DD)
    /// let date_only = dt.format("%Y-%m-%d");
    ///
    /// // 自定义格式
    /// let custom = dt.format("%d/%m/%Y %H:%M");
    /// ```
    pub fn format(&self, fmt: &str) -> String {
        self.0.format(fmt).to_string()
    }
}

/// 定义可以用于解析的格式特性
pub trait DateFormat {
    /// 尝试解析日期时间字符串
    fn try_parse(&self, s: &str) -> Option<DateTime>;
}

/// 为单一格式字符串实现解析特性
impl DateFormat for &str {
    fn try_parse(&self, s: &str) -> Option<DateTime> {
        NaiveDateTime::parse_from_str(s, self).ok().map(DateTime)
    }
}

/// 为格式字符串数组实现解析特性
impl<'a> DateFormat for &'a [&'a str] {
    fn try_parse(&self, s: &str) -> Option<DateTime> {
        for fmt in *self {
            if let Ok(dt) = NaiveDateTime::parse_from_str(s, fmt) {
                return Some(DateTime(dt));
            }
        }
        None
    }
}

impl DateTime {
    /// 从字符串解析日期时间
    ///
    /// # Arguments
    ///
    /// * `s` - 日期时间字符串
    /// * `fmt` - 日期时间格式字符串或格式字符串数组
    ///
    /// # Examples
    ///
    /// ```
    /// // 使用单一格式
    /// let dt1 = DateTime::parse("2023-01-01 12:30:45", "%Y-%m-%d %H:%M:%S");
    ///
    /// // 尝试多种格式
    /// let formats = &["%Y-%m-%d %H:%M:%S", "%Y/%m/%d %H:%M:%S", "%d-%m-%Y %H:%M:%S"];
    /// let dt2 = DateTime::parse("2023-01-01 12:30:45", formats);
    /// ```
    pub fn parse<F: DateFormat>(s: &str, fmt: F) -> Option<Self> {
        fmt.try_parse(s)
    }
}

impl Default for DateTime {
    fn default() -> Self {
        Self(NaiveDateTime::default())
    }
}
