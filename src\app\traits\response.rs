use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde::{Deserialize, Serialize};

/// # API响应模块
///
/// 提供统一的API响应格式和构建工具。
/// 使用 `track()` 函数创建所有API响应。
///
/// ## 基本使用示例
/// ```rust
/// // 成功响应
/// track().victory().message("创建成功").data(serde_json::json!(user)).build();
///
/// // 失败响应
/// track().defeat().message("用户不存在").status(400).build();
/// ```

/// # 通用响应结构体
///
/// 用于统一API响应格式，包含状态码、消息和可选数据。
///
/// ## 响应格式
/// ```json
/// {
///   "code": 0,        // 状态码：0表示成功，非0表示失败
///   "message": "操作成功", // 响应消息
///   "data": { ... }   // 可选的响应数据
/// }
/// ```
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T = ()> {
    /// 状态码：0表示成功，非0表示失败
    pub code: i32,
    /// 响应消息
    pub message: String,
    /// 可选的响应数据
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
}

/// # ApiResponse构建器
///
/// 通过 `track()` 函数获取构建器实例。
///
/// ## 构建步骤
/// 1. 创建构建器：`track()`
/// 2. 设置状态：`.victory()` 或 `.defeat()`
/// 3. 设置属性：`.message()`, `.data()`, `.status()`
/// 4. 构建响应：`.build()`
pub struct ApiResponseBuilder<T> {
    code: i32,
    message: Option<String>,
    data: Option<T>,
}

// 为 ApiResponseBuilder 添加私有构造函数
impl<T> ApiResponseBuilder<T> {
    // 构造函数，仅供 track() 函数使用
    pub(crate) fn new() -> Self {
        Self {
            code: 0,
            message: None,
            data: None,
        }
    }

    /// 设置为成功响应（状态码为0）
    pub fn victory(mut self) -> Self {
        self.code = 0;
        self
    }

    /// 设置为失败响应（状态码为1）
    pub fn defeat(mut self) -> Self {
        self.code = 1;
        self
    }

    /// 设置响应消息
    pub fn message(mut self, message: impl Into<String>) -> Self {
        self.message = Some(message.into());
        self
    }

    /// 设置状态码
    ///
    /// 常见状态码：
    /// - 0: 成功
    /// - 1: 一般性错误
    /// - 400-499: 客户端错误
    /// - 500-599: 服务端错误
    pub fn status(mut self, status: i32) -> Self {
        self.code = status;
        self
    }

    /// 构建ApiResponse实例
    pub fn build(self) -> ApiResponse<T> {
        ApiResponse {
            code: self.code,
            message: self.message.unwrap_or_else(|| {
                if self.code == 0 {
                    "操作成功".to_string()
                } else {
                    "操作失败".to_string()
                }
            }),
            data: self.data,
        }
    }
}

// 定义一个特征，用于将不同类型转换为 serde_json::Value
pub trait IntoJsonValue {
    fn into_json_value(self) -> serde_json::Value;
}

// 为 serde_json::Value 实现 IntoJsonValue
impl IntoJsonValue for serde_json::Value {
    fn into_json_value(self) -> serde_json::Value {
        self
    }
}

// 为 Vec<serde_json::Value> 实现 IntoJsonValue
impl IntoJsonValue for Vec<serde_json::Value> {
    fn into_json_value(self) -> serde_json::Value {
        serde_json::Value::Array(self)
    }
}

// 为 ApiResponseBuilder<serde_json::Value> 添加统一的 data 方法
impl ApiResponseBuilder<serde_json::Value> {
    /// 设置响应数据
    ///
    /// 自动处理不同类型：
    /// - serde_json::Value 直接使用
    /// - Vec<serde_json::Value> 自动转换为 serde_json::Value::Array
    pub fn data<D: IntoJsonValue>(mut self, data: D) -> Self {
        self.data = Some(data.into_json_value());
        self
    }
}

/// 创建API响应的唯一方法
///
/// ## 示例
/// ```rust
/// track().victory().message("操作成功").build();
/// ```
pub fn track() -> ApiResponseBuilder<serde_json::Value> {
    ApiResponseBuilder::new()
}

/// 实现 IntoResponse 特性，使 ApiResponse 可直接作为控制器返回值
impl<T: Serialize> IntoResponse for ApiResponse<T> {
    fn into_response(self) -> Response {
        (StatusCode::OK, Json(self)).into_response()
    }
}
