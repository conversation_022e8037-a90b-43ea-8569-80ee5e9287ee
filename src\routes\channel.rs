use crate::app::providers::DatabaseServiceProvider;
use axum::{routing::get, Router};
use std::sync::Arc;

/// Channel 路由
pub fn routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new()
        .route("/", get(|| async { "Channel Home" }))
        .nest("/ws", ws_routes())
    // 在这里添加 Channel 路由
}

/// WebSocket 路由分组
fn ws_routes() -> Router<Arc<DatabaseServiceProvider>> {
    Router::new()
        .route("/chat", get(|| async { "WebSocket Chat" }))
        .route("/notify", get(|| async { "WebSocket Notify" }))
}
