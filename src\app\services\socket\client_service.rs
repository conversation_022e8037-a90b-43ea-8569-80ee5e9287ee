use crate::config::SocketConfig;
use socket2::{Socket, TcpKeepalive};
use std::{io, sync::Arc, time::Duration};
use tokio::{
    io::{AsyncReadExt, AsyncWriteExt},
    net::TcpStream,
    sync::Mutex,
    time,
};
use tracing::{debug, error, info, instrument};

/// Socket连接模式
#[derive(Debug, Clone)]
pub enum SocketConnectionMode {
    /// 单连接模式 - 使用单个长连接
    Single,
    /// 连接池模式 - 使用连接池管理多个连接
    Pool,
}

/// Socket客户端配置
#[derive(Debug, Clone)]
pub struct SocketClientConfig {
    /// 服务器地址
    pub host: String,
    /// 服务器端口
    pub port: u16,
    /// 连接超时时间(毫秒)
    pub timeout_ms: u64,
    /// 是否保持长连接
    pub keep_alive: bool,
    /// 保持连接的时间间隔(秒)，仅当keep_alive为true时有效
    pub keep_alive_interval: u64,
    /// 读取超时时间(毫秒)
    pub read_timeout_ms: Option<u64>,
    /// 读取缓冲区大小
    pub read_buffer_size: usize,
    /// 写入缓冲区大小
    pub write_buffer_size: usize,
    /// 连接模式
    pub connection_mode: SocketConnectionMode,
}

impl Default for SocketClientConfig {
    fn default() -> Self {
        // 从环境配置中加载默认值
        let socket_config = SocketConfig::from_env();
        Self {
            host: socket_config.host,
            port: socket_config.port,
            timeout_ms: socket_config.timeout * 1000, // 秒转毫秒
            keep_alive: true,
            keep_alive_interval: socket_config.heartbeat_interval,
            read_timeout_ms: Some(socket_config.timeout * 1000), // 秒转毫秒
            read_buffer_size: socket_config.read_buffer_size,
            write_buffer_size: socket_config.write_buffer_size,
            connection_mode: SocketConnectionMode::Single, // 默认使用单连接模式
        }
    }
}

/// Socket客户端错误类型
#[derive(Debug, thiserror::Error)]
pub enum SocketClientError {
    #[error("连接错误: {0}")]
    ConnectionError(String),

    #[error("连接超时")]
    ConnectionTimeout,

    #[error("未连接到服务器")]
    NotConnected,

    #[error("发送数据失败: {0}")]
    SendError(String),

    #[error("接收数据失败: {0}")]
    ReceiveError(String),

    #[error("IO错误: {0}")]
    IoError(#[from] io::Error),
}

/// Socket客户端服务
#[derive(Debug)]
pub struct SocketClientService {
    /// 客户端配置
    config: SocketClientConfig,
    /// 连接实例(使用Mutex保证线程安全) - 仅在单连接模式下使用
    connection: Option<Arc<Mutex<TcpStream>>>,
}

impl SocketClientService {
    /// 创建新的Socket客户端实例
    pub fn new(config: SocketClientConfig) -> Self {
        Self {
            config,
            connection: None,
        }
    }

    /// 使用默认配置创建Socket客户端实例
    pub fn default() -> Self {
        Self::new(SocketClientConfig::default())
    }

    /// 从环境配置创建Socket客户端实例
    pub fn from_env() -> Self {
        Self::default()
    }

    /// 创建使用连接池的Socket客户端实例
    pub fn with_pool() -> Self {
        let mut config = SocketClientConfig::default();
        config.connection_mode = SocketConnectionMode::Pool;
        Self::new(config)
    }

    /// 从环境配置创建使用连接池的Socket客户端实例
    pub fn with_pool_from_env() -> Self {
        let mut config = SocketClientConfig::default();
        config.connection_mode = SocketConnectionMode::Pool;
        Self::new(config)
    }

    /// 连接到服务器 - 仅在单连接模式下有效
    #[instrument(skip(self), fields(host = %self.config.host, port = %self.config.port))]
    pub async fn connect(&mut self) -> Result<(), SocketClientError> {
        match self.config.connection_mode {
            SocketConnectionMode::Single => {
                let addr = format!("{}:{}", self.config.host, self.config.port);
                info!("开始连接到服务器");

                // 设置连接超时
                let timeout = Duration::from_millis(self.config.timeout_ms);

                match time::timeout(timeout, TcpStream::connect(&addr)).await {
                    Ok(Ok(stream)) => {
                        debug!("连接成功");
                        // 应用配置
                        self.configure_stream(stream).await
                    }
                    Ok(Err(e)) => {
                        error!("连接失败: {}", e);
                        Err(SocketClientError::ConnectionError(e.to_string()))
                    }
                    Err(_) => {
                        error!("连接超时");
                        Err(SocketClientError::ConnectionTimeout)
                    }
                }
            }
            SocketConnectionMode::Pool => {
                info!("连接池模式下无需手动连接，连接由连接池自动管理");
                Ok(())
            }
        }
    }

    /// 配置TCP流
    #[instrument(skip(self, stream))]
    async fn configure_stream(&mut self, stream: TcpStream) -> Result<(), SocketClientError> {
        // 如果需要设置保持连接
        if self.config.keep_alive {
            let std_stream = stream.into_std()?;

            // 将标准库的TcpStream转换为socket2的Socket
            let socket = Socket::from(std_stream);

            // 创建TCP保活配置
            let keepalive =
                TcpKeepalive::new().with_time(Duration::from_secs(self.config.keep_alive_interval));

            // 设置TCP保活选项
            if let Err(e) = socket.set_tcp_keepalive(&keepalive) {
                error!("设置keep-alive失败: {}", e);
            }

            // 将socket2的Socket转回tokio的TcpStream
            let tokio_stream = TcpStream::from_std(socket.into())?;

            self.connection = Some(Arc::new(Mutex::new(tokio_stream)));
        } else {
            // 不需要keepalive，直接使用原始stream
            self.connection = Some(Arc::new(Mutex::new(stream)));
        }

        Ok(())
    }

    /// 发送数据并接收响应
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    pub async fn send_and_receive(&self, data: &[u8]) -> Result<Vec<u8>, SocketClientError> {
        match self.config.connection_mode {
            SocketConnectionMode::Single => self.send_and_receive_single(data).await,
            SocketConnectionMode::Pool => self.send_and_receive_pool(data).await,
        }
    }

    /// 使用单连接模式发送数据并接收响应
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    async fn send_and_receive_single(&self, data: &[u8]) -> Result<Vec<u8>, SocketClientError> {
        let conn = self
            .connection
            .as_ref()
            .ok_or(SocketClientError::NotConnected)?;
        let mut stream = conn.lock().await;

        // 发送数据
        stream.write_all(data).await.map_err(|e| {
            error!("发送数据失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        // 确保数据发送完成
        stream.flush().await?;

        // 接收响应
        self.read_response(&mut stream).await
    }

    /// 使用连接池模式发送数据并接收响应
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    async fn send_and_receive_pool(&self, data: &[u8]) -> Result<Vec<u8>, SocketClientError> {
        use crate::app::facades::Socket as SocketFacade;

        let config = self.config.clone();
        let data = data.to_vec(); // 克隆数据以避免生命周期问题

        // 获取连接池
        let pool = SocketFacade::get_pool().await;
        let mut conn = pool.get().await.map_err(|e| {
            error!("获取连接失败: {:?}", e);
            SocketClientError::ConnectionError(format!("获取连接失败: {:?}", e))
        })?;

        // 发送数据
        conn.write_all(&data).await.map_err(|e| {
            error!("发送数据失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        // 确保数据发送完成
        conn.flush().await.map_err(|e| {
            error!("刷新发送缓冲区失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        // 接收响应
        Self::read_response_static(&mut conn, &config).await
    }

    /// 从流中读取响应
    #[instrument(skip(self, stream))]
    async fn read_response(&self, stream: &mut TcpStream) -> Result<Vec<u8>, SocketClientError> {
        // 预分配合理大小的缓冲区，避免频繁扩容
        let mut buffer = Vec::with_capacity(self.config.read_buffer_size);
        let mut temp_buffer = vec![0u8; self.config.read_buffer_size];

        // 如果设置了读取超时
        if let Some(timeout_ms) = self.config.read_timeout_ms {
            let timeout = Duration::from_millis(timeout_ms);
            return self
                .read_with_timeout(stream, &mut buffer, &mut temp_buffer, timeout)
                .await;
        }

        // 无超时读取
        loop {
            match stream.read(&mut temp_buffer).await {
                Ok(0) => break, // 连接关闭
                Ok(n) => {
                    buffer.extend_from_slice(&temp_buffer[..n]);
                    // 检查是否接收完成（根据协议确定）
                    if n < temp_buffer.len() {
                        break;
                    }
                }
                Err(e) => {
                    error!("接收数据失败: {}", e);
                    return Err(SocketClientError::from(e));
                }
            }
        }

        Ok(buffer)
    }

    /// 静态方法：从连接池获取的流中读取响应
    #[instrument(skip(stream, config))]
    async fn read_response_static(
        stream: &mut TcpStream,
        config: &SocketClientConfig,
    ) -> Result<Vec<u8>, SocketClientError> {
        // 预分配合理大小的缓冲区，避免频繁扩容
        let mut buffer = Vec::with_capacity(config.read_buffer_size);
        let mut temp_buffer = vec![0u8; config.read_buffer_size];

        // 如果设置了读取超时
        if let Some(timeout_ms) = config.read_timeout_ms {
            let timeout = Duration::from_millis(timeout_ms);
            return Self::read_with_timeout_static(stream, &mut buffer, &mut temp_buffer, timeout)
                .await;
        }

        // 无超时读取
        loop {
            match stream.read(&mut temp_buffer).await {
                Ok(0) => break, // 连接关闭
                Ok(n) => {
                    buffer.extend_from_slice(&temp_buffer[..n]);
                    // 检查是否接收完成（根据协议确定）
                    if n < temp_buffer.len() {
                        break;
                    }
                }
                Err(e) => {
                    error!("接收数据失败: {}", e);
                    return Err(SocketClientError::from(e));
                }
            }
        }

        Ok(buffer)
    }

    /// 带超时的读取操作
    #[instrument(skip(self, stream, buffer, temp_buffer))]
    async fn read_with_timeout(
        &self,
        stream: &mut TcpStream,
        buffer: &mut Vec<u8>,
        temp_buffer: &mut [u8],
        timeout: Duration,
    ) -> Result<Vec<u8>, SocketClientError> {
        loop {
            match time::timeout(timeout, stream.read(temp_buffer)).await {
                Ok(Ok(0)) => break, // 连接关闭
                Ok(Ok(n)) => {
                    buffer.extend_from_slice(&temp_buffer[..n]);
                    // 检查是否接收完成
                    if n < temp_buffer.len() {
                        break;
                    }
                }
                Ok(Err(e)) => {
                    error!("接收数据失败: {}", e);
                    return Err(SocketClientError::from(e));
                }
                Err(_) => {
                    error!("读取超时");
                    return Err(SocketClientError::ReceiveError("读取超时".to_string()));
                }
            }
        }

        // 避免不必要的克隆，直接返回buffer
        Ok(std::mem::take(buffer))
    }

    /// 静态方法：带超时的读取操作
    #[instrument(skip(stream, buffer, temp_buffer))]
    async fn read_with_timeout_static(
        stream: &mut TcpStream,
        buffer: &mut Vec<u8>,
        temp_buffer: &mut [u8],
        timeout: Duration,
    ) -> Result<Vec<u8>, SocketClientError> {
        loop {
            match time::timeout(timeout, stream.read(temp_buffer)).await {
                Ok(Ok(0)) => break, // 连接关闭
                Ok(Ok(n)) => {
                    buffer.extend_from_slice(&temp_buffer[..n]);
                    // 检查是否接收完成
                    if n < temp_buffer.len() {
                        break;
                    }
                }
                Ok(Err(e)) => {
                    error!("接收数据失败: {}", e);
                    return Err(SocketClientError::from(e));
                }
                Err(_) => {
                    error!("读取超时");
                    return Err(SocketClientError::ReceiveError("读取超时".to_string()));
                }
            }
        }

        // 避免不必要的克隆，直接返回buffer
        Ok(std::mem::take(buffer))
    }

    /// 只发送数据
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    pub async fn send(&self, data: &[u8]) -> Result<(), SocketClientError> {
        match self.config.connection_mode {
            SocketConnectionMode::Single => self.send_single(data).await,
            SocketConnectionMode::Pool => self.send_pool(data).await,
        }
    }

    /// 使用单连接模式发送数据
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    async fn send_single(&self, data: &[u8]) -> Result<(), SocketClientError> {
        let conn = self
            .connection
            .as_ref()
            .ok_or(SocketClientError::NotConnected)?;
        let mut stream = conn.lock().await;

        // 发送数据
        stream.write_all(data).await?;

        // 确保数据发送完成
        stream.flush().await?;

        Ok(())
    }

    /// 使用连接池模式发送数据
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    async fn send_pool(&self, data: &[u8]) -> Result<(), SocketClientError> {
        use crate::app::facades::Socket as SocketFacade;

        let data = data.to_vec(); // 克隆数据以避免生命周期问题

        // 获取连接池
        let pool = SocketFacade::get_pool().await;
        let mut conn = pool.get().await.map_err(|e| {
            error!("获取连接失败: {:?}", e);
            SocketClientError::ConnectionError(format!("获取连接失败: {:?}", e))
        })?;

        // 发送数据
        conn.write_all(&data).await.map_err(|e| {
            error!("发送数据失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        // 确保数据发送完成
        conn.flush().await.map_err(|e| {
            error!("刷新发送缓冲区失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        Ok(())
    }

    /// 关闭连接
    #[instrument(skip(self))]
    pub async fn close(&mut self) {
        if self.connection.is_none() {
            return;
        }

        info!("关闭连接");

        // 尝试优雅关闭连接
        if let Some(conn) = self.connection.take() {
            if let Ok(mut stream) = conn.try_lock() {
                // 忽略关闭错误，因为我们无论如何都要丢弃连接
                let _ = stream.shutdown().await;
            }
        }
    }

    /// 检查是否已连接
    pub fn is_connected(&self) -> bool {
        match self.config.connection_mode {
            SocketConnectionMode::Single => self.connection.is_some(),
            SocketConnectionMode::Pool => {
                // 连接池模式下，假设连接池总是可用的
                // 实际的连接状态由连接池管理
                true
            }
        }
    }

    /// 重新连接
    pub async fn reconnect(&mut self) -> Result<(), SocketClientError> {
        self.close().await;
        self.connect().await
    }

    /// 使用指定的重试次数和间隔进行重连
    #[instrument(skip(self), fields(max_attempts = %max_attempts, interval_ms = %interval_ms))]
    pub async fn reconnect_with_retry(
        &mut self,
        max_attempts: u32,
        interval_ms: u64,
    ) -> Result<(), SocketClientError> {
        let mut attempts = 0;
        let interval = Duration::from_millis(interval_ms);

        while attempts < max_attempts {
            attempts += 1;
            info!("尝试重连 ({}/{})", attempts, max_attempts);

            match self.connect().await {
                Ok(_) => return Ok(()),
                Err(e) => {
                    if attempts < max_attempts {
                        error!("重连失败: {:?}，将在 {}ms 后重试", e, interval_ms);
                        time::sleep(interval).await;
                    } else {
                        error!("达到最大重试次数，重连失败");
                        return Err(e);
                    }
                }
            }
        }

        // 这里理论上不会执行到，因为循环中已经处理了所有情况
        Err(SocketClientError::ConnectionError(
            "达到最大重试次数".to_string(),
        ))
    }

    /// 使用环境配置中的重试参数进行重连
    #[instrument(skip(self))]
    pub async fn reconnect_with_env_retry(&mut self) -> Result<(), SocketClientError> {
        let socket_config = SocketConfig::from_env();
        self.reconnect_with_retry(
            socket_config.max_reconnect_attempts,
            socket_config.reconnect_interval * 1000, // 秒转毫秒
        )
        .await
    }
}

/// 使用示例
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_socket_client() {
        // 从环境配置创建客户端
        let mut client = SocketClientService::from_env();

        // 或者手动创建配置
        // let config = SocketClientConfig {
        //     host: "127.0.0.1".to_string(),
        //     port: 8080,
        //     timeout_ms: 5000,
        //     keep_alive: true,
        //     keep_alive_interval: 60,
        //     read_timeout_ms: Some(10000),
        // };

        // 如果使用手动配置，则创建客户端
        // let mut client = SocketClientService::new(config);

        // 连接到服务器（测试时请确保服务器已启动）
        if let Err(e) = client.connect().await {
            println!("连接失败: {:?}", e);
            return;
        }

        // 发送数据并接收响应
        let data = b"Hello, Server!";
        match client.send_and_receive(data).await {
            Ok(response) => {
                println!("收到响应: {:?}", String::from_utf8_lossy(&response));
            }
            Err(e) => {
                println!("发送/接收失败: {:?}", e);
            }
        }

        // 关闭连接
        client.close().await;
    }

    #[tokio::test]
    async fn test_socket_client_with_pool() {
        // 使用连接池模式创建客户端
        let client = SocketClientService::with_pool_from_env();

        // 连接池模式下无需手动连接，直接发送数据
        let data = b"Hello, Server from Pool!";
        match client.send_and_receive(data).await {
            Ok(response) => {
                println!("收到响应: {:?}", String::from_utf8_lossy(&response));
            }
            Err(e) => {
                println!("发送/接收失败: {:?}", e);
            }
        }

        // 连接池模式下无需手动关闭连接
        println!("连接池模式测试完成");
    }

    #[tokio::test]
    async fn test_socket_client_comparison() {
        println!("=== 单连接模式 vs 连接池模式对比测试 ===");

        // 单连接模式
        println!("测试单连接模式...");
        let mut single_client = SocketClientService::from_env();
        if single_client.connect().await.is_ok() {
            let data = b"Single connection test";
            if let Ok(response) = single_client.send_and_receive(data).await {
                println!("单连接模式响应: {:?}", String::from_utf8_lossy(&response));
            }
            single_client.close().await;
        }

        // 连接池模式
        println!("测试连接池模式...");
        let pool_client = SocketClientService::with_pool_from_env();
        let data = b"Pool connection test";
        if let Ok(response) = pool_client.send_and_receive(data).await {
            println!("连接池模式响应: {:?}", String::from_utf8_lossy(&response));
        }

        println!("对比测试完成");
    }
}
