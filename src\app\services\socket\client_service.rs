use crate::config::SocketConfig;
use std::{io, time::Duration};
use tokio::{
    io::{AsyncReadExt, AsyncWriteExt},
    net::TcpStream,
    time,
};
use tracing::{error, instrument};

/// 可发送数据的trait，支持单个和批量数据
pub trait SendData {
    type Output;
    type ResponseOutput;

    /// 获取数据长度（用于日志）
    fn len(&self) -> usize;

    /// 只发送数据，不接收响应
    async fn send_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::Output, SocketClientError>;

    /// 发送数据并接收响应
    async fn send_and_receive_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::ResponseOutput, SocketClientError>;
}

/// Socket客户端配置
#[derive(Debug, Clone)]
pub struct SocketClientConfig {
    /// 服务器地址
    pub host: String,
    /// 服务器端口
    pub port: u16,
    /// 连接超时时间(毫秒)
    pub connect_timeout_ms: u64,
    /// 读取超时时间(毫秒)
    pub read_timeout_ms: Option<u64>,
    /// 写入超时时间(毫秒)
    pub write_timeout_ms: Option<u64>,
    /// 总体操作超时时间(毫秒)
    pub operation_timeout_ms: Option<u64>,
    /// 读取缓冲区大小
    pub read_buffer_size: usize,
}

impl Default for SocketClientConfig {
    fn default() -> Self {
        let socket_config = SocketConfig::from_env();
        Self {
            host: socket_config.host,
            port: socket_config.port,
            connect_timeout_ms: 5000, // 5秒连接超时
            read_timeout_ms: Some(socket_config.timeout * 1000), // 读取超时
            write_timeout_ms: Some(5000), // 5秒写入超时
            operation_timeout_ms: Some(socket_config.timeout * 1000 + 5000), // 总操作超时
            read_buffer_size: socket_config.read_buffer_size,
        }
    }
}

/// Socket客户端错误类型
#[derive(Debug, thiserror::Error)]
pub enum SocketClientError {
    #[error("连接池错误: {0}")]
    PoolError(String),

    #[error("发送失败: {0}")]
    SendError(String),

    #[error("接收失败: {0}")]
    ReceiveError(String),

    #[error("IO错误: {0}")]
    IoError(#[from] io::Error),
}

/// Socket客户端服务 - 统一使用连接池
#[derive(Debug, Clone)]
pub struct SocketClientService {
    /// 客户端配置
    config: SocketClientConfig,
}

impl SocketClientService {
    /// 创建新的Socket客户端实例
    pub fn new(config: SocketClientConfig) -> Self {
        Self { config }
    }

    /// 从环境配置创建Socket客户端实例
    pub fn from_env() -> Self {
        Self::new(SocketClientConfig::default())
    }

    /// 创建自定义配置的客户端实例
    pub fn with_config(host: &str, port: u16) -> Self {
        let config = SocketClientConfig {
            host: host.to_string(),
            port,
            connect_timeout_ms: 5000,
            read_timeout_ms: Some(5000),
            write_timeout_ms: Some(5000),
            operation_timeout_ms: Some(15000), // 15秒总超时
            read_buffer_size: 8192,
        };
        Self::new(config)
    }

    /// 测试连接是否可用
    #[instrument(skip(self))]
    pub async fn ping(&self) -> Result<(), SocketClientError> {
        // 发送一个简单的ping消息
        let _response = self.send_and_receive_internal(b"PING").await?;
        Ok(())
    }

    /// 获取客户端配置
    pub fn config(&self) -> &SocketClientConfig {
        &self.config
    }

    /// 设置连接超时
    pub fn with_connect_timeout(mut self, timeout_ms: u64) -> Self {
        self.config.connect_timeout_ms = timeout_ms;
        self
    }

    /// 设置读取超时
    pub fn with_read_timeout(mut self, timeout_ms: Option<u64>) -> Self {
        self.config.read_timeout_ms = timeout_ms;
        self
    }

    /// 设置写入超时
    pub fn with_write_timeout(mut self, timeout_ms: Option<u64>) -> Self {
        self.config.write_timeout_ms = timeout_ms;
        self
    }

    /// 设置总操作超时
    pub fn with_operation_timeout(mut self, timeout_ms: Option<u64>) -> Self {
        self.config.operation_timeout_ms = timeout_ms;
        self
    }

    /// 设置所有超时（便利方法）
    pub fn with_timeouts(
        mut self,
        connect_ms: u64,
        read_ms: u64,
        write_ms: u64,
        operation_ms: u64,
    ) -> Self {
        self.config.connect_timeout_ms = connect_ms;
        self.config.read_timeout_ms = Some(read_ms);
        self.config.write_timeout_ms = Some(write_ms);
        self.config.operation_timeout_ms = Some(operation_ms);
        self
    }

    /// 发送数据（支持单个或批量）
    #[instrument(skip(self, data), fields(count = data.len()))]
    pub async fn send<T>(&self, data: T) -> Result<T::Output, SocketClientError>
    where
        T: SendData,
    {
        data.send_with_client(self).await
    }

    /// 发送数据并接收响应（支持单个或批量）
    #[instrument(skip(self, data), fields(count = data.len()))]
    pub async fn send_and_receive<T>(&self, data: T) -> Result<T::ResponseOutput, SocketClientError>
    where
        T: SendData,
    {
        data.send_and_receive_with_client(self).await
    }

    /// 内部方法：发送数据并接收响应（带超时）
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    async fn send_and_receive_internal(&self, data: &[u8]) -> Result<Vec<u8>, SocketClientError> {
        // 如果设置了总操作超时，使用它包装整个操作
        if let Some(operation_timeout_ms) = self.config.operation_timeout_ms {
            let timeout = Duration::from_millis(operation_timeout_ms);
            return match time::timeout(timeout, self.send_and_receive_without_timeout(data)).await {
                Ok(result) => result,
                Err(_) => {
                    error!("操作超时: {}ms", operation_timeout_ms);
                    Err(SocketClientError::ReceiveError(format!(
                        "操作超时: {}ms",
                        operation_timeout_ms
                    )))
                }
            };
        }

        // 没有总超时，直接执行
        self.send_and_receive_without_timeout(data).await
    }

    /// 内部方法：发送数据并接收响应（不带总超时）
    async fn send_and_receive_without_timeout(
        &self,
        data: &[u8],
    ) -> Result<Vec<u8>, SocketClientError> {
        use crate::app::facades::Socket as SocketFacade;

        let pool = SocketFacade::get_pool().await;
        let mut conn = pool.get().await.map_err(|e| {
            error!("获取连接失败: {:?}", e);
            SocketClientError::PoolError(e.to_string())
        })?;

        // 发送数据（带写入超时）
        if let Some(write_timeout_ms) = self.config.write_timeout_ms {
            let timeout = Duration::from_millis(write_timeout_ms);

            // 写入数据
            match time::timeout(timeout, conn.write_all(data)).await {
                Ok(Ok(_)) => {}
                Ok(Err(e)) => {
                    error!("发送数据失败: {}", e);
                    return Err(SocketClientError::SendError(e.to_string()));
                }
                Err(_) => {
                    error!("写入超时: {}ms", write_timeout_ms);
                    return Err(SocketClientError::SendError(format!(
                        "写入超时: {}ms",
                        write_timeout_ms
                    )));
                }
            }

            // 刷新缓冲区
            match time::timeout(timeout, conn.flush()).await {
                Ok(Ok(_)) => {}
                Ok(Err(e)) => {
                    error!("刷新缓冲区失败: {}", e);
                    return Err(SocketClientError::SendError(e.to_string()));
                }
                Err(_) => {
                    error!("刷新超时: {}ms", write_timeout_ms);
                    return Err(SocketClientError::SendError(format!(
                        "刷新超时: {}ms",
                        write_timeout_ms
                    )));
                }
            }
        } else {
            // 无写入超时
            conn.write_all(data).await.map_err(|e| {
                error!("发送数据失败: {}", e);
                SocketClientError::SendError(e.to_string())
            })?;

            conn.flush().await.map_err(|e| {
                error!("刷新缓冲区失败: {}", e);
                SocketClientError::SendError(e.to_string())
            })?;
        }

        // 接收响应（带读取超时）
        self.read_response(&mut conn).await
    }

    /// 从TCP流中读取响应
    #[instrument(skip(self, stream))]
    async fn read_response(&self, stream: &mut TcpStream) -> Result<Vec<u8>, SocketClientError> {
        let mut buffer = Vec::with_capacity(self.config.read_buffer_size);
        let mut temp_buffer = vec![0u8; self.config.read_buffer_size];

        // 带超时读取
        if let Some(timeout_ms) = self.config.read_timeout_ms {
            let timeout = Duration::from_millis(timeout_ms);
            loop {
                match time::timeout(timeout, stream.read(&mut temp_buffer)).await {
                    Ok(Ok(0)) => break, // 连接关闭
                    Ok(Ok(n)) => {
                        buffer.extend_from_slice(&temp_buffer[..n]);
                        if n < temp_buffer.len() {
                            break;
                        }
                    }
                    Ok(Err(e)) => return Err(SocketClientError::IoError(e)),
                    Err(_) => return Err(SocketClientError::ReceiveError("读取超时".to_string())),
                }
            }
        } else {
            // 无超时读取
            loop {
                match stream.read(&mut temp_buffer).await {
                    Ok(0) => break,
                    Ok(n) => {
                        buffer.extend_from_slice(&temp_buffer[..n]);
                        if n < temp_buffer.len() {
                            break;
                        }
                    }
                    Err(e) => return Err(SocketClientError::IoError(e)),
                }
            }
        }

        Ok(buffer)
    }

    /// 内部方法：只发送数据（带超时）
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    async fn send_only_internal(&self, data: &[u8]) -> Result<(), SocketClientError> {
        use crate::app::facades::Socket as SocketFacade;

        let pool = SocketFacade::get_pool().await;
        let mut conn = pool.get().await.map_err(|e| {
            error!("获取连接失败: {:?}", e);
            SocketClientError::PoolError(e.to_string())
        })?;

        // 发送数据（带写入超时）
        if let Some(write_timeout_ms) = self.config.write_timeout_ms {
            let timeout = Duration::from_millis(write_timeout_ms);

            // 写入数据
            match time::timeout(timeout, conn.write_all(data)).await {
                Ok(Ok(_)) => {}
                Ok(Err(e)) => {
                    error!("发送数据失败: {}", e);
                    return Err(SocketClientError::SendError(e.to_string()));
                }
                Err(_) => {
                    error!("写入超时: {}ms", write_timeout_ms);
                    return Err(SocketClientError::SendError(format!(
                        "写入超时: {}ms",
                        write_timeout_ms
                    )));
                }
            }

            // 刷新缓冲区
            match time::timeout(timeout, conn.flush()).await {
                Ok(Ok(_)) => {}
                Ok(Err(e)) => {
                    error!("刷新缓冲区失败: {}", e);
                    return Err(SocketClientError::SendError(e.to_string()));
                }
                Err(_) => {
                    error!("刷新超时: {}ms", write_timeout_ms);
                    return Err(SocketClientError::SendError(format!(
                        "刷新超时: {}ms",
                        write_timeout_ms
                    )));
                }
            }
        } else {
            // 无写入超时
            conn.write_all(data).await.map_err(|e| {
                error!("发送数据失败: {}", e);
                SocketClientError::SendError(e.to_string())
            })?;

            conn.flush().await.map_err(|e| {
                error!("刷新缓冲区失败: {}", e);
                SocketClientError::SendError(e.to_string())
            })?;
        }

        Ok(())
    }
}

/// 使用示例
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_socket_client_unified_api() {
        let client = SocketClientService::from_env();

        // 1. 发送单个字节数据
        let response: Vec<u8> = client.send_and_receive(b"Hello").await.unwrap();
        println!("字节响应: {:?}", String::from_utf8_lossy(&response));

        // 2. 发送字符串
        let response: Vec<u8> = client.send_and_receive("Hello World").await.unwrap();
        println!("字符串响应: {:?}", String::from_utf8_lossy(&response));

        // 3. 发送批量字节数据
        let batch = vec![b"Msg1".as_slice(), b"Msg2".as_slice(), b"Msg3".as_slice()];
        let responses: Vec<Vec<u8>> = client.send_and_receive(batch).await.unwrap();
        println!("批量字节响应: {} 个", responses.len());

        // 4. 发送批量字符串
        let string_batch = vec!["Hello".to_string(), "World".to_string()];
        let responses: Vec<Vec<u8>> = client.send_and_receive(string_batch).await.unwrap();
        println!("批量字符串响应: {} 个", responses.len());

        // 5. 只发送，不接收响应
        client.send(b"Notification").await.unwrap();
        client.send("String notification").await.unwrap();

        // 6. 批量只发送
        let notifications = vec![b"Notify1".as_slice(), b"Notify2".as_slice()];
        client.send(notifications).await.unwrap();

        // 7. 测试连接
        if client.ping().await.is_ok() {
            println!("连接测试成功");
        }
    }

    #[tokio::test]
    async fn test_socket_client_timeouts() {
        // 测试超时配置
        let client = SocketClientService::from_env()
            .with_connect_timeout(3000) // 3秒连接超时
            .with_read_timeout(Some(5000)) // 5秒读取超时
            .with_write_timeout(Some(2000)) // 2秒写入超时
            .with_operation_timeout(Some(10000)); // 10秒总操作超时

        println!("客户端配置: {:?}", client.config());

        // 测试快速操作（应该成功）
        match client.send_and_receive(b"PING").await {
            Ok(response) => println!("快速操作成功: {:?}", String::from_utf8_lossy(&response)),
            Err(e) => println!("快速操作失败: {:?}", e),
        }

        // 测试批量操作的超时处理
        let batch = vec![b"Msg1".as_slice(), b"Msg2".as_slice()];
        match client.send_and_receive(batch).await {
            Ok(responses) => println!("批量操作成功: {} 个响应", responses.len()),
            Err(e) => println!("批量操作失败: {:?}", e),
        }
    }

    #[tokio::test]
    async fn test_socket_client_timeout_configurations() {
        // 测试不同的超时配置方式

        // 1. 使用便利方法设置所有超时
        let client1 = SocketClientService::from_env().with_timeouts(3000, 5000, 2000, 12000);
        println!("配置1: {:?}", client1.config());

        // 2. 链式配置
        let client2 = SocketClientService::from_env()
            .with_connect_timeout(2000)
            .with_read_timeout(Some(8000))
            .with_write_timeout(None) // 禁用写入超时
            .with_operation_timeout(Some(15000));
        println!("配置2: {:?}", client2.config());

        // 3. 自定义配置
        let config = SocketClientConfig {
            host: "127.0.0.1".to_string(),
            port: 8080,
            connect_timeout_ms: 1000,         // 1秒连接超时
            read_timeout_ms: Some(3000),      // 3秒读取超时
            write_timeout_ms: Some(1000),     // 1秒写入超时
            operation_timeout_ms: Some(8000), // 8秒总操作超时
            read_buffer_size: 4096,
        };
        let client3 = SocketClientService::new(config);
        println!("配置3: {:?}", client3.config());
    }
}

// 为 &[u8] 实现 SendData trait（单个数据）
impl SendData for &[u8] {
    type Output = ();
    type ResponseOutput = Vec<u8>;

    fn len(&self) -> usize {
        1 // 单个数据项
    }

    async fn send_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::Output, SocketClientError> {
        client.send_only_internal(self).await
    }

    async fn send_and_receive_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::ResponseOutput, SocketClientError> {
        client.send_and_receive_internal(self).await
    }
}

// 为 Vec<&[u8]> 实现 SendData trait（批量数据）
impl SendData for Vec<&[u8]> {
    type Output = ();
    type ResponseOutput = Vec<Vec<u8>>;

    fn len(&self) -> usize {
        self.len()
    }

    async fn send_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::Output, SocketClientError> {
        for data in self {
            client.send_only_internal(data).await?;
        }
        Ok(())
    }

    async fn send_and_receive_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::ResponseOutput, SocketClientError> {
        let mut responses = Vec::with_capacity(self.len());
        for data in self {
            let response = client.send_and_receive_internal(data).await?;
            responses.push(response);
        }
        Ok(responses)
    }
}

// 为 &[&[u8]] 实现 SendData trait（批量数据切片）
impl SendData for &[&[u8]] {
    type Output = ();
    type ResponseOutput = Vec<Vec<u8>>;

    fn len(&self) -> usize {
        (*self).len()
    }

    async fn send_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::Output, SocketClientError> {
        for data in *self {
            client.send_only_internal(data).await?;
        }
        Ok(())
    }

    async fn send_and_receive_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::ResponseOutput, SocketClientError> {
        let mut responses = Vec::with_capacity(self.len());
        for data in *self {
            let response = client.send_and_receive_internal(data).await?;
            responses.push(response);
        }
        Ok(responses)
    }
}

// 为 String 实现 SendData trait
impl SendData for String {
    type Output = ();
    type ResponseOutput = Vec<u8>;

    fn len(&self) -> usize {
        1
    }

    async fn send_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::Output, SocketClientError> {
        client.send_only_internal(self.as_bytes()).await
    }

    async fn send_and_receive_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::ResponseOutput, SocketClientError> {
        client.send_and_receive_internal(self.as_bytes()).await
    }
}

// 为 &str 实现 SendData trait
impl SendData for &str {
    type Output = ();
    type ResponseOutput = Vec<u8>;

    fn len(&self) -> usize {
        1
    }

    async fn send_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::Output, SocketClientError> {
        client.send_only_internal(self.as_bytes()).await
    }

    async fn send_and_receive_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::ResponseOutput, SocketClientError> {
        client.send_and_receive_internal(self.as_bytes()).await
    }
}

// 为 Vec<String> 实现 SendData trait（批量字符串）
impl SendData for Vec<String> {
    type Output = ();
    type ResponseOutput = Vec<Vec<u8>>;

    fn len(&self) -> usize {
        self.len()
    }

    async fn send_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::Output, SocketClientError> {
        for data in self {
            client.send_only_internal(data.as_bytes()).await?;
        }
        Ok(())
    }

    async fn send_and_receive_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::ResponseOutput, SocketClientError> {
        let mut responses = Vec::with_capacity(self.len());
        for data in self {
            let response = client.send_and_receive_internal(data.as_bytes()).await?;
            responses.push(response);
        }
        Ok(responses)
    }
}

// 为 Vec<u8> 实现 SendData trait
impl SendData for Vec<u8> {
    type Output = ();
    type ResponseOutput = Vec<u8>;

    fn len(&self) -> usize {
        1
    }

    async fn send_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::Output, SocketClientError> {
        client.send_only_internal(self.as_slice()).await
    }

    async fn send_and_receive_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::ResponseOutput, SocketClientError> {
        client.send_and_receive_internal(self.as_slice()).await
    }
}

// 为 &Vec<u8> 实现 SendData trait
impl SendData for &Vec<u8> {
    type Output = ();
    type ResponseOutput = Vec<u8>;

    fn len(&self) -> usize {
        1
    }

    async fn send_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::Output, SocketClientError> {
        client.send_only_internal(self.as_slice()).await
    }

    async fn send_and_receive_with_client(
        &self,
        client: &SocketClientService,
    ) -> Result<Self::ResponseOutput, SocketClientError> {
        client.send_and_receive_internal(self.as_slice()).await
    }
}
