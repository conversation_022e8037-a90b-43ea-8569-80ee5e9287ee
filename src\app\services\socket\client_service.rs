use crate::config::SocketConfig;
use std::{io, time::Duration};
use tokio::{
    io::{AsyncReadExt, AsyncWriteExt},
    net::TcpStream,
    time,
};
use tracing::{error, instrument};

/// Socket客户端配置
#[derive(Debug, Clone)]
pub struct SocketClientConfig {
    /// 服务器地址
    pub host: String,
    /// 服务器端口
    pub port: u16,
    /// 读取超时时间(毫秒)
    pub read_timeout_ms: Option<u64>,
    /// 读取缓冲区大小
    pub read_buffer_size: usize,
}

impl Default for SocketClientConfig {
    fn default() -> Self {
        let socket_config = SocketConfig::from_env();
        Self {
            host: socket_config.host,
            port: socket_config.port,
            read_timeout_ms: Some(socket_config.timeout * 1000), // 秒转毫秒
            read_buffer_size: socket_config.read_buffer_size,
        }
    }
}

/// Socket客户端错误类型
#[derive(Debug, thiserror::Error)]
pub enum SocketClientError {
    #[error("连接池错误: {0}")]
    PoolError(String),

    #[error("发送失败: {0}")]
    SendError(String),

    #[error("接收失败: {0}")]
    ReceiveError(String),

    #[error("IO错误: {0}")]
    IoError(#[from] io::Error),
}

/// Socket客户端服务 - 统一使用连接池
#[derive(Debug, Clone)]
pub struct SocketClientService {
    /// 客户端配置
    config: SocketClientConfig,
}

impl SocketClientService {
    /// 创建新的Socket客户端实例
    pub fn new(config: SocketClientConfig) -> Self {
        Self { config }
    }

    /// 从环境配置创建Socket客户端实例
    pub fn from_env() -> Self {
        Self::new(SocketClientConfig::default())
    }

    /// 批量发送数据
    #[instrument(skip(self, data_list), fields(batch_size = data_list.len()))]
    pub async fn send_batch(&self, data_list: &[&[u8]]) -> Result<Vec<Vec<u8>>, SocketClientError> {
        if data_list.is_empty() {
            return Ok(Vec::new());
        }

        let mut responses = Vec::with_capacity(data_list.len());
        for data in data_list {
            let response = self.send_and_receive(data).await?;
            responses.push(response);
        }
        Ok(responses)
    }

    /// 发送数据并接收响应
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    pub async fn send_and_receive(&self, data: &[u8]) -> Result<Vec<u8>, SocketClientError> {
        use crate::app::facades::Socket as SocketFacade;

        let pool = SocketFacade::get_pool().await;
        let mut conn = pool.get().await.map_err(|e| {
            error!("获取连接失败: {:?}", e);
            SocketClientError::PoolError(e.to_string())
        })?;

        // 发送数据
        conn.write_all(data).await.map_err(|e| {
            error!("发送数据失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        conn.flush().await.map_err(|e| {
            error!("刷新缓冲区失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        // 接收响应
        self.read_response(&mut conn).await
    }

    /// 从TCP流中读取响应
    #[instrument(skip(self, stream))]
    async fn read_response(&self, stream: &mut TcpStream) -> Result<Vec<u8>, SocketClientError> {
        let mut buffer = Vec::with_capacity(self.config.read_buffer_size);
        let mut temp_buffer = vec![0u8; self.config.read_buffer_size];

        // 带超时读取
        if let Some(timeout_ms) = self.config.read_timeout_ms {
            let timeout = Duration::from_millis(timeout_ms);
            loop {
                match time::timeout(timeout, stream.read(&mut temp_buffer)).await {
                    Ok(Ok(0)) => break, // 连接关闭
                    Ok(Ok(n)) => {
                        buffer.extend_from_slice(&temp_buffer[..n]);
                        if n < temp_buffer.len() {
                            break;
                        }
                    }
                    Ok(Err(e)) => return Err(SocketClientError::IoError(e)),
                    Err(_) => return Err(SocketClientError::ReceiveError("读取超时".to_string())),
                }
            }
        } else {
            // 无超时读取
            loop {
                match stream.read(&mut temp_buffer).await {
                    Ok(0) => break,
                    Ok(n) => {
                        buffer.extend_from_slice(&temp_buffer[..n]);
                        if n < temp_buffer.len() {
                            break;
                        }
                    }
                    Err(e) => return Err(SocketClientError::IoError(e)),
                }
            }
        }

        Ok(buffer)
    }

    /// 只发送数据
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    pub async fn send(&self, data: &[u8]) -> Result<(), SocketClientError> {
        use crate::app::facades::Socket as SocketFacade;

        let pool = SocketFacade::get_pool().await;
        let mut conn = pool.get().await.map_err(|e| {
            error!("获取连接失败: {:?}", e);
            SocketClientError::PoolError(e.to_string())
        })?;

        conn.write_all(data).await.map_err(|e| {
            error!("发送数据失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        conn.flush().await.map_err(|e| {
            error!("刷新缓冲区失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        Ok(())
    }

    /// 只发送数据的别名方法
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    pub async fn send_only(&self, data: &[u8]) -> Result<(), SocketClientError> {
        self.send(data).await
    }
}

/// 使用示例
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_socket_client() {
        // 从环境配置创建客户端（使用连接池）
        let client = SocketClientService::from_env();

        // 发送数据并接收响应
        let data = b"Hello, Server!";
        match client.send_and_receive(data).await {
            Ok(response) => {
                println!("收到响应: {:?}", String::from_utf8_lossy(&response));
            }
            Err(e) => {
                println!("发送/接收失败: {:?}", e);
            }
        }

        // 连接池模式下无需手动关闭连接
    }

    #[tokio::test]
    async fn test_socket_client_batch() {
        // 测试批量发送功能
        let client = SocketClientService::from_env();

        let batch_data = vec![
            b"Message 1".as_slice(),
            b"Message 2".as_slice(),
            b"Message 3".as_slice(),
        ];

        match client.send_batch(&batch_data).await {
            Ok(responses) => {
                println!("批量发送成功，收到 {} 个响应", responses.len());
                for (i, response) in responses.iter().enumerate() {
                    println!("响应 {}: {:?}", i + 1, String::from_utf8_lossy(response));
                }
            }
            Err(e) => {
                println!("批量发送失败: {:?}", e);
            }
        }

        println!("批量发送测试完成");
    }

    #[tokio::test]
    async fn test_socket_client_send_only() {
        // 测试只发送功能
        let client = SocketClientService::from_env();

        let notification_data = b"Notification message";
        match client.send_only(notification_data).await {
            Ok(_) => {
                println!("通知发送成功");
            }
            Err(e) => {
                println!("通知发送失败: {:?}", e);
            }
        }

        println!("只发送测试完成");
    }
}
