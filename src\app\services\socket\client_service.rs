use crate::config::SocketConfig;
use std::{io, time::Duration};
use tokio::{
    io::{AsyncReadExt, AsyncWriteExt},
    net::TcpStream,
    time,
};
use tracing::{debug, error, instrument};

/// Socket客户端配置
#[derive(Debug, Clone)]
pub struct SocketClientConfig {
    /// 服务器地址
    pub host: String,
    /// 服务器端口
    pub port: u16,
    /// 连接超时时间(毫秒)
    pub timeout_ms: u64,
    /// 是否保持长连接
    pub keep_alive: bool,
    /// 保持连接的时间间隔(秒)，仅当keep_alive为true时有效
    pub keep_alive_interval: u64,
    /// 读取超时时间(毫秒)
    pub read_timeout_ms: Option<u64>,
    /// 读取缓冲区大小
    pub read_buffer_size: usize,
    /// 写入缓冲区大小
    pub write_buffer_size: usize,
}

impl Default for SocketClientConfig {
    fn default() -> Self {
        // 从环境配置中加载默认值
        let socket_config = SocketConfig::from_env();
        Self {
            host: socket_config.host,
            port: socket_config.port,
            timeout_ms: socket_config.timeout * 1000, // 秒转毫秒
            keep_alive: true,
            keep_alive_interval: socket_config.heartbeat_interval,
            read_timeout_ms: Some(socket_config.timeout * 1000), // 秒转毫秒
            read_buffer_size: socket_config.read_buffer_size,
            write_buffer_size: socket_config.write_buffer_size,
        }
    }
}

/// Socket客户端错误类型
#[derive(Debug, thiserror::Error)]
pub enum SocketClientError {
    #[error("连接错误: {0}")]
    ConnectionError(String),

    #[error("连接超时")]
    ConnectionTimeout,

    #[error("未连接到服务器")]
    NotConnected,

    #[error("发送数据失败: {0}")]
    SendError(String),

    #[error("接收数据失败: {0}")]
    ReceiveError(String),

    #[error("IO错误: {0}")]
    IoError(#[from] io::Error),
}

/// Socket客户端服务 - 统一使用连接池
#[derive(Debug, Clone)]
pub struct SocketClientService {
    /// 客户端配置
    config: SocketClientConfig,
}

impl SocketClientService {
    /// 创建新的Socket客户端实例
    pub fn new(config: SocketClientConfig) -> Self {
        Self { config }
    }

    /// 使用默认配置创建Socket客户端实例
    pub fn default() -> Self {
        Self::new(SocketClientConfig::default())
    }

    /// 从环境配置创建Socket客户端实例
    pub fn from_env() -> Self {
        Self::default()
    }

    /// 批量发送数据
    #[instrument(skip(self, data_list), fields(batch_size = data_list.len()))]
    pub async fn send_batch(&self, data_list: &[&[u8]]) -> Result<Vec<Vec<u8>>, SocketClientError> {
        use crate::app::facades::Socket as SocketFacade;

        if data_list.is_empty() {
            return Ok(Vec::new());
        }

        let config = self.config.clone();
        let data_list: Vec<Vec<u8>> = data_list.iter().map(|data| data.to_vec()).collect();

        // 获取连接池
        let pool = SocketFacade::get_pool().await;
        let mut conn = pool.get().await.map_err(|e| {
            error!("获取连接失败: {:?}", e);
            SocketClientError::ConnectionError(format!("获取连接失败: {:?}", e))
        })?;

        let mut responses = Vec::with_capacity(data_list.len());

        for (index, data) in data_list.iter().enumerate() {
            debug!("发送第 {} 个数据包，长度: {} 字节", index + 1, data.len());

            // 发送数据
            conn.write_all(data).await.map_err(|e| {
                error!("发送第 {} 个数据包失败: {}", index + 1, e);
                SocketClientError::SendError(e.to_string())
            })?;

            // 确保数据发送完成
            conn.flush().await.map_err(|e| {
                error!("刷新发送缓冲区失败: {}", e);
                SocketClientError::SendError(e.to_string())
            })?;

            // 接收响应
            let response = Self::read_response_static(&mut conn, &config).await?;
            responses.push(response);

            debug!("第 {} 个数据包处理完成", index + 1);
        }

        Ok(responses)
    }

    /// 发送数据并接收响应
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    pub async fn send_and_receive(&self, data: &[u8]) -> Result<Vec<u8>, SocketClientError> {
        use crate::app::facades::Socket as SocketFacade;

        let config = self.config.clone();
        let data = data.to_vec(); // 克隆数据以避免生命周期问题

        // 获取连接池
        let pool = SocketFacade::get_pool().await;
        let mut conn = pool.get().await.map_err(|e| {
            error!("获取连接失败: {:?}", e);
            SocketClientError::ConnectionError(format!("获取连接失败: {:?}", e))
        })?;

        // 发送数据
        conn.write_all(&data).await.map_err(|e| {
            error!("发送数据失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        // 确保数据发送完成
        conn.flush().await.map_err(|e| {
            error!("刷新发送缓冲区失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        // 接收响应
        Self::read_response_static(&mut conn, &config).await
    }

    /// 静态方法：从连接池获取的流中读取响应
    #[instrument(skip(stream, config))]
    async fn read_response_static(
        stream: &mut TcpStream,
        config: &SocketClientConfig,
    ) -> Result<Vec<u8>, SocketClientError> {
        // 预分配合理大小的缓冲区，避免频繁扩容
        let mut buffer = Vec::with_capacity(config.read_buffer_size);
        let mut temp_buffer = vec![0u8; config.read_buffer_size];

        // 如果设置了读取超时
        if let Some(timeout_ms) = config.read_timeout_ms {
            let timeout = Duration::from_millis(timeout_ms);
            return Self::read_with_timeout_static(stream, &mut buffer, &mut temp_buffer, timeout)
                .await;
        }

        // 无超时读取
        loop {
            match stream.read(&mut temp_buffer).await {
                Ok(0) => break, // 连接关闭
                Ok(n) => {
                    buffer.extend_from_slice(&temp_buffer[..n]);
                    // 检查是否接收完成（根据协议确定）
                    if n < temp_buffer.len() {
                        break;
                    }
                }
                Err(e) => {
                    error!("接收数据失败: {}", e);
                    return Err(SocketClientError::from(e));
                }
            }
        }

        Ok(buffer)
    }

    /// 静态方法：带超时的读取操作
    #[instrument(skip(stream, buffer, temp_buffer))]
    async fn read_with_timeout_static(
        stream: &mut TcpStream,
        buffer: &mut Vec<u8>,
        temp_buffer: &mut [u8],
        timeout: Duration,
    ) -> Result<Vec<u8>, SocketClientError> {
        loop {
            match time::timeout(timeout, stream.read(temp_buffer)).await {
                Ok(Ok(0)) => break, // 连接关闭
                Ok(Ok(n)) => {
                    buffer.extend_from_slice(&temp_buffer[..n]);
                    // 检查是否接收完成
                    if n < temp_buffer.len() {
                        break;
                    }
                }
                Ok(Err(e)) => {
                    error!("接收数据失败: {}", e);
                    return Err(SocketClientError::from(e));
                }
                Err(_) => {
                    error!("读取超时");
                    return Err(SocketClientError::ReceiveError("读取超时".to_string()));
                }
            }
        }

        // 避免不必要的克隆，直接返回buffer
        Ok(std::mem::take(buffer))
    }

    /// 只发送数据
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    pub async fn send(&self, data: &[u8]) -> Result<(), SocketClientError> {
        use crate::app::facades::Socket as SocketFacade;

        let data = data.to_vec(); // 克隆数据以避免生命周期问题

        // 获取连接池
        let pool = SocketFacade::get_pool().await;
        let mut conn = pool.get().await.map_err(|e| {
            error!("获取连接失败: {:?}", e);
            SocketClientError::ConnectionError(format!("获取连接失败: {:?}", e))
        })?;

        // 发送数据
        conn.write_all(&data).await.map_err(|e| {
            error!("发送数据失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        // 确保数据发送完成
        conn.flush().await.map_err(|e| {
            error!("刷新发送缓冲区失败: {}", e);
            SocketClientError::SendError(e.to_string())
        })?;

        Ok(())
    }

    /// 只发送数据的别名方法
    #[instrument(skip(self, data), fields(data_len = data.len()))]
    pub async fn send_only(&self, data: &[u8]) -> Result<(), SocketClientError> {
        self.send(data).await
    }
}

/// 使用示例
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_socket_client() {
        // 从环境配置创建客户端（使用连接池）
        let client = SocketClientService::from_env();

        // 发送数据并接收响应
        let data = b"Hello, Server!";
        match client.send_and_receive(data).await {
            Ok(response) => {
                println!("收到响应: {:?}", String::from_utf8_lossy(&response));
            }
            Err(e) => {
                println!("发送/接收失败: {:?}", e);
            }
        }

        // 连接池模式下无需手动关闭连接
    }

    #[tokio::test]
    async fn test_socket_client_batch() {
        // 测试批量发送功能
        let client = SocketClientService::from_env();

        let batch_data = vec![
            b"Message 1".as_slice(),
            b"Message 2".as_slice(),
            b"Message 3".as_slice(),
        ];

        match client.send_batch(&batch_data).await {
            Ok(responses) => {
                println!("批量发送成功，收到 {} 个响应", responses.len());
                for (i, response) in responses.iter().enumerate() {
                    println!("响应 {}: {:?}", i + 1, String::from_utf8_lossy(response));
                }
            }
            Err(e) => {
                println!("批量发送失败: {:?}", e);
            }
        }

        println!("批量发送测试完成");
    }

    #[tokio::test]
    async fn test_socket_client_send_only() {
        // 测试只发送功能
        let client = SocketClientService::from_env();

        let notification_data = b"Notification message";
        match client.send_only(notification_data).await {
            Ok(_) => {
                println!("通知发送成功");
            }
            Err(e) => {
                println!("通知发送失败: {:?}", e);
            }
        }

        println!("只发送测试完成");
    }
}
